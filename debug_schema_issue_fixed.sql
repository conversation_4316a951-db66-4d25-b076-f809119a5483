-- 스키마 오류 디버깅: Supabase SQL Editor 호환 버전

-- 1. public 스키마 테이블 목록 확인
SELECT 
    table_name, 
    table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND (table_name LIKE '%competitor%' OR table_name LIKE '%echotik%')
ORDER BY table_name;

-- 2. echotik_api_config 테이블 구조 확인
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'echotik_api_config'
ORDER BY ordinal_position;

-- 3. competitor-dashboard-daily 테이블 구조 확인
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'competitor-dashboard-daily'
ORDER BY ordinal_position;

-- 4. 테이블 존재 여부 확인
SELECT 
    'echotik_api_config' as table_name,
    EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'echotik_api_config'
    ) as table_exists
UNION ALL
SELECT 
    'competitor-dashboard-daily' as table_name,
    EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'competitor-dashboard-daily'
    ) as table_exists;

-- 5. echotik_api_config 실제 데이터 확인
SELECT 'echotik_api_config data:' as info;
SELECT * FROM public.echotik_api_config LIMIT 3;

-- 6. competitor-dashboard-daily 데이터 요약
SELECT 'competitor-dashboard-daily summary:' as info;
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT brand_name) as unique_brands,
    MIN(date) as earliest_date,
    MAX(date) as latest_date
FROM public."competitor-dashboard-daily";

-- 7. brand 스키마 존재 여부 확인
SELECT 
    'brand' as schema_name,
    EXISTS (
        SELECT 1 
        FROM information_schema.schemata 
        WHERE schema_name = 'brand'
    ) as schema_exists;

-- 8. brand 스키마 테이블 목록 (있다면)
SELECT 
    'brand schema tables:' as info,
    table_name, 
    table_type 
FROM information_schema.tables 
WHERE table_schema = 'brand'
ORDER BY table_name;

-- 9. 기존 함수 목록 확인
SELECT 
    'functions:' as info,
    routine_name,
    routine_schema,
    routine_type
FROM information_schema.routines 
WHERE routine_schema IN ('public', 'brand')
AND (routine_name LIKE '%competitor%' OR routine_name LIKE '%echotik%')
ORDER BY routine_schema, routine_name;

-- 10. 최근 데이터 샘플 (문제 없다면)
SELECT 'Recent data sample:' as info;
SELECT 
    date,
    brand_name,
    gmv,
    sales_count
FROM public."competitor-dashboard-daily"
ORDER BY date DESC, brand_name
LIMIT 6; 