-- <PERSON><PERSON> Job 로그 및 상태 확인 함수들

-- 1. <PERSON><PERSON> Job 로그 확인 함수
CREATE OR REPLACE FUNCTION check_cron_logs(job_name TEXT DEFAULT NULL)
RETURNS TABLE(
    jobid BIGINT,
    runid BIGINT,
    job_pid INTEGER,
    database TEXT,
    username TEXT,
    command TEXT,
    status TEXT,
    return_message TEXT,
    start_time TIMESTAMPTZ,
    end_time TIMESTAMPTZ
)
LANGUAGE SQL
AS $$
    SELECT 
        jobid,
        runid,
        job_pid,
        database,
        username,
        command,
        status,
        return_message,
        start_time,
        end_time
    FROM cron.job_run_details 
    WHERE (job_name IS NULL OR command LIKE '%' || job_name || '%')
    ORDER BY start_time DESC 
    LIMIT 20;
$$;

-- 2. 활성 Cron Job 목록 확인
CREATE OR REPLACE FUNCTION check_cron_jobs()
RETURNS TABLE(
    jobid BIGINT,
    schedule TEXT,
    command TEXT,
    nodename TEXT,
    nodeport INTEGER,
    database TEXT,
    username TEXT,
    active BOOLEAN,
    jobname TEXT
)
LANGUAGE SQL
AS $$
    SELECT 
        jobid,
        schedule,
        command,
        nodename,
        nodeport,
        database,
        username,
        active,
        jobname
    FROM cron.job
    ORDER BY jobid;
$$;

-- 3. 최근 Cron Job 실행 결과 요약
CREATE OR REPLACE FUNCTION cron_status_summary()
RETURNS TABLE(
    job_command TEXT,
    last_run TIMESTAMPTZ,
    status TEXT,
    return_message TEXT,
    total_runs BIGINT,
    success_runs BIGINT,
    failed_runs BIGINT
)
LANGUAGE SQL
AS $$
    SELECT 
        command as job_command,
        MAX(start_time) as last_run,
        (SELECT status FROM cron.job_run_details jrd2 
         WHERE jrd2.command = jrd.command 
         ORDER BY start_time DESC LIMIT 1) as status,
        (SELECT return_message FROM cron.job_run_details jrd3 
         WHERE jrd3.command = jrd.command 
         ORDER BY start_time DESC LIMIT 1) as return_message,
        COUNT(*) as total_runs,
        COUNT(CASE WHEN status = 'succeeded' THEN 1 END) as success_runs,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_runs
    FROM cron.job_run_details jrd
    GROUP BY command
    ORDER BY last_run DESC;
$$;

-- 4. 특정 Job의 상세 로그
CREATE OR REPLACE FUNCTION get_job_logs(job_command_pattern TEXT)
RETURNS TABLE(
    runid BIGINT,
    status TEXT,
    return_message TEXT,
    start_time TIMESTAMPTZ,
    end_time TIMESTAMPTZ,
    duration INTERVAL
)
LANGUAGE SQL
AS $$
    SELECT 
        runid,
        status,
        return_message,
        start_time,
        end_time,
        (end_time - start_time) as duration
    FROM cron.job_run_details 
    WHERE command LIKE '%' || job_command_pattern || '%'
    ORDER BY start_time DESC 
    LIMIT 10;
$$; 