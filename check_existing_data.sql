-- 기존 데이터 구조 및 내용 확인 스크립트

-- 1. public 스키마 테이블 목록 확인
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%competitor%' OR table_name LIKE '%echotik%';

-- 2. competitor-dashboard-daily 테이블 구조 확인
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'competitor-dashboard-daily'
ORDER BY ordinal_position;

-- 3. echotik_api_config 테이블 구조 확인
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'echotik_api_config'
ORDER BY ordinal_position;

-- 4. competitor-dashboard-daily 데이터 확인
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT brand_name) as unique_brands,
    MIN(date) as earliest_date,
    MAX(date) as latest_date
FROM public."competitor-dashboard-daily";

-- 5. 브랜드별 레코드 수
SELECT 
    brand_name,
    COUNT(*) as record_count,
    MIN(date) as first_date,
    MAX(date) as last_date
FROM public."competitor-dashboard-daily"
GROUP BY brand_name
ORDER BY record_count DESC;

-- 6. echotik_api_config 데이터 확인
SELECT * FROM public.echotik_api_config;

-- 7. 최근 데이터 샘플 확인
SELECT 
    date,
    brand_name,
    gmv,
    sales_count,
    video_count,
    live_count,
    product_count
FROM public."competitor-dashboard-daily"
ORDER BY date DESC, brand_name
LIMIT 10;

-- 8. brand 스키마 존재 여부 확인
SELECT schema_name 
FROM information_schema.schemata 
WHERE schema_name = 'brand';

-- 9. brand 스키마 테이블 목록 (있다면)
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'brand';

-- 10. 기존 함수 목록 확인
SELECT 
    routine_name,
    routine_schema,
    routine_type
FROM information_schema.routines 
WHERE routine_schema IN ('public', 'brand')
AND routine_name LIKE '%competitor%' OR routine_name LIKE '%echotik%'
ORDER BY routine_schema, routine_name; 