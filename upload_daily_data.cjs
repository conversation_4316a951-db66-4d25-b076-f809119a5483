const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

// Supabase 설정
const supabaseUrl = 'https://jqfijmjvfgzgghvjbwxc.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpxZmlqbWp2Zmd6Z2dodmpid3hjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDI1NjUyMDIsImV4cCI6MjAxODE0MTIwMn0.JItd7grxUM0vo_h-UWc6Z7KuGusTbFK0qQPEYQjrUaU'

const supabase = createClient(supabaseUrl, supabaseKey)

// 브랜드 이름과 셀러 ID 매핑
const brandMapping = {
  'skin1004': {
    displayName: 'Skin1004 US',
    sellerId: '7495275617887947202'
  },
  'medicube': {
    displayName: 'medicube US Store',
    sellerId: '7495514739648989419'
  },
  'cosrx': {
    displayName: 'COSRX US',
    sellerId: '7495173442985953451'
  },
  'drmelaxin': {
    displayName: 'Dr.Melaxin Global',
    sellerId: '7495830785034323995'
  },
  'anua': {
    displayName: 'Anua Store US',
    sellerId: '7495467833010457057'
  },
  'beautyofjoseon': {
    displayName: 'Beauty of Joseon US',
    sellerId: '7495838346099132849'
  }
}

// JSON 파일에서 일일 데이터 추출
function extractDailyDataFromFile(filename) {
  try {
    const content = fs.readFileSync(filename, 'utf8')
    const lines = content.trim().split('\n')
    let allData = []
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          const parsed = JSON.parse(line)
          if (parsed.data && Array.isArray(parsed.data)) {
            allData.push(...parsed.data)
          }
        } catch (e) {
          console.log(`Skipping invalid JSON line in ${filename}`)
        }
      }
    }
    
    return allData
  } catch (error) {
    console.error(`Error reading ${filename}:`, error.message)
    return []
  }
}

// 일일 데이터 수집 및 변환
function collectDailyData() {
  const dailyRecords = []
  
  Object.entries(brandMapping).forEach(([key, brandInfo]) => {
    const filename = `${key}_longterm_trend.json`
    console.log(`Processing ${filename}...`)
    
    const data = extractDailyDataFromFile(filename)
    console.log(`  Found ${data.length} data points for ${brandInfo.displayName}`)
    
    data.forEach(item => {
      dailyRecords.push({
        date: item.dt,
        brand_name: brandInfo.displayName,
        seller_id: brandInfo.sellerId,
        gmv: parseFloat(item.total_sale_gmv_1d_amt || 0),
        sales_count: parseInt(item.total_sale_1d_cnt || 0),
        video_count: parseInt(item.total_video_cnt || 0),
        live_count: parseInt(item.total_live_cnt || 0),
        product_count: parseInt(item.total_product_cnt || 0),
        total_gmv: parseFloat(item.total_sale_gmv_amt || 0),
        total_sales: parseInt(item.total_sale_cnt || 0)
      })
    })
  })
  
  // 날짜순 정렬
  dailyRecords.sort((a, b) => new Date(a.date) - new Date(b.date))
  
  return dailyRecords
}

// 새 테이블 생성
async function setupTable() {
  console.log('🔧 새로운 일일 데이터 테이블 생성 중...')
  
  // SQL 파일 실행은 수동으로 해야 할 수 있습니다
  // 여기서는 테이블 존재 여부만 확인 (brand 스키마)
  const { data, error } = await supabase
    .schema('brand')
    .from('competitor-dashboard-daily')
    .select('count(*)')
    .limit(1)
  
  if (error && error.code === 'PGRST116') {
    console.log('❌ 테이블이 존재하지 않습니다. create_daily_table.sql을 먼저 실행해주세요.')
    return false
  }
  
  console.log('✅ 테이블이 존재합니다.')
  return true
}

// 일일 데이터 업로드
async function uploadDailyData() {
  console.log('\n📊 일일 데이터 수집 중...')
  const dailyRecords = collectDailyData()
  
  console.log(`📈 총 ${dailyRecords.length}개의 일일 레코드 준비됨`)
  console.log(`📅 기간: ${dailyRecords[0]?.date} ~ ${dailyRecords[dailyRecords.length - 1]?.date}`)
  
  // 기존 데이터 삭제 (brand 스키마)
  console.log('\n🗑️ 기존 일일 데이터 삭제 중...')
  const { error: deleteError } = await supabase
    .schema('brand')
    .from('competitor-dashboard-daily')
    .delete()
    .neq('id', 0) // 모든 데이터 삭제
  
  if (deleteError) {
    console.error('❌ 기존 데이터 삭제 오류:', deleteError)
  } else {
    console.log('✅ 기존 데이터 삭제 완료')
  }
  
  // 배치로 업로드 (Supabase는 한 번에 1000개까지 가능)
  console.log('\n📤 일일 데이터 업로드 중...')
  const batchSize = 100
  let successCount = 0
  let errorCount = 0
  
  for (let i = 0; i < dailyRecords.length; i += batchSize) {
    const batch = dailyRecords.slice(i, i + batchSize)
    
    const { data, error } = await supabase
      .schema('brand')
      .from('competitor-dashboard-daily')
      .insert(batch)
    
    if (error) {
      console.error(`❌ 배치 ${Math.floor(i / batchSize) + 1} 업로드 오류:`, error)
      errorCount += batch.length
    } else {
      console.log(`✅ 배치 ${Math.floor(i / batchSize) + 1}/${Math.ceil(dailyRecords.length / batchSize)} 업로드 완료 (${batch.length}개)`)
      successCount += batch.length
    }
    
    // API 호출 제한 방지
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  console.log(`\n📊 업로드 완료: 성공 ${successCount}개, 실패 ${errorCount}개`)
  return { successCount, errorCount }
}

// 데이터 검증
async function verifyData() {
  console.log('\n🔍 업로드된 데이터 확인 중...')
  
  // 총 레코드 수 (brand 스키마)
  const { count, error: countError } = await supabase
    .schema('brand')
    .from('competitor-dashboard-daily')
    .select('*', { count: 'exact', head: true })
  
  if (countError) {
    console.error('❌ 레코드 수 조회 오류:', countError)
    return
  }
  
  console.log(`📈 총 일일 레코드: ${count}개`)
  
  // 브랜드별 레코드 수 (brand 스키마)
  const { data: brandData, error: brandError } = await supabase
    .schema('brand')
    .from('competitor-dashboard-daily')
    .select('brand_name')
  
  if (!brandError) {
    const brandCounts = {}
    brandData.forEach(row => {
      brandCounts[row.brand_name] = (brandCounts[row.brand_name] || 0) + 1
    })
    
    console.log('\n🏪 브랜드별 일일 레코드:')
    Object.entries(brandCounts).forEach(([brand, count]) => {
      console.log(`  - ${brand}: ${count}개`)
    })
  }
  
  // 날짜 범위 확인
  const { data: dateRange, error: dateError } = await supabase
    .from('competitor-dashboard-daily')
    .select('date')
    .order('date', { ascending: true })
    .limit(1)
  
  const { data: dateRangeEnd, error: dateEndError } = await supabase
    .from('competitor-dashboard-daily')
    .select('date')
    .order('date', { ascending: false })
    .limit(1)
  
  if (!dateError && !dateEndError && dateRange.length > 0 && dateRangeEnd.length > 0) {
    console.log(`\n📅 데이터 기간: ${dateRange[0].date} ~ ${dateRangeEnd[0].date}`)
  }
  
  // 샘플 데이터
  const { data: sampleData, error: sampleError } = await supabase
    .from('competitor-dashboard-daily')
    .select('*')
    .eq('brand_name', 'medicube US Store')
    .order('date', { ascending: false })
    .limit(3)
  
  if (!sampleError && sampleData.length > 0) {
    console.log('\n📋 샘플 데이터 (medicube 최근 3일):')
    sampleData.forEach(row => {
      console.log(`  ${row.date}: GMV $${row.gmv}, 판매 ${row.sales_count}개, 비디오 ${row.video_count}개`)
    })
  }
}

// 월별 집계 뷰 테스트
async function testMonthlyView() {
  console.log('\n📊 월별 집계 뷰 테스트...')
  
  const { data, error } = await supabase
    .from('competitor-dashboard-monthly')
    .select('*')
    .eq('brand_name', 'medicube US Store')
    .order('month', { ascending: false })
    .limit(3)
  
  if (error) {
    console.error('❌ 월별 뷰 조회 오류:', error)
  } else if (data.length > 0) {
    console.log('✅ 월별 집계 뷰 동작 확인:')
    data.forEach(row => {
      console.log(`  ${row.month_label}: GMV $${Math.round(row.total_gmv).toLocaleString()}, 판매 ${row.total_sales.toLocaleString()}개`)
    })
  }
}

// 메인 실행 함수
async function main() {
  try {
    console.log('🚀 일일 데이터 테이블 구조로 마이그레이션 시작\n')
    
    // 1. 테이블 설정 확인
    const tableReady = await setupTable()
    if (!tableReady) {
      console.log('\n❌ 테이블 설정을 완료한 후 다시 실행해주세요.')
      return
    }
    
    // 2. 일일 데이터 업로드
    const { successCount, errorCount } = await uploadDailyData()
    
    // 3. 데이터 검증
    await verifyData()
    
    // 4. 월별 뷰 테스트
    await testMonthlyView()
    
    console.log('\n✅ 일일 데이터 마이그레이션 완료!')
    console.log('📊 이제 일일 단위로 세밀한 트렌드 분석이 가능합니다.')
    console.log('📈 대시보드에서 일별/주별/월별 집계를 모두 지원할 수 있습니다.')
    
  } catch (error) {
    console.error('❌ 마이그레이션 중 오류 발생:', error)
  }
}

// 스크립트 실행
if (require.main === module) {
  main()
} 