const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Supabase 설정
const supabaseUrl = 'https://tmqhrmpzfgjwqdyiormw.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRtcWhybXB6Zmdqd3FkeWlvcm13Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNzExNDEsImV4cCI6MjA2NTc0NzE0MX0.zNCWt3PbLBqB3esC70HI3PbadAVMBKPBC_5AkxkDP3Q';

const supabase = createClient(supabaseUrl, supabaseKey);

// 브랜드 이름 매핑
const brandMapping = {
  'skin1004': 'Skin1004 US',
  'medicube': 'medicube US Store', 
  'cosrx': 'COSRX US',
  'drmelaxin': 'Dr.Melaxin Global',
  'anua': 'Anua Store US',
  'beautyofjoseon': 'Beauty of Joseon US'
};

// 월별 이름 매핑
const getMonthLabel = (monthStr) => {
  const monthNames = {
    '2024-12': 'Dec 2024',
    '2025-01': 'Jan 2025',
    '2025-02': 'Feb 2025', 
    '2025-03': 'Mar 2025',
    '2025-04': 'Apr 2025',
    '2025-05': 'May 2025',
    '2025-06': 'Jun 2025 (ing)'
  };
  return monthNames[monthStr] || monthStr;
};

// 장기 트렌드 파일에서 데이터 추출
function extractLongTermData(filename) {
  try {
    const content = fs.readFileSync(filename, 'utf8');
    const lines = content.trim().split('\n');
    let allData = [];
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          const parsed = JSON.parse(line);
          if (parsed.data && Array.isArray(parsed.data)) {
            allData.push(...parsed.data);
          }
        } catch (e) {
          // Skip invalid JSON lines
        }
      }
    }
    
    return allData;
  } catch (error) {
    console.error(`Error reading ${filename}:`, error.message);
    return [];
  }
}

// 월별 데이터 집계 함수
function createMonthlyData() {
  const monthlyData = {};
  
  // 모든 브랜드의 데이터에서 월별로 집계
  Object.entries(brandMapping).forEach(([key, displayName]) => {
    const filename = `${key}_longterm_trend.json`;
    console.log(`Processing ${filename}...`);
    
    const data = extractLongTermData(filename);
    console.log(`  Found ${data.length} data points for ${displayName}`);
    
    data.forEach(item => {
      const monthKey = item.dt.substring(0, 7); // YYYY-MM 형식
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {};
      }
      
      if (!monthlyData[monthKey][displayName]) {
        monthlyData[monthKey][displayName] = {
          totalSales: 0,
          totalGMV: 0,
          totalVideos: 0,
          totalLives: 0,
          avgProducts: 0,
          dataPoints: 0
        };
      }
      
      // 월별 데이터 누적
      monthlyData[monthKey][displayName].totalSales += item.total_sale_1d_cnt || 0;
      monthlyData[monthKey][displayName].totalGMV += item.total_sale_gmv_1d_amt || 0;
      monthlyData[monthKey][displayName].totalVideos += item.total_video_cnt || 0;
      monthlyData[monthKey][displayName].totalLives += item.total_live_cnt || 0;
      monthlyData[monthKey][displayName].avgProducts += item.total_product_cnt || 0;
      monthlyData[monthKey][displayName].dataPoints += 1;
    });
  });
  
  // 월별 평균 계산
  Object.keys(monthlyData).forEach(month => {
    Object.keys(monthlyData[month]).forEach(brand => {
      const data = monthlyData[month][brand];
      data.avgProducts = Math.round(data.avgProducts / data.dataPoints);
      data.avgVideos = Math.round(data.totalVideos / data.dataPoints);
      data.avgLives = Math.round(data.totalLives / data.dataPoints);
    });
  });
  
  return monthlyData;
}

// 테이블 존재 확인
async function checkTableExists() {
  try {
    const { data, error } = await supabase
      .from('competitor-dashboard')
      .select('id')
      .limit(1);
    
    if (error && error.code === '42P01') {
      return false; // 테이블이 존재하지 않음
    }
    
    return true; // 테이블 존재
  } catch (err) {
    return false;
  }
}

// 데이터를 Supabase에 업로드
async function uploadData() {
  console.log('\n🔍 테이블 존재 확인...');
  const tableExists = await checkTableExists();
  
  if (!tableExists) {
    console.log('❌ competitor-dashboard 테이블이 존재하지 않습니다.');
    console.log('\n📋 다음 SQL을 Supabase 대시보드에서 실행해주세요:');
    console.log('---');
    console.log(`CREATE TABLE "competitor-dashboard" (
  id SERIAL PRIMARY KEY,
  metric_type VARCHAR(50) NOT NULL,
  date_period VARCHAR(20) NOT NULL,
  month_key VARCHAR(10) NOT NULL,
  brand_name VARCHAR(100) NOT NULL,
  value BIGINT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`);
    console.log('---');
    console.log('\n🌐 Supabase 대시보드: https://supabase.com/dashboard/project/tmqhrmpzfgjwqdyiormw/sql');
    return;
  }
  
  console.log('✅ 테이블 존재 확인됨');
  
  console.log('\n월별 데이터 집계 중...');
  const monthlyData = createMonthlyData();
  
  console.log('\nSupabase에 데이터 업로드 중...');
  
  const sortedMonths = Object.keys(monthlyData).sort();
  const metrics = ['GMV', 'Item solds', 'Creators', 'Video', 'Live', 'Views'];
  
  // 기존 데이터 삭제
  console.log('기존 데이터 삭제 중...');
  const { error: deleteError } = await supabase
    .from('competitor-dashboard')
    .delete()
    .gte('id', 0); // 모든 데이터 삭제
  
  if (deleteError) {
    console.error('기존 데이터 삭제 실패:', deleteError);
  } else {
    console.log('✅ 기존 데이터 삭제 완료');
  }
  
  const uploadPromises = [];
  
  for (const metric of metrics) {
    for (const month of sortedMonths) {
      const monthLabel = getMonthLabel(month);
      
      for (const [key, brandName] of Object.entries(brandMapping)) {
        const data = monthlyData[month][brandName];
        let value = 0;
        
        switch (metric) {
          case 'GMV':
            value = data ? Math.round(data.totalGMV) : 0;
            break;
          case 'Item solds':
            value = data ? data.totalSales : 0;
            break;
          case 'Creators':
            value = data ? Math.round(data.avgVideos / 10) : 0;
            break;
          case 'Video':
            value = data ? data.avgVideos : 0;
            break;
          case 'Live':
            value = data ? data.avgLives : 0;
            break;
          case 'Views':
            value = data ? data.avgVideos * 1000 : 0;
            break;
        }
        
        uploadPromises.push({
          metric_type: metric,
          date_period: monthLabel,
          month_key: month,
          brand_name: brandName,
          value: value
        });
      }
    }
  }
  
  // 배치로 업로드
  const batchSize = 50; // 작은 배치 사이즈로 안정성 확보
  let successCount = 0;
  
  for (let i = 0; i < uploadPromises.length; i += batchSize) {
    const batch = uploadPromises.slice(i, i + batchSize);
    
    const { data, error } = await supabase
      .from('competitor-dashboard')
      .insert(batch);
    
    if (error) {
      console.error(`❌ Batch ${Math.floor(i / batchSize) + 1} 업로드 실패:`, error.message);
    } else {
      successCount += batch.length;
      console.log(`✅ Batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(uploadPromises.length / batchSize)} 업로드 완료 (${batch.length}개)`);
    }
  }
  
  console.log(`\n🎉 업로드 완료! 총 ${successCount}/${uploadPromises.length}개 레코드 업로드됨`);
  
  // 간단한 데이터 확인
  await verifyData();
}

// 업로드된 데이터 확인
async function verifyData() {
  console.log('\n📊 업로드된 데이터 확인...');
  
  // 샘플 데이터 확인
  const { data: sampleData, error: sampleError } = await supabase
    .from('competitor-dashboard')
    .select('*')
    .eq('metric_type', 'GMV')
    .eq('brand_name', 'medicube US Store')
    .order('month_key')
    .limit(3);
  
  if (sampleError) {
    console.error('❌ 데이터 확인 실패:', sampleError.message);
  } else if (sampleData && sampleData.length > 0) {
    console.log('✅ 샘플 데이터 (medicube GMV):');
    sampleData.forEach(row => {
      console.log(`  ${row.date_period}: $${row.value.toLocaleString()}`);
    });
  } else {
    console.log('❌ 업로드된 데이터가 없습니다.');
  }
}

// 메인 실행 함수
async function main() {
  try {
    console.log('🚀 Supabase competitor-dashboard 업로드 시작\n');
    
    await uploadData();
    
    console.log('\n✅ 프로세스 완료!');
    console.log('이제 competitor dashboard에서 이 데이터를 사용할 수 있습니다.');
    
  } catch (error) {
    console.error('❌ 업로드 중 오류 발생:', error);
  }
}

// 스크립트 실행
main(); 