-- 스키마 오류 디버깅: 단계별 확인

-- 1. echotik_api_config 테이블 구조 상세 확인
\d public.echotik_api_config

-- 2. 컬럼 정보 확인
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'echotik_api_config'
ORDER BY ordinal_position;

-- 3. 테이블 존재 여부 확인
SELECT EXISTS (
    SELECT 1 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'echotik_api_config'
) as table_exists;

-- 4. 실제 데이터 확인 (컬럼명 확인용)
SELECT * FROM public.echotik_api_config LIMIT 1;

-- 5. 테이블 정의 확인
SELECT 
    table_name,
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'echotik_api_config';

-- 6. 다른 방법으로 데이터 조회 시도
SELECT 
    t.api_username,
    t.api_password,
    t.created_at
FROM public.echotik_api_config t;

-- 7. 모든 컬럼 조회
SELECT * FROM public.echotik_api_config; 