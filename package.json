{"name": "allsale_brand", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.2.0", "@next/eslint-plugin-next": "^15.0.3", "@next/third-parties": "^15.2.3", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.50.0", "@types/chrome": "^0.0.304", "apexcharts": "^4.5.0", "axios": "1.7.9", "countries-and-timezones": "^3.8.0", "crypto-js": "^4.2.0", "file-saver": "^2.0.5", "g": "^2.0.1", "next": "^15.2.3", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.1", "react-phone-number-input": "^3.4.11", "react-virtualized-auto-sizer": "^1.0.25", "recharts": "^3.0.0", "tailwindcss": "^4.0.8", "xlsx": "^0.18.5", "zustand": "^5.0.3"}, "devDependencies": {"@styled-jsx/plugin-sass": "^4.1.1", "@tokens-studio/sd-transforms": "^1.2.9", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "^15.1.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "prettier": "^3.5.0", "sd-tailwindcss-transformer": "^2.1.0", "style-dictionary": "^4.3.3", "typescript": "^5"}}