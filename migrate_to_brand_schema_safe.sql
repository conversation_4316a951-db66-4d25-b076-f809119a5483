-- Supabase Schema Migration: public → brand (안전한 버전)
-- 컬럼명 참조 문제 완전 해결

-- 1. brand 스키마에 테이블 생성
CREATE TABLE IF NOT EXISTS brand."competitor-dashboard-daily" (
    id BIGSERIAL PRIMARY KEY,
    date DATE NOT NULL,
    brand_name TEXT NOT NULL,
    seller_id TEXT NOT NULL,
    gmv DECIMAL(15,2) DEFAULT 0,
    sales_count INTEGER DEFAULT 0,
    video_count INTEGER DEFAULT 0,
    live_count INTEGER DEFAULT 0,
    product_count INTEGER DEFAULT 0,
    total_gmv DECIMAL(15,2) DEFAULT 0,
    total_sales INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(date, brand_name)
);

-- 2. brand 스키마에 API 설정 테이블 생성
CREATE TABLE IF NOT EXISTS brand.echotik_api_config (
    id SERIAL PRIMARY KEY,
    api_username TEXT NOT NULL,
    api_password TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 3. 기존 데이터를 brand 스키마로 복사
INSERT INTO brand."competitor-dashboard-daily" (
    date, brand_name, seller_id, gmv, sales_count, video_count, 
    live_count, product_count, total_gmv, total_sales, created_at, updated_at
)
SELECT 
    date, brand_name, seller_id, gmv, sales_count, video_count,
    live_count, product_count, total_gmv, total_sales, created_at, updated_at
FROM public."competitor-dashboard-daily"
ON CONFLICT (date, brand_name) DO NOTHING;

-- 4. API 설정 데이터 복사 (컬럼명 문제 해결)
DO $$
DECLARE
    rec RECORD;
BEGIN
    -- 기존 데이터를 하나씩 처리
    FOR rec IN SELECT * FROM public.echotik_api_config LOOP
        INSERT INTO brand.echotik_api_config (api_username, api_password, created_at)
        VALUES (rec.api_username, rec.api_password, rec.created_at)
        ON CONFLICT DO NOTHING;
    END LOOP;
END $$;

-- 5. brand 스키마에 함수들 재생성

-- 5-1. API 호출 함수 (완전히 새로 작성)
CREATE OR REPLACE FUNCTION brand.call_echotik_seller_trend_api(
    p_seller_id TEXT,
    p_date TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    api_url TEXT;
    username_val TEXT;
    password_val TEXT;
    auth_header TEXT;
    request_body JSONB;
    api_response JSONB;
    target_date TEXT;
    config_record RECORD;
BEGIN
    -- API 인증 정보 가져오기 (RECORD 사용으로 컬럼명 문제 해결)
    SELECT * INTO config_record
    FROM brand.echotik_api_config
    ORDER BY id DESC
    LIMIT 1;
    
    IF config_record IS NULL THEN
        RAISE EXCEPTION 'EchoTik API credentials not found in brand.echotik_api_config table';
    END IF;
    
    username_val := config_record.api_username;
    password_val := config_record.api_password;
    
    IF username_val IS NULL OR password_val IS NULL THEN
        RAISE EXCEPTION 'EchoTik API credentials are null';
    END IF;
    
    -- Basic Auth 헤더 생성
    auth_header := 'Basic ' || encode(username_val || ':' || password_val, 'base64');
    
    -- 날짜 설정 (파라미터가 없으면 어제 날짜 사용)
    target_date := COALESCE(p_date, (CURRENT_DATE - INTERVAL '1 day')::TEXT);
    
    -- API URL 구성
    api_url := 'https://api.echotik.com/v1/seller/trend';
    
    -- 요청 본문 구성
    request_body := jsonb_build_object(
        'seller_id', p_seller_id,
        'date', target_date
    );
    
    RAISE NOTICE '🚀 EchoTik API 호출: seller_id=%, date=%', p_seller_id, target_date;
    
    -- HTTP 요청 실행
    SELECT content::JSONB INTO api_response
    FROM http((
        'POST',
        api_url,
        ARRAY[
            http_header('Content-Type', 'application/json'),
            http_header('Authorization', auth_header)
        ],
        'application/json',
        request_body::TEXT
    )::http_request);
    
    RAISE NOTICE '✅ API 응답 수신: %', api_response;
    
    RETURN api_response;
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE '❌ API 호출 실패: %', SQLERRM;
    RETURN jsonb_build_object('error', SQLERRM);
END;
$$;

-- 5-2. 메인 데이터 수집 함수
CREATE OR REPLACE FUNCTION brand.fetch_daily_competitor_data_v2()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    brand_info RECORD;
    api_response JSONB;
    daily_data JSONB;
    item JSONB;
    current_date_str TEXT;
    success_count INTEGER := 0;
    error_count INTEGER := 0;
BEGIN
    -- 현재 날짜 (어제 데이터를 가져옴)
    current_date_str := (CURRENT_DATE - INTERVAL '1 day')::TEXT;
    
    RAISE NOTICE '🚀 Daily competitor data fetch started for date: %', current_date_str;
    
    -- 브랜드별 데이터 수집
    FOR brand_info IN 
        SELECT 
            'Skin1004 US' as brand_name, 
            '7495275617887947202' as seller_id
        UNION ALL SELECT 
            'medicube US Store', 
            '7495514739648989419'
        UNION ALL SELECT 
            'COSRX US', 
            '7495173442985953451'
        UNION ALL SELECT 
            'Dr.Melaxin Global', 
            '7495830785034323995'
        UNION ALL SELECT 
            'Anua Store US', 
            '7495467833010457057'
        UNION ALL SELECT 
            'Beauty of Joseon US', 
            '7495838346099132849'
    LOOP
        BEGIN
            RAISE NOTICE '📊 Processing brand: % (seller_id: %)', brand_info.brand_name, brand_info.seller_id;
            
            -- EchoTik API 호출
            api_response := brand.call_echotik_seller_trend_api(brand_info.seller_id, current_date_str);
            
            -- 응답 확인
            IF api_response ? 'error' THEN
                RAISE EXCEPTION 'API Error: %', api_response->>'error';
            END IF;
            
            -- 데이터 추출
            daily_data := api_response->'data';
            
            IF daily_data IS NULL OR jsonb_array_length(daily_data) = 0 THEN
                RAISE EXCEPTION 'No data returned from API';
            END IF;
            
            -- 첫 번째 데이터 항목 처리
            item := daily_data->0;
            
            -- 기존 데이터가 있는지 확인 후 삽입/업데이트
            INSERT INTO brand."competitor-dashboard-daily" (
                date,
                brand_name,
                seller_id,
                gmv,
                sales_count,
                video_count,
                live_count,
                product_count,
                total_gmv,
                total_sales,
                created_at,
                updated_at
            ) VALUES (
                current_date_str::DATE,
                brand_info.brand_name,
                brand_info.seller_id,
                COALESCE((item->>'total_sale_gmv_1d_amt')::DECIMAL, 0),
                COALESCE((item->>'total_sale_1d_cnt')::INTEGER, 0),
                COALESCE((item->>'total_video_cnt')::INTEGER, 0),
                COALESCE((item->>'total_live_cnt')::INTEGER, 0),
                COALESCE((item->>'total_product_cnt')::INTEGER, 0),
                COALESCE((item->>'total_sale_gmv_1d_amt')::DECIMAL, 0),
                COALESCE((item->>'total_sale_1d_cnt')::INTEGER, 0),
                NOW(),
                NOW()
            )
            ON CONFLICT (date, brand_name) 
            DO UPDATE SET
                gmv = EXCLUDED.gmv,
                sales_count = EXCLUDED.sales_count,
                video_count = EXCLUDED.video_count,
                live_count = EXCLUDED.live_count,
                product_count = EXCLUDED.product_count,
                total_gmv = EXCLUDED.total_gmv,
                total_sales = EXCLUDED.total_sales,
                updated_at = NOW();
            
            success_count := success_count + 1;
            RAISE NOTICE '✅ Successfully processed %: GMV=%, Sales=%', 
                brand_info.brand_name, 
                COALESCE((item->>'total_sale_gmv_1d_amt')::DECIMAL, 0),
                COALESCE((item->>'total_sale_1d_cnt')::INTEGER, 0);
            
        EXCEPTION WHEN OTHERS THEN
            error_count := error_count + 1;
            RAISE NOTICE '❌ Error processing %: %', brand_info.brand_name, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE '🎉 Daily competitor data fetch completed: % success, % errors', success_count, error_count;
    
    RETURN format('✅ Completed: %s success, %s errors', success_count, error_count);
END;
$$;

-- 5-3. 테스트 함수
CREATE OR REPLACE FUNCTION brand.test_daily_fetch_v2()
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
    PERFORM brand.fetch_daily_competitor_data_v2();
    RETURN '✅ Test completed. Check logs and data for details.';
END;
$$;

-- 6. 권한 설정
GRANT USAGE ON SCHEMA brand TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA brand TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA brand TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA brand TO postgres, anon, authenticated, service_role;

-- 7. 마이그레이션 완료 후 데이터 확인
SELECT 'Migration completed. Data verification:' as status;
SELECT 
    'brand schema' as schema_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT brand_name) as unique_brands,
    MIN(date) as earliest_date,
    MAX(date) as latest_date
FROM brand."competitor-dashboard-daily";

-- 8. 브랜드별 레코드 수 확인
SELECT 
    brand_name,
    COUNT(*) as record_count,
    MIN(date) as first_date,
    MAX(date) as last_date
FROM brand."competitor-dashboard-daily"
GROUP BY brand_name
ORDER BY record_count DESC;

-- 9. API 설정 확인
SELECT 'API config verification:' as status;
SELECT api_username, created_at FROM brand.echotik_api_config; 