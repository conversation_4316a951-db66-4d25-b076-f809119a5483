import type { NextConfig } from "next"

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    unoptimized: true,
  },
  reactStrictMode: false,
  async rewrites() {
    // 더미 모드이거나 API URL이 설정되지 않은 경우 빈 배열 반환
    const apiUrl = process.env.NEXT_PUBLIC_NEST_API_URL
    if (!apiUrl || apiUrl.includes('undefined') || apiUrl === 'http://localhost:3001') {
      console.log('🎭 더미 모드: API rewrites 건너뛰기')
      return []
    }

    return [
      {
        source: "/api/:path*",
        destination: `${apiUrl}/api/:path*`,
      },
      {
        source: "/brand/:path*",
        destination: `${apiUrl}/brand/:path*`,
      },
    ]
  },
  env: {
    PROJECT_ENV: process.env.PROJECT_ENV,
    TIKTOK_CLIENT_KEY: process.env.TIKTOK_CLIENT_KEY,
    TIKTOK_SECRET_KEY: process.env.TIKTOK_SECRET_KEY,
  },
}

module.exports = nextConfig
