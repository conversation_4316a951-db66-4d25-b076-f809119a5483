"use client"

import React, { useEffect, useState } from "react"
import { supabaseClient } from "@/utils/supabase/client"
import { useRouter, useSearchParams } from "next/navigation"
import useVerifyEmail from "@/store/useVerifyEmail"
import <PERSON>ton from "@/components/elements/Button"
import Input from "@/components/elements/Input"
import { colors, fonts, schemes } from "@/utils/theme/style"
import OnboardingFrame from "@/components/onboarding/Frame"
import { getAssetPath } from "@/utils/util"

export default function SignInPages() {
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const { setVerifyEmail, resetVerifyEmail } = useVerifyEmail()
  const [isLoading, setIsLoading] = useState(false)
  const [disabled, setDisabled] = useState<boolean>(true)
  const [emailHelperText, setEmailHelperText] = useState<string | undefined>()
  const [passwordHelperText, setPasswordHelperText] = useState<
    string | undefined
  >()

  const searchParams = useSearchParams()
  const redirectUrl = searchParams.get("redirect_uri") ?? null

  useEffect(() => {
    if (email && password) {
      setDisabled(false)
    } else {
      setDisabled(true)
    }
  }, [email, password])

  const signInWithGoogle = async () => {
    await supabaseClient.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo: `${location.origin}/auth/callback?next=${redirectUrl ?? ""}`,
        queryParams: {
          access_type: "offline",
          prompt: "consent",
        },
      },
    })
  }

  function isEmpty(param: any) {
    return Object.keys(param).length === 0
  }

  const helperParse = (text: string | undefined) => {
    if (text) {
      return { text: text }
    }
  }

  const handleAuthentication = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (isLoading) return

    try {
      // If user is null and no specific error about password, attempt to sign up
      setIsLoading(true)
      console.log("Attempting to sign up...")
      const { data: signUpData } = await supabaseClient.auth.signUp({
        email: email,
        password: password,
        options: {
          emailRedirectTo: `${location.origin}/auth/callback`,
        },
      })

      if (isEmpty(signUpData.user?.user_metadata)) {
        setEmailHelperText(
          "This email is already registered. Please sign in instead."
        )
        setIsLoading(false)
        return
      }

      if (signUpData.user) {
        // notifySuccess("Please check your email to finish your Sign Up")
        setVerifyEmail(email)
        router.push("/resend-verify")
        setIsLoading(false)
        return
      }
    } catch (error) {
      setIsLoading(false)
      console.error("Unexpected error:", error)
      setPasswordHelperText("An error occurred. Please try again.")
    }
  }

  useEffect(() => {
    resetVerifyEmail()
  }, [])

  return (
    <OnboardingFrame>
      <div className={"base"}>
        <div>
          <h2>Let’s get started</h2>
          <p>Powered by AI, One-Click TikTok Collabs.</p>
        </div>
        <div className={"google-sign"}>
          <Button full={true} onClick={signInWithGoogle}>
            <i />
            <span>Continue with Google</span>
          </Button>
        </div>
        <div className={"bar"}>
          <div></div>
        </div>
        <div className={"form"}>
          <form action="" onSubmit={handleAuthentication}>
            <div>
              <Input
                type={"email"}
                value={email}
                placeholderText={"<EMAIL>"}
                status={emailHelperText ? "error" : "default"}
                isRequired={true}
                onChange={setEmail}
                label={{ text: "Work email" }}
                helper={helperParse(emailHelperText)}
              />
            </div>
            <div>
              <Input
                type={"password"}
                value={password}
                placeholderText={"Password"}
                status={passwordHelperText ? "error" : "default"}
                isRequired={true}
                onChange={setPassword}
                label={{ text: "Password" }}
                helper={helperParse(passwordHelperText)}
              />
            </div>
            <div>
              <Button
                type="submit"
                full={true}
                isLoading={isLoading}
                disabled={isLoading || disabled}
              >
                Sign up
              </Button>
            </div>
          </form>
          <div>
            <span>Have an account?</span>
            <Button
              style={"text"}
              size={"xs"}
              onClick={() => router.push("/signin")}
            >
              Sign in
            </Button>
          </div>
        </div>
      </div>
      <style jsx>{`
        div.base {
          position: relative;
          max-width: 25rem;
          margin: 0 auto;
          top: 50%;
          transform: translateY(-50%);
        }
        h2 {
          font-size: ${fonts.heading.h2.fontSize};
          font-weight: ${fonts.heading.h2.fontWeight};
          line-height: ${fonts.heading.h2.lineHeight};
          margin-bottom: ${schemes.spacing["2xl"]};
          & + p {
            color: ${colors.schems.dark.onDisabled};
            font-size: ${fonts.body.large.fontSize};
            font-weight: ${fonts.body.large.fontWeight};
            line-height: ${fonts.body.large.lineHeight};
            margin-bottom: ${schemes.spacing["8xl"]};
          }
        }
        .bar {
          padding: ${schemes.spacing["3xl"]} 0;
          & > div {
            border-top: 1px solid ${colors.border.dark.default};
          }
        }
        .form > form {
          display: flex;
          flex-direction: column;
          & > div:nth-of-type(2) {
            margin-top: ${schemes.spacing["2xl"]};
            margin-bottom: ${schemes.spacing["3xl"]};
            & > div {
              position: relative;
              width: 100%;
              & > div:nth-of-type(2) {
                position: absolute;
                top: 0;
                right: 0;
                & > button {
                  background: none;
                  border: none;
                  padding: 0;
                  color: ${colors.schems.dark.onDisabled};
                  font-size: ${fonts.body.label.fontSize};
                  font-weight: ${fonts.body.label.fontWeight};
                  line-height: ${fonts.body.label.lineHeight};
                }
              }
            }
          }
          & + div {
            height: 2.5rem;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: ${schemes.spacing.sm};
            margin-top: ${schemes.spacing["2xl"]};
            span > span {
              color: ${colors.schems.dark.onDisabled};
              font-size: ${fonts.body.large.fontSize};
              font-weight: ${fonts.body.large.fontWeight};
              line-height: ${fonts.body.large.lineHeight};
            }
          }
        }
        .google-sign {
          button {
            background: ${colors.schems.dark.onBase} !important;
            span {
              display: flex;
              justify-content: center;
              align-items: center;
              i {
                display: inline-block;
                width: 1rem;
                height: 1rem;
                background: url("${getAssetPath("google.svg")}") center/cover
                  no-repeat;
              }
              span {
                color: ${colors.schems.dark.base} !important;
              }
            }
          }
        }
      `}</style>
    </OnboardingFrame>
  )
}
