"use client"

import React, { useCallback, useEffect } from "react"
import { supabaseClient } from "@/utils/supabase/client"
import { useRouter } from "next/navigation"
import { toast } from "react-hot-toast"
import useVerifyEmail from "@/store/useVerifyEmail"
import OnboardingFrame from "@/components/onboarding/Frame"
import Input from "@/components/elements/Input"
import Button from "@/components/elements/Button"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { notifyError, notifySuccess } from "@/components/modal/Toasts"

export default function SignInPages() {
  const router = useRouter()
  const { verifyEmail } = useVerifyEmail()

  const resendVerifyEmail = useCallback(async () => {
    const { error } = await supabaseClient.auth.resend({
      type: "signup",
      email: verifyEmail!,
      options: {
        emailRedirectTo: `${location.origin}/auth/callback`,
      },
    })

    if (!error) {
      notifySuc<PERSON>("Successful verification resend. Please check your email")
    } else {
      notifyError(error.message)
    }
  }, [verifyEmail])

  useEffect(() => {
    if (!verifyEmail) {
      router.replace("/signin")
    }
  }, [router, verifyEmail])

  return (
    <OnboardingFrame>
      <div className={"base"}>
        <div>
          <h2>Verify your email</h2>
          <p>To complete your signup, please verify your email.</p>
        </div>
        <div>
          <div>
            <Input
              label={{ text: "Work email" }}
              helper={{ text: "Check your inbox and spam folder" }}
              value={verifyEmail!}
              isRightBtn={false}
              disabled={true}
            />
          </div>
          <div>
            <span>Didn&#39;t get the email?</span>
            <Button style={"text"} size={"xs"} onClick={resendVerifyEmail}>
              Resend
            </Button>
          </div>
        </div>
      </div>
      <style jsx>{`
        div.base {
          position: relative;
          max-width: 25rem;
          margin: 0 auto;
          top: 50%;
          transform: translateY(-50%);
          > div:first-of-type {
            margin-bottom: ${schemes.spacing["8xl"]};
            > h2 {
              font-size: ${fonts.heading.h2.fontSize};
              font-weight: ${fonts.heading.h2.fontWeight};
              line-height: ${fonts.heading.h2.lineHeight};
              margin-bottom: ${schemes.spacing["2xl"]};
              & + p {
                color: ${colors.schems.dark.onDisabled};
                font-size: ${fonts.body.large.fontSize};
                font-weight: ${fonts.body.large.fontWeight};
                line-height: ${fonts.body.large.lineHeight};
                margin-bottom: ${schemes.spacing["8xl"]};
              }
            }
          }
          > div:last-of-type {
            > div:last-of-type {
              margin-top: ${schemes.spacing.md};
              display: flex;
              justify-content: start;
              align-items: center;
              gap: ${schemes.spacing.sm};
              > span {
                color: ${colors.schems.dark.onDisabled};
                font-size: ${fonts.body.large.fontSize};
                font-weight: ${fonts.body.large.fontWeight};
                line-height: ${fonts.body.large.lineHeight};
              }
            }
          }
        }
      `}</style>
    </OnboardingFrame>
  )
}
