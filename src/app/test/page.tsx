"use client"

import Input from "@/components/elements/Input"
import Button from "@/components/elements/Button"
import Icon from "@/components/elements/Icon"
import IconButton from "@/components/elements/IconButton"
import { colors } from "@/utils/theme/style"
import LabelButton from "@/components/elements/LabelButton"
import LabelTag from "@/components/elements/LabelTag"
import { useState } from "react"
import RangeBar from "@/components/elements/RangeBar"
import Checkbox from "@/components/elements/Checkbox"
import { getAssetPath } from "@/utils/util"
import SelectBox from "@/components/elements/SelectBox"
import BaseModal from "@/components/modal/BaseModal"
import { notifyError, notifySuccess } from "@/components/modal/Toasts"
import SaveTags from "@/components/table/SaveTags"
import Loading from "@/components/common/Loading"
import Toggle from "@/components/elements/Toggle"

const TestPage = () => {
  const min = 0
  const max = 100000
  const [minVal, setMinVal] = useState(min)
  const [maxVal, setMaxVal] = useState(max)
  const [isOpen, setIsOpen] = useState(false)
  const [checked, setChecked] = useState(false)
  const [checked2, setChecked2] = useState(false)
  const [checked3, setChecked3] = useState(true)

  return (
    <>
      <div className={"content"}>
        <div>
          <Toggle
            checked={checked}
            onChange={(e) => setChecked(e.target.checked)}
            id={"toggle-1"}
          />{" "}
          <Toggle
            checked={checked2}
            onChange={(e) => setChecked2(e.target.checked)}
            id={"toggle-2"}
          />{" "}
          <Toggle
            checked={checked3}
            onChange={(e) => setChecked3(e.target.checked)}
            id={"toggle-3"}
            disabled={true}
          />
        </div>
        <div>
          <Input type={"text"} />
        </div>
        <div>
          <Button full={true}>Button</Button>
        </div>
        <div>
          <Button full={true} isLoading={true} disabled={true}>
            Button
          </Button>
        </div>
        <div>
          <Button size={"sm"} style={"secondary"}>
            Button
          </Button>
        </div>
        <div>
          <Button size={"xs"} style={"text"}>
            Button
          </Button>
        </div>
        <div>
          <Icon
            path={getAssetPath("setting_true.svg")}
            color={colors.schems.dark.onBase}
          />
        </div>
        <div>
          <IconButton
            path={getAssetPath("setting_true.svg")}
            label={"button"}
          />
        </div>
        <div>
          <ul>
            <li>
              <LabelButton size={"sm"}>0</LabelButton>
            </li>
            <li>
              <LabelButton>Label</LabelButton>
            </li>
            <li>
              <LabelButton color={"red"}>Label</LabelButton>
            </li>
            <li>
              <LabelButton color={"orange"}>Label</LabelButton>
            </li>
            <li>
              <LabelButton color={"yellow"}>Label</LabelButton>
            </li>
            <li>
              <LabelButton color={"green"}>Label</LabelButton>
            </li>
            <li>
              <LabelButton color={"cyan"}>Label</LabelButton>
            </li>
            <li>
              <LabelButton color={"blue"}>Label</LabelButton>
            </li>
            <li>
              <LabelButton color={"indigo"}>Label</LabelButton>
            </li>
            <li>
              <LabelButton color={"purple"}>Label</LabelButton>
            </li>
            <li>
              <LabelButton color={"pink"}>Label</LabelButton>
            </li>
          </ul>
        </div>
        <div>
          <ul>
            <li>
              <LabelTag>Label</LabelTag>
            </li>
            <li>
              <LabelTag color={"red"}>Label</LabelTag>
            </li>
            <li>
              <LabelTag color={"orange"}>Label</LabelTag>
            </li>
            <li>
              <LabelTag color={"yellow"}>Label</LabelTag>
            </li>
            <li>
              <LabelTag color={"green"}>Label</LabelTag>
            </li>
            <li>
              <LabelTag color={"cyan"}>Label</LabelTag>
            </li>
            <li>
              <LabelTag color={"blue"}>Label</LabelTag>
            </li>
            <li>
              <LabelTag color={"indigo"}>Label</LabelTag>
            </li>
            <li>
              <LabelTag color={"purple"}>Label</LabelTag>
            </li>
            <li>
              <LabelTag color={"pink"}>Label</LabelTag>
            </li>
          </ul>
        </div>
        <div>
          <ul>
            <li>
              <LabelTag type={"status"}>Label</LabelTag>
            </li>
            <li>
              <LabelTag type={"status"} color={"red"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"status"} color={"orange"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"status"} color={"yellow"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"status"} color={"green"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"status"} color={"cyan"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"status"} color={"blue"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"status"} color={"indigo"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"status"} color={"purple"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"status"} color={"pink"}>
                Label
              </LabelTag>
            </li>
          </ul>
        </div>
        <div>
          <ul>
            <li>
              <LabelTag lightness={"darken"} type={"status"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag lightness={"darken"} type={"status"} color={"red"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag lightness={"darken"} type={"status"} color={"orange"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag lightness={"darken"} type={"status"} color={"yellow"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag lightness={"darken"} type={"status"} color={"green"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag lightness={"darken"} type={"status"} color={"cyan"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag lightness={"darken"} type={"status"} color={"blue"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag lightness={"darken"} type={"status"} color={"indigo"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag lightness={"darken"} type={"status"} color={"purple"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag lightness={"darken"} type={"status"} color={"pink"}>
                Label
              </LabelTag>
            </li>
          </ul>
        </div>
        <div>
          <ul>
            <li>
              <LabelTag type={"folder"}>Label</LabelTag>
            </li>
            <li>
              <LabelTag type={"folder"} color={"red"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"folder"} color={"orange"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"folder"} color={"yellow"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"folder"} color={"green"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"folder"} color={"cyan"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"folder"} color={"blue"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"folder"} color={"indigo"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"folder"} color={"purple"}>
                Label
              </LabelTag>
            </li>
            <li>
              <LabelTag type={"folder"} color={"pink"}>
                Label
              </LabelTag>
            </li>
          </ul>
        </div>
        <div>
          <RangeBar
            min={min}
            max={max}
            step={1000}
            minVal={minVal}
            setMinVal={setMinVal}
            maxVal={maxVal}
            setMaxVal={setMaxVal}
          />
        </div>
        <div>
          <ul>
            <li>
              <Checkbox>Label</Checkbox>
            </li>
            <li>
              <Checkbox color={"red"} textColor={colors.sys.dark.red.default}>
                Label
              </Checkbox>
            </li>
            <li>
              <Checkbox color={"orange"}>Label</Checkbox>
            </li>
            <li>
              <Checkbox color={"yellow"}>Label</Checkbox>
            </li>
            <li>
              <Checkbox color={"green"}>Label</Checkbox>
            </li>
            <li>
              <Checkbox color={"cyan"}>Label</Checkbox>
            </li>
            <li>
              <Checkbox color={"blue"}>Label</Checkbox>
            </li>
            <li>
              <Checkbox color={"purple"}>Label</Checkbox>
            </li>
            <li>
              <Checkbox color={"pink"}>Label</Checkbox>
            </li>
            <li>
              <Checkbox color={"pink"} disabled={true}>
                Label
              </Checkbox>
            </li>
          </ul>
        </div>
        <div>
          <ul>
            <li>
              <Checkbox isBlock={true}>Label</Checkbox>
            </li>
            <li>
              <Checkbox isBlock={true} color={"red"}>
                Label
              </Checkbox>
            </li>
            <li>
              <Checkbox isBlock={true} disabled={true}>
                Label
              </Checkbox>
            </li>
          </ul>
        </div>
        <div>
          <SelectBox data={[25, 50, 100]} />
        </div>
        <div>
          <SelectBox data={[25, 50, 100]} display={"up"} />
        </div>
        <div>
          <SelectBox data={[25, 50, 100]} isFull={true} />
        </div>
        <div>
          <Button onClick={() => setIsOpen(true)}>modal open</Button>
          <BaseModal isOpen={isOpen} setIsOpen={setIsOpen}>
            test
          </BaseModal>
        </div>
        <div>
          <ul>
            <li>
              <Button onClick={() => notifySuccess("toast success")}>
                toast success
              </Button>
            </li>
            <li>
              <Button onClick={() => notifyError("toast error")}>
                toast error
              </Button>
            </li>
          </ul>
        </div>
        <div style={{ width: "10rem" }}>
          <SaveTags
            tagIds={[]}
            setTagIds={function (val: number[]): void {
              throw new Error("Function not implemented.")
            }}
          />
        </div>
        <div
          style={{
            width: "30rem",
            height: "30rem",
            border: "1px solid purple",
          }}
        >
          {/*<NotFound />*/}
          <Loading />
        </div>
      </div>
      <style jsx>{`
        .content {
          width: 50%;
          border: 2px solid ${colors.sys.dark.gray.darken};
          border-radius: 5px;
          margin: 2rem;
          padding: 2rem;
          display: flex;
          flex-direction: column;
          gap: 1rem;
          & ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            gap: 1rem;
          }
        }
      `}</style>
    </>
  )
}
export default TestPage
