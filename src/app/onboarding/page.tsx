"use client"

import React, { useEffect, useState } from "react"
import { Profile } from "@/components/onboarding/Profile"
import { Business } from "@/components/onboarding/Business"
import { createUser } from "@/service/user"
import { useRouter } from "next/navigation"
import OnboardingFrame from "@/components/onboarding/Frame"
import { useAuth } from "@/common/hooks/useAuthProvider"
import { notifyError, notifySuccess } from "@/components/modal/Toasts"
import useUserInfo from "@/store/useUserInfo"
import { WorkSpaceTagType, WorkspaceType } from "@/common/types/WorkspaceType"
import useWorkspace from "@/store/useWorkspace"

export default function OnboardingPages() {
  const router = useRouter()
  const auth = useAuth()
  const { userInfo, setUserInfo } = useUserInfo()
  const { setTags, setWorkspace } = useWorkspace()

  const [step, setStep] = useState<number>(0)
  const [fullName, setFullName] = useState<string>("")
  const [jobTitle, setJobTitle] = useState<string>("")
  const [phoneNumber, setPhoneNumber] = useState<string>("")
  const [companyName, setCompanyName] = useState<string>("")
  const [industry, setIndustry] = useState<string>("Beauty & Personal Care")
  const [companySize, setCompanySize] = useState<string>("21-50")
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (userInfo) {
      router.replace("/explore")
    }
  }, [])

  const onboardingSubmit = async () => {
    if (
      fullName.length > 0 &&
      jobTitle.length > 0 &&
      phoneNumber.length > 0 &&
      companyName.length > 0 &&
      industry.length > 0 &&
      companySize.length > 0 &&
      auth.session?.access_token &&
      auth.user?.id
    ) {
      setIsLoading(true)
      await createUser({
        userId: auth.user?.id,
        data: {
          fullName,
          jobTitle,
          phoneNumber,
          companyName,
          industry,
          companySize,
        },
        accessToken: auth.session?.access_token,
      })
        .then((res) => {
          setUserInfo(res)
          setWorkspace(res.workspace as WorkspaceType)
          setTags(res.workspaceTags as WorkSpaceTagType[])
          notifySuccess("brand onboarding completed!!")
          router.replace("/explore")
        })
        .catch((err) => {
          notifyError(err.message)
          setIsLoading(false)
        })
    }
  }

  return userInfo?.user_id ? (
    <></>
  ) : (
    <OnboardingFrame>
      <div className={"base"}>
        {step === 0 && (
          <Profile
            setStep={setStep}
            jobTitle={jobTitle}
            setJobTitle={setJobTitle}
            fullName={fullName}
            setFullName={setFullName}
            phoneNumber={phoneNumber}
            setPhoneNumber={setPhoneNumber}
          />
        )}
        {step === 1 && (
          <Business
            companyName={companyName}
            setCompanyName={setCompanyName}
            industry={industry}
            setIndustry={setIndustry}
            companySize={companySize}
            setCompanySize={setCompanySize}
            onboardingSubmit={onboardingSubmit}
            isSubmit={isLoading}
          />
        )}
      </div>
      <style jsx>{`
        div.base {
          position: relative;
          max-width: 25rem;
          margin: 0 auto;
          top: 50%;
          transform: translateY(-50%);
        }
      `}</style>
    </OnboardingFrame>
  )
}
