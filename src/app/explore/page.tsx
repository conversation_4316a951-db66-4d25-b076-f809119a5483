"use client"

import React, { useEffect, useMemo, useState } from "react"
import Filters from "@/components/filters/Filters"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { getAssetPath } from "@/utils/util"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import Pagination from "@/components/table/Pagination"
import { useAuth } from "@/common/hooks/useAuthProvider"
import { FilterType } from "@/common/enums/filterType"
import useSearchCreator from "@/store/useSearchCreator"
import { useCreatorSearch } from "@/common/hooks/useCreatorSearch"
import Icon from "@/components/elements/Icon"
import {
  removeBookmark,
  saveBookmark,
  saveBookmarks,
} from "@/service/workspace"
import { createQueryString } from "@/common/utils/common"
import Button from "@/components/elements/Button"
import useWorkspace from "@/store/useWorkspace"
import { notifyError, notifySuccess } from "@/components/modal/Toasts"
import { useAbortableRequest } from "@/common/hooks/useAbortableRequest"
import SaveTags from "@/components/table/SaveTags"
import { exploreColumns } from "@/common/utils/table"
import AddTagModal from "@/components/modal/AddTagModal"
import CreatorTable from "@/components/table/CreatorTable"
import CreatorDetailModal from "@/components/modal/CreatorDetailModal"

export default function ExplorePages() {
  const auth = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const { currentWorkspace, setTags } = useWorkspace()
  const workspaceId = currentWorkspace?.id
  const {
    perPage,
    setPerPage,
    sortKey,
    sortDirection,
    uniqueIds,
    resetUniqueIds,
  } = useSearchCreator()
  const { getSignal, abortRequest } = useAbortableRequest()
  const [loading, setLoading] = useState({
    saveBulkLoading: false,
    pageLoading: false,
  })

  const selectUniqueIds = useMemo(() => new Set(uniqueIds), [uniqueIds])

  const search = useCreatorSearch()

  const [creators, setCreators] = useState([])
  const [totalCount, setTotalCount] = useState<number>(0)
  const searchParams = useSearchParams()
  const page = searchParams.get("page") ? Number(searchParams.get("page")) : 1
  const [tagIds, setTagIds] = useState<number[]>([])
  const [isTagAddModalOpen, setIsTagAddModalOpen] = useState(false)
  const [selectCreator, setSelectCreator] = useState(null)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)

  useEffect(() => {
    resetUniqueIds()
    const signal = getSignal()
    handleChangePage(page, signal)
    return () => {
      resetUniqueIds()
      abortRequest()
    }
  }, [page, perPage, sortKey, sortDirection])

  const handleChangePage = async (page: number, signal?: AbortSignal) => {
    try {
      setLoading((prev) => ({
        ...prev,
        pageLoading: true,
      }))
      const response = await search.searchCreators(
        auth.session?.access_token as string,
        page,
        workspaceId as number,
        signal
      )

      if (signal?.aborted) return

      if (response) {
        setCreators(response.creators || [])
        setTotalCount(response.totalCount || 0)
        if (page === 1) {
          router.push(
            pathname +
              "?" +
              createQueryString("page", "1", searchParams.toString())
          )
        }
      }
    } catch (error: any) {
      console.error("Failed to search creators", error)
    } finally {
      setLoading((prev) => ({
        ...prev,
        pageLoading: false,
      }))
    }
  }

  const actionSaveBookmark = async (
    uniqueId: string,
    tagIds: number[] = []
  ) => {
    try {
      const response = await saveBookmark({
        accessToken: auth.session?.access_token as string,
        workspaceId: workspaceId as number,
        uniqueId,
        tagIds: tagIds,
      })
      setCreators((prev: any) =>
        prev.map((creator: any) =>
          creator.unique_id === uniqueId ? { ...creator, saved: true } : creator
        )
      )
      setTags(response)
      notifySuccess(
        tagIds.length > 0 ? "Successfully select a tag." : "Successfully Save"
      )
    } catch (e) {
      notifyError(
        tagIds.length > 0
          ? "Unable to select a tag, please try again."
          : "Unable to Save, please try again."
      )
    }
  }

  const actionRemoveBookmark = async (uniqueId: string) => {
    try {
      const response = await removeBookmark({
        accessToken: auth.session?.access_token as string,
        workspaceId: workspaceId as number,
        uniqueId,
      })
      setCreators((prev: any) =>
        prev.map((creator: any) =>
          creator.unique_id === uniqueId
            ? { ...creator, saved: false }
            : creator
        )
      )
      setTags(response)
      notifySuccess("Successfully cancelled the save.")
    } catch (e) {
      notifyError("Unable to cancel save, please try again.")
    }
  }

  const saveBulk = async () => {
    try {
      setLoading((prev) => ({
        ...prev,
        saveBulkLoading: false,
      }))
      const response = await saveBookmarks({
        accessToken: auth.session?.access_token as string,
        workspaceId: workspaceId as number,
        uniqueIds: [...selectUniqueIds],
        tagIds: tagIds,
      })

      setCreators((prev: any) =>
        prev.map((creator: any) =>
          selectUniqueIds.has(creator.unique_id)
            ? { ...creator, saved: false }
            : creator
        )
      )
      setTags(response)
      resetUniqueIds()
      notifySuccess("Successfully Save")
    } catch (e) {
      notifyError("Unable to Save, please try again.")
    } finally {
      setLoading((prev) => ({
        ...prev,
        saveBulkLoading: false,
      }))
    }
  }

  return (
    <>
      <div className="table-layout">
        <input
          type="checkbox"
          className={"nav-check"}
          id={"navCheck"}
          defaultChecked={true}
        />
        <section className={"sideNav"}>
          <Filters type={FilterType.explore} search={handleChangePage} />
        </section>
        <section className={"body"}>
          <div className={`head ${selectUniqueIds.size > 0 ? "open" : ""}`}>
            <div className={"head-buttons"}>
              <div className={`save `}>
                <span>{`${selectUniqueIds.size} selected`}</span>
                <SaveTags
                  tagIds={tagIds}
                  setTagIds={setTagIds}
                  position={"right"}
                  setIsTagAddModal={setIsTagAddModalOpen}
                />
                <Button
                  type={"button"}
                  size={"sm"}
                  icon={{ path: getAssetPath("bookmark_false.svg") }}
                  style={"primary"}
                  onClick={saveBulk}
                  isLoading={loading.saveBulkLoading}
                  disabled={loading.saveBulkLoading}
                >
                  Save selected creators
                </Button>
              </div>
              <label htmlFor="navCheck" className={"nav-check-label"}>
                <Icon
                  path={getAssetPath("instant_mix.svg")}
                  color={colors.schems.dark.primary}
                />
                <span>Filter</span>
              </label>
            </div>
            <div></div>
          </div>
          <div className={"table"}>
            {creators && (
              <CreatorTable
                type={"explore"}
                columns={exploreColumns}
                creators={creators}
                actionSaveBookmark={actionSaveBookmark}
                actionRemoveBookmark={actionRemoveBookmark}
                setIsTagAddModal={setIsTagAddModalOpen}
                loading={loading.pageLoading}
                setSelectCreator={setSelectCreator}
                isDetailModalOpen={isDetailModalOpen}
                setIsDetailModalOpen={setIsDetailModalOpen}
              />
            )}
          </div>
          <div className={"foot"}>
            <Pagination
              currentPage={page}
              totalCount={totalCount}
              perPage={perPage}
              setPerPage={setPerPage}
            />
          </div>
        </section>
      </div>
      <AddTagModal
        isOpen={isTagAddModalOpen}
        setIsOpen={setIsTagAddModalOpen}
      />
      <CreatorDetailModal
        isOpen={isDetailModalOpen}
        setIsOpen={setIsDetailModalOpen}
        creator={selectCreator}
      />
      <style jsx>{`
        .table-layout {
          display: flex;
          justify-content: left;
          height: 100vh;
          & .nav-check {
            display: none;
            &:checked + .sideNav {
              display: flex;
            }
          }
          & .sideNav {
            width: 20rem;
            display: none;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
            border-right: ${schemes.borderWidth.md} solid
              ${colors.border.dark.default};
            background: linear-gradient(
              180deg,
              ${colors.bg.dark.surfaceDim} 75%,
              ${colors.bg.dark.surface} 95%
            );
          }
          & > .body {
            flex-grow: 1;
            width: calc(100% - 20rem);
            height: 100vh;
            overflow: hidden;
            white-space: nowrap;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            & > div {
              width: 100%;
              &.head {
                flex-shrink: 0;
                border-bottom: ${schemes.borderWidth.md} solid
                  ${colors.border.dark.default};
                height: 3.5rem;
                padding: 0 ${schemes.spacing["2xl"]};
                display: flex;
                justify-content: space-between;
                align-items: center;
                &.open {
                  padding: 0;
                  z-index: 12;
                  & .save {
                    display: flex;
                    opacity: 1;
                    transform: translateX(0);
                    pointer-events: auto; /* 나타날 때 클릭 가능하게 변경 */
                  }
                }
                & .save {
                  display: none;
                  height: 100%;
                  justify-content: space-between;
                  align-items: center;
                  border-right: ${schemes.borderWidth.sm} solid
                    ${colors.border.dark.default};
                  padding: 0 ${schemes.spacing["3xl"]};
                  background: ${colors.gray["90"]};
                  gap: ${schemes.spacing.md};
                  opacity: 0;
                  transform: translateX(-20px);
                  transition:
                    opacity 0.3s ease-in-out,
                    transform 0.3s ease-in-out;
                  pointer-events: none; /* 초기 상태에서 클릭 방지 */
                  & span {
                    font-weight: ${fonts.body.body.fontWeight};
                    font-size: ${fonts.body.body.fontSize};
                    color: ${colors.schems.dark.onDisabled};
                  }
                }
                & .head-buttons {
                  display: inline-flex;
                  justify-content: start;
                  align-items: center;
                  height: 100%;
                  gap: ${schemes.spacing["2xl"]};
                }
                & .nav-check-label {
                  height: 2rem;
                  padding: 0 ${schemes.spacing.md};
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  border-radius: ${schemes.radius.sm};
                  border: 1px solid ${colors.border.dark.bgLighten};
                  background: ${colors.schems.dark.secondary};
                  color: ${colors.schems.dark.onSecondary};
                  font-size: ${fonts.body.bodyBold.fontSize};
                  font-weight: ${fonts.body.bodyBold.fontWeight};
                  line-height: ${fonts.body.bodyBold.lineHeight};
                  cursor: pointer;
                  > span {
                    padding: 0 ${schemes.spacing.sm};
                  }
                }
                & .nav-check-label {
                  position: relative;
                  &::after {
                    position: absolute;
                    content: "";
                    right: -${schemes.spacing["2xl"]};
                    top: 50%;
                    transform: translateY(-50%);
                    height: 1.5rem;
                    width: 0;
                    border-right: 1px solid ${colors.border.dark.default};
                    border-left: 1px solid ${colors.border.dark.default};
                  }
                }
              }
              &.foot {
                flex-shrink: 0;
                border-top: ${schemes.borderWidth.md} solid
                  ${colors.border.dark.default};
                background: ${colors.schems.dark.base};
                height: 3.5rem;
              }
              &.table {
                flex: 1;
                height: 100%;
                max-height: calc(100vh - 7rem);
              }
            }
          }
        }
      `}</style>
    </>
  )
}
