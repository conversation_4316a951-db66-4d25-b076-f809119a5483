"use client"

import type { Session } from "@supabase/supabase-js"
import React, { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { supabaseClient } from "@/utils/supabase/client"
import Input from "@/components/elements/Input"
import Button from "@/components/elements/Button"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { notifyError, notifySuccess } from "@/components/modal/Toasts"

export default function OrderDetail() {
  const router = useRouter()
  const [session, setSession] = useState<Session | null>()
  const [newPassword, setNewPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const searchParams = new URLSearchParams(window.location.hash.split("#")[1])
  const error = searchParams.get("error") ? searchParams.get("error") : null
  const errorDescription = searchParams.get("error_description")
    ? searchParams.get("error_description")
    : null

  async function ResetPassword(password: string) {
    setIsLoading(true)
    const { data, error } = await supabaseClient.auth.updateUser({
      password: password,
    })

    if (error) {
      notifyError("There was an error updating your password.")
      setIsLoading(false)
    } else {
      notifySuccess("Password updated successfully!")
      await supabaseClient.auth.signOut()
      router.replace("/")
      setIsLoading(false)
    }
  }

  useEffect(() => {
    async function getAuth() {
      const {
        data: { session },
        error,
      } = await supabaseClient.auth.getSession()
      setSession(session)
    }
    getAuth()
  }, [])

  return (
    <>
      {session && (
        <div className={"base"}>
          <div>
            <h2>Reset your password</h2>
          </div>
          <div>
            <Input
              type={"password"}
              label={{ text: "New Password" }}
              placeholderText={"Create a secure password"}
              onChange={setNewPassword}
            />
          </div>
          <div>
            <Button
              full={true}
              onClick={() => ResetPassword(newPassword)}
              isLoading={isLoading}
              disabled={isLoading}
            >
              Reset password
            </Button>
          </div>
        </div>
      )}
      {error && (
        <div className={"base"}>
          <div>
            <h2>{errorDescription}</h2>
          </div>
          <div>
            <Button full={true} onClick={() => router.push("/")}>
              Go to Home
            </Button>
          </div>
        </div>
      )}
      <style jsx>{`
        .base {
          max-width: 25rem;
          margin: 6.25rem auto 0;
          display: flex;
          flex-direction: column;
          gap: ${schemes.spacing["8xl"]};
          > h2 {
            color: ${colors.schems.dark.onBase};
            font-size: ${fonts.heading.h2.fontSize};
            font-weight: ${fonts.heading.h2.fontWeight};
            line-height: ${fonts.heading.h2.lineHeight};
          }
        }
      `}</style>
    </>
  )
}
