"use client"

import { useMemo } from "react"
import useUserInfo from "@/store/useUserInfo"
import DashboardMok from "@/components/dashboard/DashboardMok"

export default function HomeDashboardPage() {
  const { userInfo } = useUserInfo()

  const acceptUsers = useMemo(() => {
    return process.env.PROJECT_ENV === "production"
      ? ["0820978d-8bc0-4f6a-afcb-d24ac277d403"]
      : [
          "e828d279-4c20-4dc3-a37c-122095f274b1",
          "5924d06a-3868-416b-8fbb-0ce9a2894e1f",
        ]
  }, [])

  if (userInfo && userInfo?.user_id && acceptUsers.includes(userInfo.user_id)) {
    return <DashboardMok />
  } else {
    return <></>
  }
}
