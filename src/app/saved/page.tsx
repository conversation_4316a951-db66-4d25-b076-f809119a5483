"use client"

import React, { useEffect, use<PERSON>emo, useState } from "react"
import SavedTabs from "@/components/saved/SavedTabs"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { getAssetPath } from "@/utils/util"
import CreatorTable from "@/components/table/CreatorTable"
import { useAuth } from "@/common/hooks/useAuthProvider"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import useSearchCreator from "@/store/useSearchCreator"
import Pagination from "@/components/table/Pagination"
import { useCreatorSearch } from "@/common/hooks/useCreatorSearch"
import Icon from "@/components/elements/Icon"
import {
  removeBookmark,
  removeBookmarks,
  saveBookmarks,
} from "@/service/workspace"
import { createQueryString } from "@/common/utils/common"
import useWorkspace from "@/store/useWorkspace"
import { useAbortableRequest } from "@/common/hooks/useAbortableRequest"
import Button from "@/components/elements/Button"
import { notify<PERSON>rror, notify<PERSON>uc<PERSON> } from "@/components/modal/Toasts"
import SaveTags from "@/components/table/SaveTags"
import { savedColumns } from "@/common/utils/table"
import AddTagModal from "@/components/modal/AddTagModal"
import RemoveSavedModal from "@/components/modal/RemoveSavedModal"
import CreatorDetailModal from "@/components/modal/CreatorDetailModal"
import BulkSetCustomTagModal from "@/components/modal/BulkSetCustomTagModal"

export default function SavedPages() {
  const auth = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const { currentWorkspace, setTags } = useWorkspace()
  const workspaceId = currentWorkspace?.id
  const {
    savedPerPage,
    setSavedPerPage,
    resetUniqueIds,
    savedSortDirection,
    savedSortKey,
    uniqueIds,
  } = useSearchCreator()
  const { getSignal, abortRequest } = useAbortableRequest()
  const [tagIds, setTagIds] = useState<number[]>([])

  const search = useCreatorSearch()

  const [creators, setCreators] = useState([])
  const [totalCount, setTotalCount] = useState<number>(0)
  const [loading, setLoading] = useState({
    exportExcelLoading: false,
    removeLoading: false,
    addTagLoading: false,
    pageLoading: false,
  })
  const [isTagAddModal, setIsTagAddModal] = useState(false)
  const [removeModalOpen, setRemoveModalOpen] = useState(false)
  const [selectCreator, setSelectCreator] = useState(null)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const [bulkImportModal, setBulkImportModal] = useState(false)
  const [updatePage, setUpdatePage] = useState(false)

  const searchParams = useSearchParams()
  const page = searchParams.get("page") ? Number(searchParams.get("page")) : 1

  const selectUniqueIds = useMemo(() => new Set(uniqueIds), [uniqueIds])

  useEffect(() => {
    console.log("change")
    resetUniqueIds()
    const signal = getSignal()
    handleChangePage(page, signal)
    return () => {
      resetUniqueIds()
      abortRequest()
    }
  }, [page, savedPerPage, savedSortKey, savedSortDirection, updatePage])

  const handleChangePage = async (page: number, signal?: AbortSignal) => {
    try {
      setLoading((prev) => ({
        ...prev,
        pageLoading: true,
      }))
      const response = await search.searchSavedCreators(
        auth.session?.access_token as string,
        page,
        workspaceId as number,
        signal
      )

      if (signal?.aborted) return

      if (response) {
        setCreators(response.creators || [])
        setTotalCount(response.totalCount || 0)
        if (page === 1) {
          router.push(
            pathname +
              "?" +
              createQueryString("page", "1", searchParams.toString())
          )
        }
      }
    } catch (error: any) {
      console.error("Failed to search creators", error)
    } finally {
      setLoading((prev) => ({
        ...prev,
        pageLoading: false,
      }))
    }
  }

  const actionRemoveBookmark = async (uniqueId: string) => {
    try {
      const response = await removeBookmark({
        accessToken: auth.session?.access_token as string,
        workspaceId: workspaceId as number,
        uniqueId,
      })
      notifySuccess("Successfully Save")
      setTags(response)
      const signal = getSignal()
      await handleChangePage(page, signal)
    } catch (e) {
      notifyError("Unable to Save, please try again.")
    }
  }

  const exportSavedCreators = async () => {
    setLoading((prev) => ({
      ...prev,
      exportExcelLoading: true,
    }))
    try {
      const response: any = await search.exportSavedCreators(
        auth.session?.access_token as string,
        page,
        workspaceId as number
      )
      const url = window.URL.createObjectURL(new Blob([response]))
      const link = document.createElement("a")
      link.href = url
      link.setAttribute("download", "tiktok-creator.xlsx") // 파일 이름 설정
      document.body.appendChild(link)
      link.click()

      if (link && link.parentNode) {
        link.parentNode.removeChild(link)
      }
      window.URL.revokeObjectURL(url)
      notifySuccess("Successfully downloaded.")
    } catch (e) {
      notifyError("Unable to download, please try again.")
    } finally {
      setLoading((prev) => ({
        ...prev,
        exportExcelLoading: false,
      }))
    }
  }

  const removeSaved = async () => {
    setLoading((prev) => ({
      ...prev,
      removeLoading: true,
    }))

    try {
      const response = await removeBookmarks({
        accessToken: auth.session?.access_token as string,
        workspaceId: workspaceId as number,
        uniqueIds: [...selectUniqueIds],
      })
      resetUniqueIds()
      const signal = getSignal()
      handleChangePage(1, signal)
      setTags(response)
      notifySuccess("Successfully remove from saved.")
    } catch (e) {
      notifyError("Unable to remove from saved, please try again.")
    } finally {
      setRemoveModalOpen(false)
      setLoading((prev) => ({
        ...prev,
        removeLoading: false,
      }))
    }
  }

  const saveTags = async () => {
    setLoading((prev) => ({
      ...prev,
      addTagLoading: true,
    }))

    try {
      const response = await saveBookmarks({
        accessToken: auth.session?.access_token as string,
        workspaceId: workspaceId as number,
        uniqueIds: [...selectUniqueIds],
        tagIds: tagIds,
      })

      setCreators((prev: any) =>
        prev.map((creator: any) =>
          selectUniqueIds.has(creator.unique_id)
            ? { ...creator, saved: false }
            : creator
        )
      )
      resetUniqueIds()
      setTags(response)
      notifySuccess("Successfully changed the custom tag.")
    } catch (e) {
      notifyError("Unable to change custom tag. please try again.")
    } finally {
      setLoading((prev) => ({
        ...prev,
        addTagLoading: false,
      }))
    }
  }

  return (
    <>
      {bulkImportModal && (
        <BulkSetCustomTagModal
          isOpen={bulkImportModal}
          setIsTagAddModal={setIsTagAddModal}
          reload={() => {
            setUpdatePage((prev) => !prev)
          }}
          setIsOpen={() => {
            setBulkImportModal((prev) => !prev)
          }}
        />
      )}
      <div className="table-layout">
        <input
          type="checkbox"
          className={"nav-check"}
          id={"navCheck"}
          defaultChecked={true}
        />
        <section className={"sideNav"}>
          <SavedTabs search={handleChangePage} />
        </section>
        <section className={"body"}>
          <div className={`head ${selectUniqueIds.size > 0 ? "open" : ""}`}>
            <div className={"head-buttons"}>
              <div className={`save `}>
                <span>{`${selectUniqueIds.size} selected`}</span>
                <Button
                  type={"button"}
                  size={"sm"}
                  icon={{ path: getAssetPath("bookmark_true.svg") }}
                  style={"secondary"}
                  onClick={() => setRemoveModalOpen(true)}
                >
                  Remove from saved
                </Button>
                <SaveTags
                  tagIds={tagIds}
                  setTagIds={setTagIds}
                  setIsTagAddModal={setIsTagAddModal}
                />
                <Button
                  type={"button"}
                  size={"sm"}
                  style={"secondary"}
                  onClick={saveTags}
                  isLoading={loading.addTagLoading}
                  disabled={loading.addTagLoading || tagIds.length === 0}
                >
                  Add custom tags
                </Button>
                <Button
                  type={"button"}
                  size={"sm"}
                  style={"secondary"}
                  onClick={exportSavedCreators}
                  isLoading={loading.exportExcelLoading}
                  disabled={loading.exportExcelLoading}
                >
                  Download Excel
                </Button>
              </div>
              <label htmlFor="navCheck" className={"nav-check-label"}>
                <Icon
                  path={getAssetPath("instant_mix.svg")}
                  color={colors.schems.dark.primary}
                />
                <span>Filter</span>
              </label>
            </div>
            <div>
              <div className={"top"}>
                <Button
                  type={"button"}
                  size={"sm"}
                  icon={{ path: getAssetPath("upload.svg") }}
                  style={"secondary"}
                  onClick={() => {
                    setBulkImportModal((prev) => !prev)
                  }}
                >
                  Import
                </Button>
              </div>
            </div>
          </div>
          <div className={"table"}>
            {creators && (
              <CreatorTable
                type={"saved"}
                columns={savedColumns}
                creators={creators}
                actionRemoveBookmark={actionRemoveBookmark}
                loading={loading.pageLoading}
                setSelectCreator={setSelectCreator}
                isDetailModalOpen={isDetailModalOpen}
                setIsDetailModalOpen={setIsDetailModalOpen}
              />
            )}
          </div>
          <div className={"foot"}>
            <Pagination
              currentPage={page}
              totalCount={totalCount}
              perPage={savedPerPage}
              setPerPage={setSavedPerPage}
            />
          </div>
        </section>
      </div>
      <RemoveSavedModal
        isOpen={removeModalOpen}
        setIsOpen={setRemoveModalOpen}
        loading={loading.removeLoading}
        selectUniqueIds={selectUniqueIds}
        removeSaved={removeSaved}
      />
      <CreatorDetailModal
        isOpen={isDetailModalOpen}
        setIsOpen={setIsDetailModalOpen}
        creator={selectCreator}
      />
      <AddTagModal isOpen={isTagAddModal} setIsOpen={setIsTagAddModal} />
      <style jsx>{`
        .table-layout {
          display: flex;
          justify-content: left;
          height: 100vh;
          & .nav-check {
            display: none;
            &:checked + .sideNav {
              display: flex;
            }
          }
          & .sideNav {
            width: 20rem;
            display: none;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
            border-right: ${schemes.borderWidth.md} solid
              ${colors.border.dark.default};
            background: linear-gradient(
              180deg,
              ${colors.bg.dark.surfaceDim} 75%,
              ${colors.bg.dark.surface} 95%
            );
          }
          & > .body {
            flex-grow: 1;
            width: calc(100% - 20rem);
            height: 100vh;
            overflow: hidden;
            white-space: nowrap;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            & > div {
              width: 100%;
              &.head {
                flex-shrink: 0;
                border-bottom: ${schemes.borderWidth.md} solid
                  ${colors.border.dark.default};
                height: 3.5rem;
                padding: 0 ${schemes.spacing["2xl"]};
                display: flex;
                justify-content: space-between;
                align-items: center;
                position: relative;
                &.open {
                  padding: 0;
                  z-index: 12;
                  & .save {
                    display: flex;
                    opacity: 1;
                    transform: translateX(0);
                    pointer-events: auto; /* 나타날 때 클릭 가능하게 변경 */
                  }
                }
                & .save {
                  display: none;
                  height: 100%;
                  justify-content: space-between;
                  align-items: center;
                  border-right: ${schemes.borderWidth.sm} solid
                    ${colors.border.dark.default};
                  padding: 0 ${schemes.spacing["3xl"]};
                  background: ${colors.gray["90"]};
                  gap: ${schemes.spacing.md};
                  opacity: 0;
                  transform: translateX(-20px);
                  transition:
                    opacity 0.3s ease-in-out,
                    transform 0.3s ease-in-out;
                  pointer-events: none; /* 초기 상태에서 클릭 방지 */
                  & span {
                    font-weight: ${fonts.body.body.fontWeight};
                    font-size: ${fonts.body.body.fontSize};
                    color: ${colors.schems.dark.onDisabled};
                  }
                }
                & .head-buttons {
                  display: inline-flex;
                  justify-content: start;
                  align-items: center;
                  height: 100%;
                  gap: ${schemes.spacing["2xl"]};
                }
                & .nav-check-label {
                  height: 2rem;
                  padding: 0 ${schemes.spacing.md};
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  border-radius: ${schemes.radius.sm};
                  border: 1px solid ${colors.border.dark.bgLighten};
                  background: ${colors.schems.dark.secondary};
                  color: ${colors.schems.dark.onSecondary};
                  font-size: ${fonts.body.bodyBold.fontSize};
                  font-weight: ${fonts.body.bodyBold.fontWeight};
                  line-height: ${fonts.body.bodyBold.lineHeight};
                  cursor: pointer;
                  > span {
                    padding: 0 ${schemes.spacing.sm};
                  }
                }
                & .nav-check-label,
                .top {
                  position: relative;
                  &::after {
                    position: absolute;
                    content: "";
                    right: -${schemes.spacing["2xl"]};
                    top: 50%;
                    transform: translateY(-50%);
                    height: 1.5rem;
                    width: 0;
                    border-right: 1px solid ${colors.border.dark.default};
                    border-left: 1px solid ${colors.border.dark.default};
                  }
                }
                & .top {
                  &::after {
                    right: initial;
                    left: -${schemes.spacing["2xl"]};
                  }
                }
              }
              &.foot {
                flex-shrink: 0;
                border-top: ${schemes.borderWidth.md} solid
                  ${colors.border.dark.default};
                height: 3.5rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
                & > div:first-of-type {
                  width: 19rem;
                  height: 3.5rem;
                  flex-shrink: 0;
                  text-align: right;
                  padding-right: ${schemes.spacing["2xl"]};
                  border-right: ${schemes.borderWidth.md} solid
                    ${colors.border.dark.default};
                  & > span {
                    font-size: ${fonts.body.bodyBold.fontSize};
                    font-weight: ${fonts.body.bodyBold.fontWeight};
                    line-height: 3.5rem;
                    &:first-of-type {
                      color: ${colors.schems.dark.onDisabled};
                    }
                  }
                }
                & > div:last-of-type {
                  display: flex;
                  justify-content: right;
                  align-items: center;
                  gap: ${schemes.spacing.md};
                  padding-right: ${schemes.spacing["3xl"]};
                  & div:nth-of-type(2) {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: ${schemes.spacing.sm};
                  }
                  & div:nth-of-type(3) {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                  }
                  & span {
                    color: ${colors.schems.dark.onDisabled};
                    font-size: ${fonts.body.large.fontSize};
                    font-weight: ${fonts.body.large.fontWeight};
                    line-height: ${fonts.body.large.lineHeight};
                  }
                  & input {
                    width: 2rem;
                    height: 2rem;
                    background: ${colors.schems.dark.secondary};
                    border-radius: ${schemes.radius.sm};
                    border: ${schemes.borderWidth.sm} solid
                      ${colors.border.dark.default};
                    text-align: center;
                    color: ${colors.schems.dark.onBase};

                    /* Chrome, Safari, Edge, Opera */
                    &::-webkit-outer-spin-button,
                    &::-webkit-inner-spin-button {
                      -webkit-appearance: none;
                      margin: 0;
                    }

                    /* Firefox  */
                    &[type="number"] {
                      -moz-appearance: textfield;
                    }
                  }
                  & select {
                    height: 2rem;
                    padding: 0 ${schemes.spacing.md} 0 ${schemes.spacing.xl};
                    background: ${colors.schems.dark.secondary};
                    border-radius: ${schemes.radius.sm};
                    border: ${schemes.borderWidth.sm} solid
                      ${colors.border.dark.default};
                    text-align: center;
                    color: ${colors.schems.dark.onBase};
                  }
                }
              }
              &.table {
                flex: 1;
                height: 100%;
                max-height: calc(100vh - 7rem);
              }
            }
          }
        }
      `}</style>
    </>
  )
}
