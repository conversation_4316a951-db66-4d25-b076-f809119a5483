"use client"

import Layout from "@/components/campaign/detail/Layout"
import React, { useEffect, useState } from "react"
import { getCampaign } from "@/service/campaign"
import { useParams, useSearchParams } from "next/navigation"
import CampaignDetailCreators from "@/components/campaign/detail/Creators"
import CampaignDetailSequences from "@/components/campaign/detail/Sequences"
import CampaignDetailSetting from "@/components/campaign/detail/Setting"
import Loading from "@/components/common/Loading"
import CampaignDetailTarget from "@/components/campaign/detail/Target"

export default function CampaignCreatorsPage() {
  const campaignId = Number(useParams().campaignId)

  const searchParams = useSearchParams()
  const tab = searchParams.get("tab")

  const [campaign, setCampaign] = useState(null)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    setIsLoading(true)
    getCampaign(campaignId)
      .then((res) => setCampaign(res))
      .finally(() => setIsLoading(false))
  }, [campaignId])

  return (
    <>
      {campaign && (
        <Layout
          campaignId={campaignId}
          campaign={campaign}
          setCampaign={setCampaign}
          tab={tab ?? "creators"}
        >
          {!tab && (
            <CampaignDetailCreators
              campaignId={campaignId}
              campaign={campaign}
            />
          )}
          {tab === "target" && (
            <CampaignDetailTarget
              campaignId={campaignId}
              campaign={campaign}
              setCampaign={setCampaign}
            />
          )}
          {tab === "sequence" && (
            <CampaignDetailSequences
              campaignId={campaignId}
              campaign={campaign}
              setCampaign={setCampaign}
            />
          )}
          {tab === "setting" && (
            <CampaignDetailSetting
              campaignId={campaignId}
              campaign={campaign}
              setCampaign={setCampaign}
            />
          )}
        </Layout>
      )}
      {isLoading && (
        <div className={"loading"}>
          <Loading />
        </div>
      )}
      <style jsx>{`
        .loading {
          height: 100vh;
        }
      `}</style>
    </>
  )
}
