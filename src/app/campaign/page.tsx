"use client"

import { useEffect, useMemo, useState } from "react"
import CampaignList from "@/components/campaign/List"
import { useRouter } from "next/navigation"
import useUserInfo from "@/store/useUserInfo"

export default function CampaignPage() {
  const { userInfo } = useUserInfo()
  const router = useRouter()
  const [isCampaigns, setIsCampaigns] = useState(false)

  const tiktokAuthInfo = useMemo(() => {
    return userInfo?.user_tiktok_info || null
  }, [userInfo?.user_tiktok_info])

  useEffect(() => {
    if (userInfo) {
      if (!tiktokAuthInfo?.shop_id) {
        router.replace("/campaign/onboarding")
      } else if (!userInfo.has_created_campaign) {
        router.replace("/campaign/create")
      } else {
        setIsCampaigns(true)
      }
    }
  }, [router, tiktokAuthInfo, userInfo])

  return isCampaigns && <CampaignList />
}
