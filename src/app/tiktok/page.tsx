import CryptoJS from "crypto-js"
import TiktokPageComponent from "@/app/tiktok/component"

const TiktokPage = () => {
  let code_verifier = ""
  const generateRandomString = (length = 43) => {
    let result = ""
    const characters =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~"
    const charactersLength = characters.length
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength))
    }
    return result
  }

  const tiktokAuthUrl = () => {
    const csrfState = Math.random().toString(36).substring(2)
    const isLocalEnv = process.env.PROJECT_ENV === "local"

    let url =
      `https://www.tiktok.com/v2/auth/authorize/` +
      `?client_key=${process.env.TIKTOK_CLIENT_KEY}` +
      "&scope=user.info.basic,user.info.profile,user.info.stats,video.list" +
      "&response_type=code" +
      `&redirect_uri=${encodeURIComponent(`${process.env.NEXT_PUBLIC_URL}/tiktok/callback`)}` +
      `&state=${csrfState}`
    if (isLocalEnv) {
      code_verifier = generateRandomString()
      const code_challenge = CryptoJS.SHA256(code_verifier).toString(
        CryptoJS.enc.Hex
      )

      url += `&code_challenge=${code_challenge}&code_challenge_method=S256`
    }

    return url
  }

  return (
    <TiktokPageComponent tiktokAuthUrl={tiktokAuthUrl()} cv={code_verifier} />
  )
}

export default TiktokPage
