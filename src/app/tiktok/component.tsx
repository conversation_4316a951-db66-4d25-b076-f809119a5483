"use client"

import Link from "next/link"
import { useEffect } from "react"

const TiktokPageComponent = ({
  tiktokAuthUrl,
  cv,
}: {
  tiktokAuthUrl: string
  cv: string
}) => {
  useEffect(() => {
    window.localStorage.setItem("cv", cv)
  }, [cv])

  return (
    <div className={`h-screen`}>
      <div className={`mx-auto my-96 size-fit`}>
        <Link
          className={`rounded bg-gray-900 p-4 font-bold text-white`}
          href={tiktokAuthUrl}
        >
          Continue with TikTok
        </Link>
      </div>
    </div>
  )
}

export default TiktokPageComponent
