"use client"

import { useCallback, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { getTiktokAuth } from "@/service/tiktok"
import useUserInfo from "@/store/useUserInfo"
import { useAuth } from "@/common/hooks/useAuthProvider"

export default function TiktokCallbackComponent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const tiktokCode = searchParams.get("code")!
  const isLocalEnv = process.env.PROJECT_ENV === "local"

  const auth = useAuth()
  const { userInfo } = useUserInfo()

  const callTiktokFunction = useCallback(async () => {
    const userId = userInfo?.user_id ?? auth.user?.id
    let code_verifier = ""
    let data = null

    if (userId && tiktokCode) {
      if (isLocalEnv) {
        code_verifier = window.localStorage.getItem("cv") ?? ""
        data = await getTiktokAuth(userId, tiktokCode, code_verifier)
        window.localStorage.removeItem("cv")
      } else {
        data = await getTiktokAuth(userId, tiktokCode)
      }
      router.replace("/tiktok/callback")
    }
  }, [auth.user?.id, isLocalEnv, router, tiktokCode, userInfo?.user_id])

  useEffect(() => {
    callTiktokFunction().then()
  }, [callTiktokFunction])

  return <div></div>
  // return router.replace("/tiktok")
}
