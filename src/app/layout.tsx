import type { Metada<PERSON> } from "next"
import "./globals.css"
import React from "react"
import { Roboto } from "next/font/google"
import { AuthProvider } from "@/common/hooks/useAuthProvider"
import { Toaster } from "react-hot-toast"
import NavPage from "@/components/Nav"
import StyledJsxRegistry from "@/app/registry"
import { GoogleTagManager } from "@next/third-parties/google"
import ClarityIntegration from "@/components/ClarityIntegration"

const roboto = Roboto({
  weight: ["100", "300", "400", "500", "700", "900"],
  subsets: [
    "cyrillic",
    "cyrillic-ext",
    "greek",
    "greek-ext",
    "latin",
    "latin-ext",
    "vietnamese",
  ],
})

export const metadata: Metadata = {
  title: "ALLSALE Manager",
  description: "ALLSALE Manager",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <GoogleTagManager gtmId="GTM-5S5GQS5F" />
      <body className={`${roboto.className} antialiased`}>
        <Toaster />
        <AuthProvider>
          {process.env.PROJECT_ENV === "production" && <ClarityIntegration />}
          <StyledJsxRegistry>
            <NavPage>{children}</NavPage>
          </StyledJsxRegistry>
        </AuthProvider>
      </body>
    </html>
  )
}
