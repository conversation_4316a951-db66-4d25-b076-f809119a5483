"use client"

import React, { useState } from "react"
import Competitor<PERSON>hart from "@/components/dashboard/CompetitorChart"
import { colors, schemes, headings, bodies } from "@/utils/theme/style"

const tabs = ["GMV", "Item solds", "Video", "Live"] // 실제 Supabase 데이터만 표시

export default function CompetitorsPage() {
  const [activeTab, setActiveTab] = useState("GMV")
  
  // Monthly range display
  const getDateRange = () => {
    return "Dec 2024 ~ Jun 2025 (Daily data → Monthly aggregation)"
  }
  
  return (
    <div className="competitors-dashboard">
      <div className="header">
        <h1>Trend</h1>
        <span className="date-range">{getDateRange()}</span>
      </div>
      
      <div className="chart-container">
        <CompetitorChart activeTab={activeTab} />
      </div>
      
      <div className="tabs">
        {tabs.map((tab) => (
          <button
            key={tab}
            className={`tab ${activeTab === tab ? "active" : ""}`}
            onClick={() => setActiveTab(tab)}
          >
            {tab}
          </button>
        ))}
      </div>
      
      <style jsx>{`
        .competitors-dashboard {
          width: 100%;
          height: 100vh;
          padding: ${schemes.spacing["3xl"]};
          background: ${colors.bg.dark.surfaceDim};
          display: flex;
          flex-direction: column;
        }
        
        .header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: ${schemes.spacing["2xl"]};
          
          h1 {
            color: ${colors.schems.dark.onBase};
            font-size: ${headings.h2.fontSize};
            font-weight: ${headings.h2.fontWeight};
            margin: 0;
          }
          
          .date-range {
            color: ${colors.schems.dark.onDisabled};
            font-size: ${bodies.body.fontSize};
          }
        }
        
        .chart-container {
          flex: 1;
          background: ${colors.bg.dark.surface};
          border-radius: ${schemes.radius.md};
          padding: ${schemes.spacing["2xl"]};
          margin-bottom: ${schemes.spacing["2xl"]};
        }
        
        .tabs {
          display: flex;
          gap: ${schemes.spacing.xl};
          border-top: 1px solid ${colors.border.dark.default};
          padding-top: ${schemes.spacing.xl};
          
          .tab {
            background: none;
            border: none;
            color: ${colors.schems.dark.onDisabled};
            font-size: ${bodies.body.fontSize};
            font-weight: ${bodies.body.fontWeight};
            cursor: pointer;
            padding: ${schemes.spacing.sm} ${schemes.spacing.xl};
            position: relative;
            transition: color 0.2s ease;
            
            &:hover {
              color: ${colors.schems.dark.onBase};
            }
            
            &.active {
              color: ${colors.schems.dark.primary};
              
              &::after {
                content: "";
                position: absolute;
                bottom: -${schemes.spacing.xl};
                left: 0;
                right: 0;
                height: 2px;
                background: ${colors.schems.dark.primary};
              }
            }
          }
        }
      `}</style>
    </div>
  )
} 