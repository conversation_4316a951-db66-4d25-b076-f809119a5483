import { NextResponse } from "next/server"
import { createClient } from "@/utils/supabase/server"
import { EmailOtpType } from "@supabase/auth-js"
import {requester} from "@/requester";

export async function GET(request: Request) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get("code")
  const type = searchParams.get("type") as EmailOtpType | null
  const token_hash = searchParams.get("token_hash")
  // if "next" is in param, use it as the redirect URL
  const next = searchParams.get("next") ?? "/"

  const forwardedHost = request.headers.get("x-forwarded-host") // original origin before load balancer
  const isLocalEnv = process.env.PROJECT_ENV === "local"

  const supabase = await createClient()

  if (code) {
    const { data: {session, user} , error } = await supabase.auth.exchangeCodeForSession(code)

    if (!error) {

      const userId = user?.id
      const accessToken = session?.access_token
      const userInfo = await getUserProfile(accessToken as string, userId as string)

      if (!userInfo) {
        return NextResponse.redirect(
          new URL(`/onboarding?next=${next}`, request.url)
        )
      }

      if (isLocalEnv) {
        // we can be sure that there is no load balancer in between, so no need to watch for X-Forwarded-Host
        return NextResponse.redirect(`${origin}${next}`)
      } else if (forwardedHost) {
        return NextResponse.redirect(`http://${forwardedHost}${next}`)
      } else {
        return NextResponse.redirect(`${origin}${next}`)
      }
    } else {
      return NextResponse.redirect(
        new URL(`/signin?message=${error?.message}`, request.url)
      )
    }
  } else if (token_hash && type) {
    const { data: {session, user}, error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    })

    if (!error) {
      const userId = user?.id
      const accessToken = session?.access_token
      const userInfo = await getUserProfile(accessToken as string, userId as string)

      if (!userInfo) {
        return NextResponse.redirect(
          new URL(`/onboarding?next=${next}`, request.url)
        )
      }
    }

    if (error) {
      return NextResponse.redirect(
        new URL(`/signup?message=${error?.message}`, request.url)
      )
    }
  }

  return NextResponse.redirect(new URL("/", request.url))
}


const getUserProfile = async (accessToken: string, userId: string) => {
  const res = await requester.get(`${process.env.NEXT_PUBLIC_NEST_API_URL}/brand/user/${userId}`, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    }
  } )

  return res.data.userInfo
}