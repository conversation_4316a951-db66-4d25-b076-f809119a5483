"use client"

import React, { useEffect, useState } from "react"
import { supabaseClient } from "@/utils/supabase/client"
import { getUserInfo } from "@/service/user"
import useAuthInfo from "@/store/useAuthInfo"
import useUserInfo from "@/store/useUserInfo"
import { useRouter, useSearchParams } from "next/navigation"
import useVerifyEmail from "@/store/useVerifyEmail"
import Input from "@/components/elements/Input"
import { colors, fonts, schemes } from "@/utils/theme/style"
import Button from "@/components/elements/Button"
import OnboardingFrame from "@/components/onboarding/Frame"
import { getAssetPath } from "@/utils/util"
import FindPasswordModal from "@/components/modal/FindPasswordModal"
import { WorkSpaceTagType, WorkspaceType } from "@/common/types/WorkspaceType"
import useWorkspace from "@/store/useWorkspace"

export default function SignInPages() {
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const { setAuthInfo } = useAuthInfo()
  const { setUserInfo } = useUserInfo()
  const { setTags, setWorkspace } = useWorkspace()
  const { setVerifyEmail, resetVerifyEmail } = useVerifyEmail()
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [disabled, setDisabled] = useState<boolean>(true)
  const [emailHelperText, setEmailHelperText] = useState<string | undefined>()
  const [passwordHelperText, setPasswordHelperText] = useState<
    string | undefined
  >()

  const searchParams = useSearchParams()
  const redirectUrl = searchParams.get("redirect_uri") ?? null

  useEffect(() => {
    if (email && password) {
      setDisabled(false)
    } else {
      setDisabled(true)
    }
  }, [email, password])

  const helperParse = (text: string | undefined) => {
    if (text) {
      return { text: text }
    }
  }

  const handleAuthentication = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Try to sign in with the provided credentials
      // 로그인 성공하면 user가 있고 실패하면 없음
      const { data: signInData, error: signInError } =
        await supabaseClient.auth.signInWithPassword({
          email: email,
          password: password,
        })

      // If sign in is successful, no need to proceed further
      if (signInData.user && signInData.session) {
        setAuthInfo({
          session: signInData.session,
          user: signInData.user,
        })

        await getUserInfo(signInData.user.id, signInData.session.access_token)
          .then((res) => {
            if (res.userInfo) {
              setUserInfo(res.userInfo)
              setWorkspace(res.userInfo.workspace as WorkspaceType)
              setTags(res.userInfo.workspaceTags as WorkSpaceTagType[])
              router.push(redirectUrl ?? "/")
            } else {
              router.push("/onboarding")
            }
          })
          .catch((err) => {
            console.error(err.message)
          })
          .finally(() => setIsOpen(false))
        return
      }

      // If sign in fails due to wrong password (assuming email exists as user was not created)
      if (signInError && signInError.status === 400) {
        console.log("signInError", signInError)
        if (signInError.code === "email_not_confirmed") {
          setVerifyEmail(email)
          router.push("/resend-verify")
        } else {
          console.error("Sign in error:", signInError.message)
          setPasswordHelperText(signInError.message)
        }
        setIsLoading(false)
        return
      }
    } catch (error) {
      setIsLoading(false)
      console.error("Unexpected error:", error)
      setPasswordHelperText("An error occurred. Please try again.")
    }
  }

  const signInWithGoogle = async () => {
    await supabaseClient.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo: `${location.origin}/auth/callback?next=${redirectUrl ?? ""}`,
        queryParams: {
          access_type: "offline",
          prompt: "consent",
        },
      },
    })
  }

  useEffect(() => {
    resetVerifyEmail()
  }, [])

  return (
    <OnboardingFrame>
      <div className={"base"}>
        <div>
          <h2>
            Powered by AI,
            <br />
            One-Click TikTok Collabs.
          </h2>
          <p>Focus on growth. We handle the rest.</p>
        </div>
        <div className={"google-sign"}>
          <Button full={true} onClick={signInWithGoogle}>
            <i />
            <span>Continue with Google</span>
          </Button>
        </div>
        <div className={"bar"}>
          <div></div>
        </div>
        <div className={"form"}>
          <form action="" onSubmit={handleAuthentication}>
            <div>
              <Input
                type={"email"}
                value={email}
                placeholderText={"<EMAIL>"}
                autoComplete={"email"}
                status={
                  emailHelperText || passwordHelperText ? "error" : "default"
                }
                isRequired={true}
                onChange={setEmail}
                label={{ text: "Work email" }}
                helper={helperParse(emailHelperText)}
              />
            </div>
            <div>
              <div>
                <Input
                  type={"password"}
                  value={password}
                  placeholderText={"Password"}
                  autoComplete={"current-password"}
                  status={
                    emailHelperText || passwordHelperText ? "error" : "default"
                  }
                  isRequired={true}
                  onChange={setPassword}
                  label={{ text: "Password" }}
                  helper={helperParse(passwordHelperText)}
                />
                <div>
                  <button type={"button"} onClick={() => setIsOpen(true)}>
                    Forgot password?
                  </button>
                </div>
                <div></div>
              </div>
            </div>
            <div>
              <Button
                type="submit"
                icon={{ path: getAssetPath("toy_true.svg") }}
                full={true}
                isLoading={isLoading}
                disabled={isLoading || disabled}
              >
                Sign in with Email
              </Button>
            </div>
          </form>
          <div>
            <span>Not a member?</span>
            <Button
              style={"text"}
              size={"xs"}
              onClick={() => router.push("/signup")}
            >
              Sign up
            </Button>
          </div>
        </div>
      </div>
      <FindPasswordModal isOpen={isOpen} setIsOpen={setIsOpen} />
      <style jsx>{`
        div.base {
          position: relative;
          max-width: 25rem;
          margin: 0 auto;
          top: 50%;
          transform: translateY(-50%);
        }
        h2 {
          font-size: ${fonts.heading.h2.fontSize};
          font-weight: ${fonts.heading.h2.fontWeight};
          line-height: ${fonts.heading.h2.lineHeight};
          margin-bottom: ${schemes.spacing["2xl"]};
          & + p {
            color: ${colors.schems.dark.onDisabled};
            font-size: ${fonts.body.large.fontSize};
            font-weight: ${fonts.body.large.fontWeight};
            line-height: ${fonts.body.large.lineHeight};
            margin-bottom: ${schemes.spacing["8xl"]};
          }
        }
        .bar {
          padding: ${schemes.spacing["3xl"]} 0;
          & > div {
            border-top: 1px solid ${colors.border.dark.default};
          }
        }
        .form > form {
          display: flex;
          flex-direction: column;
          & > div:nth-of-type(2) {
            margin-top: ${schemes.spacing["2xl"]};
            margin-bottom: ${schemes.spacing["3xl"]};
            & > div {
              position: relative;
              width: 100%;
              & > div:nth-of-type(2) {
                position: absolute;
                top: 0;
                right: 0;
                & > button {
                  background: none;
                  border: none;
                  padding: 0;
                  color: ${colors.schems.dark.onDisabled};
                  font-size: ${fonts.body.label.fontSize};
                  font-weight: ${fonts.body.label.fontWeight};
                  line-height: ${fonts.body.label.lineHeight};
                }
              }
            }
          }
          & + div {
            height: 2.5rem;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: ${schemes.spacing.sm};
            margin-top: ${schemes.spacing["2xl"]};
            & > span {
              color: ${colors.schems.dark.onDisabled};
              font-size: ${fonts.body.large.fontSize};
              font-weight: ${fonts.body.large.fontWeight};
              line-height: ${fonts.body.large.lineHeight};
            }
          }
        }
        .google-sign {
          button {
            background: ${colors.schems.dark.onBase} !important;
            span {
              display: flex;
              justify-content: center;
              align-items: center;
              i {
                display: inline-block;
                width: 1rem;
                height: 1rem;
                background: url("${getAssetPath("google.svg")}") center/cover
                  no-repeat;
              }
              span {
                color: ${colors.schems.dark.base} !important;
              }
            }
          }
        }
      `}</style>
    </OnboardingFrame>
  )
}
