import { devtools, persist } from "zustand/middleware"
import { create } from "@/store/useAllReset"
import { WorkSpaceTagType, WorkspaceType } from "@/common/types/WorkspaceType"

interface WorkspaceInfoState {
  tags: WorkSpaceTagType[]
  currentWorkspace: WorkspaceType | null
}

interface UserInfoActions {
  addTag: (workspaceTag: WorkSpaceTagType) => void
  updateTag: (id: number, workspaceTag: WorkSpaceTagType) => void
  removeTag: (workspaceTagId: number) => void
  setTags: (workspaceTags: WorkSpaceTagType[]) => void
  resetTags: () => void
  resetWorkspace: () => void
  setWorkspace: (workspace: WorkspaceType) => void
}

const useWorkspace = create<WorkspaceInfoState & UserInfoActions>()(
  devtools(
    persist(
      (set) => ({
        tags: [],
        currentWorkspace: null,
        setWorkspace: (workspace: WorkspaceType) => {
          set({ currentWorkspace: workspace })
        },
        resetWorkspace: () => set({ currentWorkspace: null }),
        removeTag: (workspaceTagId) =>
          set((state) => ({
            tags: state.tags.filter((tag) => tag.id !== workspaceTagId),
          })),
        addTag: (workspaceTag) =>
          set((state) => ({
            tags: [...state.tags, workspaceTag],
          })),
        updateTag: (id, workspaceTag) =>
          set((state) => ({
            tags: state.tags.map((tag) => (tag.id == id ? workspaceTag : tag)),
          })),
        resetTags: () => set({ tags: [] }),
        setTags: (workspaceTags: WorkSpaceTagType[]) => {
          set({ tags: workspaceTags })
        },
      }),
      {
        name: "workspaceInfo",
      }
    ),
    { enabled: process.env.PROJECT_ENV !== "production" }
  )
)

export default useWorkspace
