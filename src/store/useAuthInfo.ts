import { devtools, persist } from "zustand/middleware"
import type { Session, User } from "@supabase/supabase-js"
import { create } from "@/store/useAllReset"

export interface AuthType {
  session: Session | null | undefined
  user: User | null | undefined
}

interface AuthState {
  auth: AuthType
}

interface AuthActions {
  setAuthInfo: (auth: AuthType) => void
  resetAuthInfo: () => void
}

const defaultState = { session: null, user: null }

const useAuthInfo = create<AuthState & AuthActions>()(
  devtools(
    persist(
      (set) => ({
        auth: defaultState,
        setAuthInfo: (auth: AuthType) => {
          set({ auth })
        },
        resetAuthInfo: () => {
          set({ auth: defaultState })
        },
      }),
      {
        name: "supabaseAuth",
        // storage: createJSONStorage(() => sessionStorage) // default localStorage
      }
    ),
    { enabled: process.env.PROJECT_ENV !== "production" }
  )
)

export default useAuthInfo
