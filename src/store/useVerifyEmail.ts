import { devtools, persist } from "zustand/middleware"
// import { create } from "@/store/useAllReset" // 로그아웃 일괄삭제할 경우 사용
import { create } from "zustand" // 로그아웃에 영향을 받지 않아야할 경우 사용

interface VerifyEmailState {
  verifyEmail: string | null
}

interface VerifyEmailActions {
  setVerifyEmail: (verifyEmail: string | null) => void
  resetVerifyEmail: () => void
}

const initialState = null

const useVerifyEmail = create<VerifyEmailState & VerifyEmailActions>()(
  devtools(
    persist(
      (set) => ({
        verifyEmail: initialState,
        setVerifyEmail: (verifyEmail) => {
          set({ verifyEmail })
        },
        resetVerifyEmail: () => {
          set({ verifyEmail: initialState })
        },
      }),
      {
        name: "verifyEmail",
      }
    ),
    { enabled: process.env.PROJECT_ENV !== "production" }
  )
)

export default useVerifyEmail
