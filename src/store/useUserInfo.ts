import { devtools, persist } from "zustand/middleware"
import { create } from "@/store/useAllReset" // 로그아웃 일괄삭제할 경우 사용
// import { create } from "zustand" // 로그아웃에 영향을 받지 않아야할 경우 사용
import { UserType } from "@/common/types/userType"

interface UserInfoState {
  userInfo: UserType | null
  savedTab: string
}

interface UserInfoActions {
  setUserInfo: (userInfo: UserType | null) => void
  resetUserInfo: () => void
  setSavedTab: (tab: string) => void
}

const initialState = null

const useUserInfo = create<UserInfoState & UserInfoActions>()(
  devtools(
    persist(
      (set) => ({
        userInfo: initialState,
        savedTab: "tags",
        setUserInfo: (userInfo) => {
          set({ userInfo })
        },
        resetUserInfo: () => {
          set({ userInfo: initialState })
        },
        setSavedTab: (tab) => set({ savedTab: tab }),
      }),
      {
        name: "brandUserInfo",
        // storage: createJSONStorage(() => sessionStorage) // default localStorage
      }
    ),
    { enabled: process.env.PROJECT_ENV !== "production" }
  )
)

export default useUserInfo
