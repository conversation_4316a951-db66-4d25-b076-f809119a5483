import { devtools, persist } from "zustand/middleware"
import { create } from "@/store/useAllReset"
import { deepCopy } from "@/common/utils/common"
import { CampaignMessageType } from "@/common/enums/campaignTypes"

interface CampaignStoreState {
  purpose: string | null
  purposeName: string | null
  products: any | null
  isCollabs: boolean
  collabs: any | null
  creatorTags: number[]
  sequences: any[]
  datetime: any | null
  isSchedule: boolean
}

interface CampaignStoreActions {
  setPurpose: (purpose: string | null) => void
  setPurposeName: (purposeName: string | null) => void
  setProduct: (product: any) => void
  updateProduct: (product: any, index: number) => void
  removeProduct: (product: any) => void
  setIsCollabs: (collabs: boolean) => void
  setCollabs: (collabs: any | null) => void
  addTag: (tag: number) => void
  removeTag: (tag: number) => void
  resetTags: () => void
  addSequence: () => void
  setSequences: (sequence: any[]) => void
  removeSequence: (index: number) => void
  setDateTime: (datetime: any | null) => void
  setIsSchedule: (isSchedule: boolean) => void
  resetCampaignStore: () => void
}

const defaultSequence = Object.freeze({
  type: CampaignMessageType.message,
  days: 0,
  message: "",
})

const useCampaignStore = create<CampaignStoreState & CampaignStoreActions>()(
  devtools(
    persist(
      (set) => ({
        purpose: null,
        setPurpose: (value) => set({ purpose: value }),
        purposeName: null,
        setPurposeName: (value) => set({ purposeName: value }),
        products: [],
        setProduct: (value) =>
          set((state) => ({ products: [...state.products, value] })),
        updateProduct: (value: any, index: number) =>
          set((state) => ({
            products: state.products.map((product: any, idx: number) => {
              return index === idx ? value : product
            }),
          })),
        removeProduct: (value) =>
          set((state) => ({
            products: state.products.filter(
              (product: any) => product.name !== value.name
            ),
          })),
        isCollabs: true,
        setIsCollabs: (value) => set({ isCollabs: value }),
        collabs: null,
        setCollabs: (value) =>
          set((state) => ({ collabs: { ...state.collabs, ...value } })),
        creatorTags: [],
        addTag: (tags) =>
          set((state) => ({
            creatorTags: [...state.creatorTags, tags],
          })),
        removeTag: (removeTag) =>
          set((state) => ({
            creatorTags: state.creatorTags.filter((tag) => tag !== removeTag),
          })),
        resetTags: () => set({ creatorTags: [] }),
        sequences: [],
        addSequence: () =>
          set((state) => ({
            sequences: [...state.sequences, deepCopy(defaultSequence)],
          })),
        setSequences: (values) => set({ sequences: values }),
        removeSequence: (index: number) =>
          set((state) => ({
            sequences: state.sequences.filter((_, i) => i !== index),
          })),
        datetime: {
          startDate: "",
          endDate: "",
          startTime: "09:00",
          endTime: "21:00",
          timezone: null,
          weeks: parseInt("0111110", 2),
        },
        setDateTime: (values) => set({ datetime: values }),
        isSchedule: false,
        setIsSchedule: (value) => set({ isSchedule: value }),
        resetCampaignStore: () =>
          set({
            purpose: null,
            purposeName: null,
            products: [],
            isCollabs: true,
            collabs: null,
            creatorTags: [],
            sequences: [],
            datetime: {
              startDate: "",
              endDate: "",
              startTime: "09:00",
              endTime: "21:00",
              timezone: null,
              weeks: parseInt("0111110", 2),
            },
            isSchedule: false,
          }),
      }),
      { name: "campaignStore" }
    ),
    { enabled: process.env.PROJECT_ENV !== "production" }
  )
)

export default useCampaignStore
