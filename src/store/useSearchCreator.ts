import { create } from "@/store/useAllReset"
import { devtools, persist } from "zustand/middleware"
import {
  SearchFilterType,
  SelectedFilterType,
} from "@/common/types/searchFilterType"
import { creatorFilter } from "@/service/creatorFilter"

interface FilterState {
  searchFilters: SearchFilterType[]
  selectedFilters: SelectedFilterType[]
  savedSelectedFilters: SelectedFilterType[]
  perPage: number
  savedPerPage: number
  sortKey: string
  savedSortKey: string
  sortDirection: string
  savedSortDirection: string
  uniqueIds: string[]
  tagIds: number[]
  keyword: string
  product: string
}

interface FilterActions {
  resetSearchFilter: () => void
  setSelectedFilters: (selectedFilter: SelectedFilterType) => void
  resetSelectedFilters: () => void
  setSavedSelectedFilters: (selectedFilter: SelectedFilterType) => void
  resetSavedSelectedFilters: () => void
  getSearchFilter: () => void
  setPerPage: (perPage: number) => void
  setSavedPerPage: (perPage: number) => void
  setUniqueIds: (uniqueIds: Set<string>) => void
  setUniqueId: (uniqueId: string) => void
  resetUniqueIds: () => void
  changSort: (sortKey: string, sortDirection: string) => void
  changSavedSort: (sortKey: string, sortDirection: string) => void
  addTagId: (tagId: number) => void
  resetTagIds: () => void
  setKeyword: (inputKeyword: string) => void
  setProduct: (inputProduct: string) => void
}

const useSearchCreator = create<FilterState & FilterActions>()(
  devtools(
    persist(
      (set) => ({
        perPage: 50,
        savedPerPage: 50,
        searchFilters: [],
        selectedFilters: [],
        savedSelectedFilters: [],
        sortKey: "",
        savedSortKey: "",
        sortDirection: "asc",
        savedSortDirection: "asc",
        uniqueIds: [],
        tagIds: [],
        keyword: "",
        product: "",
        setKeyword: (keyword) => set({ keyword }),
        setProduct: (product) => set({ product }),
        changSort: (sortKey: string, sortDirection: string) =>
          set({ sortKey, sortDirection }),
        changSavedSort: (sortKey: string, sortDirection: string) =>
          set({ savedSortKey: sortKey, savedSortDirection: sortDirection }),
        resetUniqueIds: () => set({ uniqueIds: [] }),
        setUniqueIds: (uniqueIds: Set<string>) =>
          set({
            uniqueIds: [...uniqueIds],
          }),
        setUniqueId: (uniqueId: string) =>
          set((state) => {
            const uniqueIdsSet = new Set(state.uniqueIds)
            if (uniqueIdsSet.has(uniqueId)) {
              uniqueIdsSet.delete(uniqueId)
            } else {
              uniqueIdsSet.add(uniqueId)
            }
            return {
              uniqueIds: [...uniqueIdsSet],
            }
          }),
        setPerPage: (perPage: number) => set({ perPage }),
        setSavedPerPage: (perPage: number) =>
          set({ savedPerPage: perPage, uniqueIds: [] }),
        getSearchFilter: async () => {
          console.log('🎭 getSearchFilter 호출됨')
          const data = await creatorFilter()
          console.log('🎭 받은 필터 데이터:', data)
          set(
            { searchFilters: (data?.categories || []) as SearchFilterType[] },
            false,
            "setSearchFilters"
          )
        },
        resetSearchFilter: () => {
          set({ searchFilters: [] }, false, "resetSearchFilter")
        },
        setSelectedFilters: (selectedFilter: SelectedFilterType) =>
          set(
            (state) => {
              const { key, values } = selectedFilter

              const isValuesEmptyArray =
                Array.isArray(values) && values.length === 0
              const index = state.selectedFilters.findIndex(
                (filter) => filter.key === key
              )

              if (isValuesEmptyArray) {
                if (index !== -1) {
                  const updatedFilters = [...state.selectedFilters]
                  updatedFilters.splice(index, 1)
                  return { selectedFilters: updatedFilters }
                }
                return state
              }

              if (index !== -1) {
                const updatedFilters = [...state.selectedFilters]
                updatedFilters[index] = selectedFilter
                return { selectedFilters: updatedFilters }
              }
              return {
                selectedFilters: [...state.selectedFilters, selectedFilter],
              }
            },
            false,
            "setSelectedFilters"
          ),
        resetSelectedFilters: () => {
          set({ selectedFilters: [] }, false, "resetSelectedFilters")
        },
        setSavedSelectedFilters: (selectedFilter: SelectedFilterType) =>
          set(
            (state) => {
              const { key, values } = selectedFilter

              const isValuesEmptyArray =
                Array.isArray(values) && values.length === 0
              const index = state.savedSelectedFilters.findIndex(
                (filter) => filter.key === key
              )

              if (isValuesEmptyArray) {
                if (index !== -1) {
                  const updatedFilters = [...state.savedSelectedFilters]
                  updatedFilters.splice(index, 1)
                  return { savedSelectedFilters: updatedFilters }
                }
              }

              if (index !== -1) {
                const updatedFilters = [...state.savedSelectedFilters]
                updatedFilters[index] = selectedFilter
                return { savedSelectedFilters: updatedFilters }
              }

              return {
                savedSelectedFilters: [
                  ...state.savedSelectedFilters,
                  selectedFilter,
                ],
              }
            },
            false,
            "setSavedSelectedFilters"
          ),
        resetSavedSelectedFilters: () => {
          set({ savedSelectedFilters: [] }, false, "resetSavedSelectedFilters")
        },
        resetTagIds: () => set({ tagIds: [] }),
        addTagId: (tagId: number) => {
          set((state) => ({
            tagIds: state.tagIds.includes(tagId)
              ? state.tagIds.filter((tag) => tag !== tagId) // 이미 존재하면 제거
              : [...state.tagIds, tagId], // 없으면 추가
          }))
        },
      }),
      {
        name: "SearchFilters",
      }
    ),
    { enabled: process.env.PROJECT_ENV !== "production" }
  )
)

export default useSearchCreator
