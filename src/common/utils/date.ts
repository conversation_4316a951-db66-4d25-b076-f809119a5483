export const dateFormatYmd = (date: Date) => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`
}

export const dateMonthlyCalculate = (date: Date, monthly: number = 1) => {
  return new Date(date.setMonth(date.getMonth() + monthly))
}

export const calcDateByTimezone = ({
  date = new Date(),
  offset,
}: {
  date?: Date
  offset: number
}) => {
  const calcDate = new Date(date)
  const utcOffset = calcDate.getTimezoneOffset()
  calcDate.setMinutes(calcDate.getMinutes() + utcOffset + offset)

  return calcDate
}
