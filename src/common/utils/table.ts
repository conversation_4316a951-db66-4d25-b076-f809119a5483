export const savedColumns: {
  name: string
  data: string
  subData?: string
  sort: boolean
  align: string
  label?: string
  style?: "dollar" | "percent" | undefined
  maxWidth?: string
}[] = [
  {
    name: "Creator",
    data: "unique_id",
    sort: true,
    align: "left",
  },
  {
    name: "Custom tag",
    data: "tags",
    sort: false,
    align: "left",
  },
  {
    name: "Top Sales Item",
    data: "products",
    sort: true,
    align: "left",
    maxWidth: "240px",
  },
  {
    name: "More Item",
    data: "products",
    sort: false,
    align: "center",
  },
  {
    name: "Followers",
    data: "follower_count",
    sort: true,
    align: "right",
  },
  {
    name: "GMV",
    data: "gmv",
    subData: "gmv_percent_change",
    sort: true,
    align: "right",
    style: "dollar",
  },
  {
    name: "GPM",
    data: "gpm",
    subData: "gpm_percent_change",
    sort: true,
    align: "right",
    label: "1K Views",
    style: "dollar",
  },
  {
    name: "Items Sold",
    data: "items_sold",
    subData: "units_sold_percent_change",
    sort: true,
    align: "right",
  },
  {
    name: "Video Views",
    data: "tiktok_creator_posts",
    sort: false,
    align: "center",
  },
  {
    name: "Avg. Video Views",
    data: "video_play_avg",
    sort: true,
    align: "right",
  },
  {
    name: "Avg. Video Likes",
    data: "video_like_avg",
    sort: true,
    align: "right",
  },
  {
    name: "Avg. Video Comments",
    data: "video_comment_avg",
    sort: true,
    align: "right",
  },
  {
    name: "Avg. Video Shares",
    data: "video_share_avg",
    sort: true,
    align: "right",
  },
  {
    name: "Avg. Video Engagement",
    data: "video_view_engagement",
    sort: true,
    align: "right",
    style: "percent",
  },
  {
    name: "Avg. S.Video Views",
    data: "avg_video_views",
    sort: true,
    align: "right",
  },
  {
    name: "S.Video Engagement",
    data: "video_engagement",
    sort: true,
    align: "right",
    style: "percent",
  },
  {
    name: "Video GMV",
    data: "video_gmv",
    sort: true,
    align: "right",
    style: "dollar",
  },
  {
    name: "Video GPM",
    data: "video_gpm",
    sort: true,
    align: "right",
    label: "1K Views",
    style: "dollar",
  },
  {
    name: "Video CVR",
    data: "video_cvr",
    sort: true,
    align: "right",
    style: "percent",
  },
  {
    name: "Avg. S.Live Views",
    data: "avg_live_views",
    sort: true,
    align: "right",
  },
  {
    name: "S.Live Engagement",
    data: "live_engagement",
    sort: true,
    align: "right",
    style: "percent",
  },
  {
    name: "Live GMV",
    data: "live_gmv",
    sort: true,
    align: "right",
    label: "1K Views",
    style: "dollar",
  },
  {
    name: "Live GPM",
    data: "live_gpm",
    sort: true,
    align: "right",
    style: "dollar",
  },
  {
    name: "Live CVR",
    data: "live_cvr",
    sort: true,
    align: "right",
    style: "percent",
  },
  {
    name: "Categories",
    data: "categories",
    sort: false,
    align: "left",
    maxWidth: "400px",
  },
  {
    name: "Content Categories",
    data: "main_content_type",
    sort: true,
    align: "right",
  },
  {
    name: "",
    data: "content_type_coverage",
    sort: false,
    align: "center",
  },
  {
    name: "Skin Concerns",
    data: "skin_concerns",
    sort: false,
    align: "left",
    maxWidth: "320px",
  },
  {
    name: "Skin Types",
    data: "skin_types",
    sort: false,
    align: "left",
    maxWidth: "160px",
  },
  {
    name: "Age Range",
    data: "age_range",
    sort: true,
    align: "right",
  },
  { name: "Race", data: "race", sort: true, align: "left" },
  { name: "Gender", data: "gender", sort: true, align: "left" },
  {
    name: "Body Type",
    data: "body_type",
    sort: true,
    align: "left",
  },
  {
    name: "Contents Tag",
    data: "content_tags",
    sort: false,
    align: "left",
    maxWidth: "400px",
  },
  {
    name: "Avg.Commission",
    data: "avg_commission",
    sort: true,
    align: "right",
    style: "percent",
  },
  {
    name: "Brand Collab",
    data: "brand_collaboration_count",
    sort: true,
    align: "right",
  },
  {
    name: "Brand Mention",
    data: "brand_mentions",
    sort: false,
    align: "left",
    maxWidth: "200px",
  },
]

export const exploreColumns: {
  name: string
  data: string
  subData?: string
  sort: boolean
  align: string
  label?: string
  style?: "dollar" | "percent" | undefined
  maxWidth?: string
}[] = [
  {
    name: "Creator",
    data: "unique_id",
    sort: true,
    align: "left",
  },
  {
    name: "Top Sales Item",
    data: "products",
    sort: true,
    align: "left",
    maxWidth: "240px",
  },
  {
    name: "More Item",
    data: "products",
    sort: false,
    align: "center",
  },
  {
    name: "Content",
    data: "tiktok_creator_posts",
    sort: false,
    align: "left",
  },
  {
    name: "Followers",
    data: "follower_count",
    sort: true,
    align: "right",
  },
  {
    name: "GMV",
    data: "gmv",
    subData: "gmv_percent_change",
    sort: true,
    align: "right",
    style: "dollar",
  },
  {
    name: "GPM",
    data: "gpm",
    subData: "gpm_percent_change",
    sort: true,
    align: "right",
    label: "1K Views",
    style: "dollar",
  },
  {
    name: "Items Sold",
    data: "items_sold",
    subData: "units_sold_percent_change",
    sort: true,
    align: "right",
  },
  {
    name: "Video Views",
    data: "tiktok_creator_posts",
    sort: false,
    align: "center",
  },
  {
    name: "Avg. Video Views",
    data: "video_play_avg",
    sort: true,
    align: "right",
  },
  {
    name: "Avg. Video Likes",
    data: "video_like_avg",
    sort: true,
    align: "right",
  },
  {
    name: "Avg. Video Comments",
    data: "video_comment_avg",
    sort: true,
    align: "right",
  },
  {
    name: "Avg. Video Shares",
    data: "video_share_avg",
    sort: true,
    align: "right",
  },
  {
    name: "Avg. Video Engagement",
    data: "video_view_engagement",
    sort: true,
    align: "right",
    style: "percent",
  },
  {
    name: "Avg. S.Video Views",
    data: "avg_video_views",
    sort: true,
    align: "right",
  },
  {
    name: "S.Video Engagement",
    data: "video_engagement",
    sort: true,
    align: "right",
    style: "percent",
  },
  {
    name: "Video GMV",
    data: "video_gmv",
    sort: true,
    align: "right",
    style: "dollar",
  },
  {
    name: "Video GPM",
    data: "video_gpm",
    sort: true,
    align: "right",
    label: "1K Views",
    style: "dollar",
  },
  {
    name: "Video CVR",
    data: "video_cvr",
    sort: true,
    align: "right",
    style: "percent",
  },
  {
    name: "Avg. S.Live Views",
    data: "avg_live_views",
    sort: true,
    align: "right",
  },
  {
    name: "S.Live Engagement",
    data: "live_engagement",
    sort: true,
    align: "right",
    style: "percent",
  },
  {
    name: "Live GMV",
    data: "live_gmv",
    sort: true,
    align: "right",
    label: "1K Views",
    style: "dollar",
  },
  {
    name: "Live GPM",
    data: "live_gpm",
    sort: true,
    align: "right",
    style: "dollar",
  },
  {
    name: "Live CVR",
    data: "live_cvr",
    sort: true,
    align: "right",
    style: "percent",
  },
  {
    name: "Categories",
    data: "categories",
    sort: false,
    align: "left",
    maxWidth: "400px",
  },
  {
    name: "Content Categories",
    data: "main_content_type",
    sort: true,
    align: "right",
  },
  {
    name: "",
    data: "content_type_coverage",
    sort: false,
    align: "center",
  },
  {
    name: "Skin Concerns",
    data: "skin_concerns",
    sort: false,
    align: "left",
    maxWidth: "320px",
  },
  {
    name: "Skin Types",
    data: "skin_types",
    sort: false,
    align: "left",
    maxWidth: "160px",
  },
  {
    name: "Age Range",
    data: "age_range",
    sort: true,
    align: "right",
  },
  { name: "Race", data: "race", sort: true, align: "left" },
  { name: "Gender", data: "gender", sort: true, align: "left" },
  {
    name: "Body Type",
    data: "body_type",
    sort: true,
    align: "left",
  },
  {
    name: "Contents Tag",
    data: "content_tags",
    sort: false,
    align: "left",
    maxWidth: "400px",
  },
  {
    name: "Avg.Commission",
    data: "avg_commission",
    sort: true,
    align: "right",
    style: "percent",
  },
  {
    name: "Brand Collab",
    data: "brand_collaboration_count",
    sort: true,
    align: "right",
  },
  {
    name: "Brand Mention",
    data: "brand_mentions",
    sort: false,
    align: "left",
    maxWidth: "200px",
  },
]

export const campaignColumns: {
  name: string
  data: string
  subData?: string
  sort: boolean
  align: string
  label?: string
  style?: "dollar" | "percent" | undefined
  maxWidth?: string
  border?: string
}[] = [
  {
    name: "Creator",
    data: "unique_id",
    sort: false,
    align: "left",
    border: "right",
  },
  {
    name: "Status",
    data: "",
    sort: false,
    align: "left",
    border: "right",
  },
  {
    name: "Target",
    data: "messages",
    sort: false,
    align: "center",
  },
  {
    name: "First",
    data: "messages",
    sort: false,
    align: "center",
  },
  {
    name: "Second",
    data: "messages",
    sort: false,
    align: "center",
  },
  {
    name: "Third",
    data: "messages",
    sort: false,
    align: "center",
  },
  {
    name: "Accept",
    data: "is_collaboration_accepted",
    sort: false,
    align: "center",
    border: "left",
  },
  {
    name: "Replied",
    data: "replied",
    sort: false,
    align: "center",
    border: "left",
  },
  {
    name: "Followers",
    data: "follower_count",
    sort: false,
    align: "right",
    border: "left",
  },
  {
    name: "GMV",
    data: "gmv",
    subData: "gmv_percent_change",
    sort: false,
    align: "right",
    style: "dollar",
  },
  {
    name: "GPM",
    data: "gpm",
    subData: "gpm_percent_change",
    sort: false,
    align: "right",
    label: "1K Views",
    style: "dollar",
  },
  {
    name: "Items Sold",
    data: "units_sold",
    subData: "units_sold_percent_change",
    sort: false,
    align: "right",
  },
]
