import { colors } from "@/utils/theme/style"
import { ColorType } from "@/common/types/colorType"

export const contentTypeData = [
  {
    name: "Haircare",
    color: colors.sys.dark.red.default,
    colorCode: "red" as ColorType,
  },
  {
    name: "Skincare",
    color: colors.sys.dark.cyan.default,
    colorCode: "cyan" as ColorType,
  },
  {
    name: "Makeup",
    color: colors.sys.dark.purple.default,
    colorCode: "purple" as ColorType,
  },
  {
    name: "Bodycare",
    color: colors.sys.dark.green.default,
    colorCode: "green" as ColorType,
  },
  {
    name: "ETC",
    color: colors.sys.dark.gray.lighten,
    colorCode: "gray" as ColorType,
  },
]

export const contentStyleData = {
  Educational: [
    {
      name: "Tips & Hack",
      color: colors.sys.dark.red.default,
      colorCode: "red",
    },
    {
      name: "Tutorial",
      color: colors.sys.dark.orange.default,
      colorCode: "orange",
    },
    {
      name: "Routine & GRWM",
      color: colors.sys.dark.yellow.default,
      colorCode: "yellow",
    },
  ],
  Review: [
    {
      name: "Product Review",
      color: colors.sys.dark.green.default,
      colorCode: "green",
    },
    {
      name: "First Impression",
      color: colors.sys.dark.cyan.default,
      colorCode: "cyan",
    },
    {
      name: "Dupe Comparison",
      color: colors.sys.dark.blue.default,
      colorCode: "blue",
    },
  ],
  Shopping: [
    {
      name: "Sales & Deals",
      color: colors.sys.dark.indigo.default,
      colorCode: "indigo",
    },
    {
      name: "Haul",
      color: colors.sys.dark.purple.default,
      colorCode: "purple",
    },
    {
      name: "Unboxing",
      color: colors.sys.dark.pink.default,
      colorCode: "pink",
    },
  ],
  ETC: [
    {
      name: "ETC",
      color: colors.sys.dark.gray.lighten,
      colorCode: "gray",
    },
  ],
}
