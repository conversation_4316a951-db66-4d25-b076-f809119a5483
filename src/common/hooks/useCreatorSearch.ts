import useSearchCreator from "@/store/useSearchCreator"
import {
  exportExcel,
  searchSavedTiktokCreators,
  tiktokCreators,
} from "@/service/workspace"
import { isDummyMode, dummyCreators, dummyDelay } from "@/utils/dummy"

export const useCreatorSearch = () => {
  const searchSavedCreators = async (
    accessToken: string,
    page: number,
    workspaceId: number,
    signal?: AbortSignal
  ) => {
    if (isDummyMode()) {
      console.log('🎭 더미 모드: searchSavedCreators 호출')
      await dummyDelay(400)
      return {
        creators: dummyCreators.slice(0, 3), // 저장된 크리에이터는 일부만
        totalCount: 3,
      }
    }

    if (!workspaceId) {
      console.log("workspaceId is null")
      return null
    }
    const state = useSearchCreator.getState()
    try {
      const param = {
        filters: state.savedSelectedFilters,
        keyword: state.keyword,
        product: state.product,
        page: page,
        perPage: state.savedPerPage,
        sortKey: state.savedSortKey,
        sortDirection: state.savedSortDirection,
        tags: state.tagIds,
      }
      const data = await searchSavedTiktokCreators({
        accessToken: accessToken,
        data: param,
        workspaceId,
        signal,
      })

      return {
        creators: data.creators || [],
        totalCount: data.total_count || 0,
      }
    } catch (err: any) {
      if (err.name === "CanceledError") {
        console.log("Request was aborted")
        return
      }
      console.error("Creator search error:", err)
      return null
    }
  }

  const exportSavedCreators = async (
    accessToken: string,
    page: number,
    workspaceId: number
  ) => {
    if (isDummyMode()) {
      console.log('🎭 더미 모드: exportSavedCreators 호출')
      await dummyDelay(300)
      // 더미 모드에서는 빈 응답 반환
      return new Blob([], { type: 'application/vnd.ms-excel' })
    }

    if (!workspaceId) {
      return null
    }
    const state = useSearchCreator.getState()
    try {
      const param = {
        filters: state.savedSelectedFilters,
        keyword: state.keyword,
        product: state.product,
        page: page,
        perPage: state.savedPerPage,
        sortKey: state.savedSortKey,
        sortDirection: state.savedSortDirection,
        tags: state.tagIds,
        uniqueIds: state.uniqueIds,
      }
      return await exportExcel({
        accessToken: accessToken,
        data: param,
        workspaceId,
      })
    } catch (err: any) {
      if (err.name === "CanceledError") {
        console.log("Request was aborted")
        return
      }
      console.error("Creator search error:", err)
      return null
    }
  }

  const searchCreators = async (
    accessToken: string,
    page: number,
    workspaceId: number,
    signal?: AbortSignal
  ) => {
    if (isDummyMode()) {
      console.log('🎭 더미 모드: searchCreators 호출')
      await dummyDelay(500)
      
      const state = useSearchCreator.getState()
      let filteredCreators = dummyCreators
      
      // 키워드 필터링
      if (state.keyword) {
        filteredCreators = filteredCreators.filter(creator =>
          creator.name.toLowerCase().includes(state.keyword.toLowerCase()) ||
          creator.handle.toLowerCase().includes(state.keyword.toLowerCase())
        )
      }
      
      // 페이지네이션
      const startIndex = (page - 1) * state.perPage
      const endIndex = startIndex + state.perPage
      
      return {
        creators: filteredCreators.slice(startIndex, endIndex),
        totalCount: filteredCreators.length,
      }
    }

    const state = useSearchCreator.getState()
    try {
      const param = {
        filters: state.selectedFilters,
        keyword: state.keyword,
        product: state.product,
        page: page,
        perPage: state.perPage,
        sortKey: state.sortKey,
        sortDirection: state.sortDirection,
        workspaceId,
      }
      const data = await tiktokCreators({
        accessToken: accessToken,
        data: param,
        signal,
      })

      return {
        creators: data.creators || [],
        totalCount: data.total_count || 0,
      }
    } catch (err: any) {
      if (err.name === "CanceledError") {
        console.log("Request was aborted")
        return
      }
      console.error("Creator search error:", err)
      return null
    }
  }

  return {
    searchCreators,
    searchSavedCreators,
    exportSavedCreators,
  }
}
