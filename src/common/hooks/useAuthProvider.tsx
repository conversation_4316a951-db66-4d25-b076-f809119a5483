"use client"

import { createContext, use<PERSON>ontext, useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { supabaseClient } from "@/utils/supabase/client"
import { AuthType } from "@/store/useAuthInfo"
import { resetAllStores } from "@/store/useAllReset"
import useAuthInfo from "@/store/useAuthInfo"
import useUserInfo from "@/store/useUserInfo"
import { getUserInfo } from "@/service/user"
import useWorkspace from "@/store/useWorkspace"
import { WorkSpaceTagType, WorkspaceType } from "@/common/types/WorkspaceType"

export interface UserAuth extends AuthType {
  signOut: () => void
}

const AuthContext = createContext<UserAuth>({
  session: null,
  user: null,
  signOut: () => {},
})

export const AuthProvider = ({ children }: any) => {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const { auth, setAuthInfo } = useAuthInfo()
  const { userInfo, setUserInfo, resetUserInfo } = useUserInfo()
  const { setTags, setWorkspace } = useWorkspace()

  useEffect(() => {
    const setData = async () => {
      const {
        data: { session },
        error,
      } = await supabaseClient.auth.getSession()
      setAuthInfo({ session, user: session?.user })
      const extensionId = window && window.localStorage.getItem("extensionId")
      if (
        session?.user?.id &&
        session.access_token &&
        (!userInfo || userInfo?.user_id !== session?.user?.id)
      ) {
        resetUserInfo()
        getUserInfo(session.user.id, session.access_token).then((res) => {
          if (res.userInfo) {
            setUserInfo(res.userInfo)
            setWorkspace(res.userInfo.workspace as WorkspaceType)
            setTags(res.userInfo.workspaceTags as WorkSpaceTagType[])
          } else {
            router.push("/onboarding")
          }
        })
      }

      if (
        typeof chrome !== "undefined" &&
        chrome &&
        chrome.runtime &&
        extensionId &&
        session
      ) {
        chrome.runtime.sendMessage(
          extensionId,
          {
            action: "setSession",
            value: {
              accessToken: session?.access_token,
              refreshToken: session?.refresh_token,
              email: session?.user?.email || "",
            },
          },
          async (response) => {
            const { data, error } = await supabaseClient.auth.refreshSession()
          }
        )
      }

      setLoading(false)
    }

    setData().then()
  }, [userInfo])

  useEffect(() => {
    const { data: listener } = supabaseClient.auth.onAuthStateChange(
      (_event, session) => {
        const extensionId = window && window.localStorage.getItem("extensionId")
        if (_event === "SIGNED_OUT") {
          resetAllStores()
          if (
            typeof chrome !== "undefined" &&
            chrome &&
            chrome.runtime &&
            extensionId
          ) {
            chrome.runtime.sendMessage(
              extensionId,
              { action: "signOut", value: null },
              async (response) => {
                console.log("Response from extension:", response)
              }
            )
          }
          router.push("/")
        } else {
          setAuthInfo({ session, user: session?.user })
        }
        setLoading(false)
      }
    )

    return () => {
      listener?.subscription.unsubscribe()
    }
  }, [router, setAuthInfo])

  const value = {
    session: auth.session,
    user: auth.user,
    signOut: async () => {
      supabaseClient.auth.signOut().then(() => {
        resetAllStores()
        router.push("/signin")
      })
    },
  }

  // use a provider to pass down the value
  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  return useContext(AuthContext)
}
