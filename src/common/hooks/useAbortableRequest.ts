import { useRef, useCallback, useEffect } from "react"

export const useAbortableRequest = () => {
  const controllerRef = useRef<AbortController | null>(null)
  const getSignal = useCallback(() => {
    if (controllerRef.current) {
      controllerRef.current.abort()
    }
    controllerRef.current = new AbortController()
    return controllerRef.current.signal
  }, [])

  const abortRequest = useCallback(() => {
    if (controllerRef.current) {
      controllerRef.current.abort()
      controllerRef.current = null
    }
  }, [])

  useEffect(() => {
    return () => {
      abortRequest()
    }
  }, [abortRequest])

  return { getSignal, abortRequest }
}
