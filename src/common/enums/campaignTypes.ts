export enum CampaignTypes {
  purpose = "purpose",
  products = "products",
  target = "target",
  creators = "creators",
  messages = "messages",
  datetime = "datetime",
}

export enum CampaignMessageType {
  message = "Message",
  email = "Email",
}

export enum CampaignPurposeType {
  firstContact = "FirstContact",
  contentRemind = "ContentRemind",
  reProductionRequest = "ReProductionRequest",
}

export enum CampaignStatus {
  active = "Active",
  paused = "Paused",
  canceled = "Canceled",
  pending = "Pending",
  completed = "Completed",
  ready = "Ready",
}

export enum CampaignMessageStatus {
  sent = "Sent",
  sending = "Sending",
  pending = "Pending",
  failed = "Failed",
}
export enum CampaignMessageContentType {
  Text = "Text",
  TargetCollaborationCard = "TargetCollaborationCard",
}

export enum campaignCreatorCheckStatus {
  PENDING = "PENDING",
  NOT_AFFILIATE_USER = "NOT_AFFILIATE_USER",
  AFFILIATE_USER = "AFFILIATE_USER",
  TARGET_COLLABORATION_CONFLICT = "TARGET_COLLABORATION_CONFLICT",
  TARGET_COLLABORATION_SUCCESS = "TARGET_COLLABORATION_SUCCESS",
}

export enum CampaignFilterKey {
  Waiting = "Waiting",
  Sent = "Sent",
  Accept = "Accept",
  Replied = "Replied",
  Conflict = "Conflict",
  Failed = "Failed",
}
