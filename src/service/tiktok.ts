import { requester } from "@/requester"
import { isDummyMode, dummyDelay } from "@/utils/dummy"

export const getTiktokAuth = async (
  userId: string,
  code: string,
  codeVerifier?: string
) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: getTiktokAuth 호출')
    await dummyDelay(300)
    return {
      success: true,
      message: 'TikTok 연동이 성공적으로 완료되었습니다',
      data: {
        access_token: 'dummy-tiktok-access-token',
        refresh_token: 'dummy-tiktok-refresh-token',
        expires_in: 3600,
        user_id: 'dummy-tiktok-user-id',
        display_name: '더미 TikTok 사용자'
      }
    }
  }

  const params: {
    code: string
    codeVerifier?: string
  } = { code }
  if (codeVerifier) {
    params.codeVerifier = codeVerifier
  }
  const res = await requester.post(`/brand/tiktok/${userId}`, params)

  return res.data
}
