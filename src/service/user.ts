import { getBasePath, requester } from "@/requester"
import { isDummyMode, dummyUserInfo, dummyDelay } from "@/utils/dummy"

export const getUserInfo = async (userId: string, accessToken: string) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: getUserInfo 호출')
    await dummyDelay(300)
    return { userInfo: dummyUserInfo }
  }

  const res = await requester.get(getBasePath(`/user/${userId}`), {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  })

  return res.data
}

export const createUser = async ({
  userId,
  data,
  accessToken,
}: {
  userId: string
  data: any
  accessToken: string
}) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: createUser 호출')
    await dummyDelay(300)
    return { userInfo: { ...dummyUserInfo, ...data } }
  }

  const res = await requester.post(getBasePath(`/user/${userId}`), data, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  })

  return res.data
}
