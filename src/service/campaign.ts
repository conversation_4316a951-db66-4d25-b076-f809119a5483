import { getBasePath, requester } from "@/requester"
import { isDummyMode, dummyCampaigns, dummyCreators, dummyDelay } from "@/utils/dummy"

export const getCampaignList = async (
  workspaceId: number,
  status?: string | null
) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: getCampaignList 호출')
    await dummyDelay(300)
    let filteredCampaigns = dummyCampaigns
    if (status) {
      filteredCampaigns = dummyCampaigns.filter(campaign => campaign.status === status)
    }
    return {
      data: filteredCampaigns,
      total: filteredCampaigns.length
    }
  }

  const res = await requester.get(
    getBasePath(`/workspace/${workspaceId}/campaigns`),
    {
      params: {
        status,
      },
    }
  )

  return res.data
}

export const saveCampaign = async (workspaceId: number, data: any) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: saveCampaign 호출')
    await dummyDelay(300)
    const newCampaign = {
      id: (dummyCampaigns.length + 1).toString(),
      ...data,
      status: 'draft',
      created_at: new Date().toISOString()
    }
    return { data: newCampaign }
  }

  const res = await requester.post(
    getBasePath(`/workspace/${workspaceId}/campaign`),
    data
  )

  return res.data
}

export const getCampaign = async (campaignId: number) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: getCampaign 호출')
    await dummyDelay(300)
    const campaign = dummyCampaigns.find(c => c.id === campaignId.toString()) || dummyCampaigns[0]
    return { data: campaign }
  }

  const res = await requester.get(getBasePath(`/campaign/${campaignId}`))
  return res.data
}

export const getCampaignCreators = async (
  campaignId: number,
  page: number,
  perPage: number,
  filter?: string
) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: getCampaignCreators 호출')
    await dummyDelay(300)
    let filteredCreators = dummyCreators
    if (filter) {
      filteredCreators = dummyCreators.filter(creator => 
        creator.name.toLowerCase().includes(filter.toLowerCase()) ||
        creator.category.toLowerCase().includes(filter.toLowerCase())
      )
    }
    const startIndex = (page - 1) * perPage
    const endIndex = startIndex + perPage
    return {
      data: filteredCreators.slice(startIndex, endIndex),
      total: filteredCreators.length,
      page,
      perPage
    }
  }

  let params: {
    page: number
    perPage: number
    filter?: string
  } = {
    page,
    perPage,
  }
  if (filter) {
    params = { ...params, filter }
  }
  const res = await requester.get(
    getBasePath(`/campaign/${campaignId}/creators`),
    {
      params,
    }
  )
  return res.data
}

export const deleteCampaign = async (campaignId: number) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: deleteCampaign 호출')
    await dummyDelay(300)
    return { success: true, message: '캠페인이 삭제되었습니다' }
  }

  const res = await requester.delete(getBasePath(`/campaign/${campaignId}`))
  return res.data
}

export const updateCampaign = async (campaignId: number, data: any) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: updateCampaign 호출')
    await dummyDelay(300)
    const campaign = dummyCampaigns.find(c => c.id === campaignId.toString()) || dummyCampaigns[0]
    return { data: { ...campaign, ...data } }
  }

  const res = await requester.patch(
    getBasePath(`/campaign/${campaignId}`),
    data
  )
  return res.data
}

export const updateCampaignMessages = async (campaignId: number, data: any) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: updateCampaignMessages 호출')
    await dummyDelay(300)
    return { success: true, message: '메시지가 업데이트되었습니다' }
  }

  const res = await requester.patch(
    getBasePath(`/campaign/${campaignId}/messages`),
    data
  )
  return res.data
}

export const updateCampaignTargetCollabs = async (
  campaignId: number,
  data: any
) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: updateCampaignTargetCollabs 호출')
    await dummyDelay(300)
    return { success: true, message: '타겟 콜라보가 업데이트되었습니다' }
  }

  const res = await requester.patch(
    getBasePath(`/campaign/${campaignId}/target-collaboration`),
    data
  )
  return res.data
}

export const changeCampaignStatus = async (
  campaignId: number,
  status: string
) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: changeCampaignStatus 호출')
    await dummyDelay(300)
    return { success: true, message: `캠페인 상태가 ${status}로 변경되었습니다` }
  }

  const res = await requester.patch(
    getBasePath(`/campaign/${campaignId}/${status}`)
  )
  return res.data
}

export const deleteCampaignCreators = async (
  campaignId: number,
  creatorIds: string[]
) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: deleteCampaignCreators 호출')
    await dummyDelay(300)
    return { success: true, message: `${creatorIds.length}명의 크리에이터가 삭제되었습니다` }
  }

  const res = await requester.delete(
    getBasePath(`/campaign/${campaignId}/creators`),
    {
      data: {
        campaignCreatorIds: creatorIds,
      },
    }
  )
  return res.data
}
