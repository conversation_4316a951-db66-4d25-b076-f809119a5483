import { getBasePath, requester } from "@/requester"
import { isDummyMode, dummyCreators, dummyDelay, dummySearchFilters } from "@/utils/dummy"

export const creatorFilter = async (featureScope = "searchCreators") => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: creatorFilter 호출')
    await dummyDelay(200)
    
    // 더미 필터 데이터를 올바른 형식으로 반환
    return {
      categories: dummySearchFilters
    }
  }

  const res = await requester.get(
    getBasePath(`/search-filters/${featureScope}`)
  )

  return res.data
}

export const getCreators = async (params: any) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: getCreators 호출')
    await dummyDelay(400)
    
    let filteredCreators = dummyCreators
    
    // 검색 필터 적용
    if (params.search) {
      filteredCreators = filteredCreators.filter(creator =>
        creator.name.toLowerCase().includes(params.search.toLowerCase()) ||
        creator.handle.toLowerCase().includes(params.search.toLowerCase())
      )
    }
    
    // 카테고리 필터 적용
    if (params.category) {
      filteredCreators = filteredCreators.filter(creator =>
        creator.category === params.category
      )
    }
    
    // 팔로워 수 필터 적용
    if (params.minFollowers) {
      filteredCreators = filteredCreators.filter(creator =>
        creator.followers >= params.minFollowers
      )
    }
    
    if (params.maxFollowers) {
      filteredCreators = filteredCreators.filter(creator =>
        creator.followers <= params.maxFollowers
      )
    }
    
    // 페이지네이션
    const page = params.page || 1
    const limit = params.limit || 10
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    
    return {
      data: filteredCreators.slice(startIndex, endIndex),
      total: filteredCreators.length,
      page,
      limit,
      totalPages: Math.ceil(filteredCreators.length / limit)
    }
  }

  const res = await requester.get(getBasePath("/creators"), { params })
  return res.data
}
