import { getBasePath, requester } from "@/requester"
import { isDummyMode, dummyDelay } from "@/utils/dummy"

export const getTikTokAccessTokenWithCode = async (
  workspaceId: number,
  code: string
) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: getTikTokAccessTokenWithCode 호출')
    await dummyDelay(300)
    return {
      data: {
        access_token: 'dummy-tiktok-shop-access-token',
        refresh_token: 'dummy-tiktok-shop-refresh-token',
        expires_in: 3600,
        shop_id: 'dummy-shop-123',
        shop_name: '더미 TikTok Shop'
      }
    }
  }

  const res = await requester.get(
    getBasePath(`/workspace/${workspaceId}/tiktok-authorization`),
    {
      params: {
        code,
      },
    }
  )

  delete res.data.open_id
  delete res.data.user_type

  return res
}

export const getTiktokAuthUrl = async (workspaceId: number, url: string) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: getTiktokAuthUrl 호출')
    await dummyDelay(200)
    return {
      auth_url: 'https://dummy-tiktok-auth-url.com/authorize?client_id=dummy&redirect_uri=' + encodeURIComponent(url),
      state: 'dummy-state-123'
    }
  }

  const res = await requester.post(
    getBasePath(`/workspace/${workspaceId}/tiktok-authorization`),
    { url }
  )

  return res.data
}
