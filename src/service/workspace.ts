import { getBasePath, requester } from "@/requester"
import { isDummyMode, dummyCreators, dummyDelay } from "@/utils/dummy"

export const tiktokCreators = async ({
  accessToken,
  data,
  signal,
}: {
  accessToken: string
  data: any
  signal?: AbortSignal
}) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: tiktokCreators 호출')
    await dummyDelay(500)
    return {
      data: dummyCreators.slice(0, data.limit || 10),
      total: dummyCreators.length,
      hasMore: dummyCreators.length > (data.limit || 10)
    }
  }

  const res = await requester.post(getBasePath(`/tiktok-creators`), data, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    signal,
  })

  return res.data
}

export const searchSavedTiktokCreators = async ({
  accessToken,
  data,
  workspaceId,
  signal,
}: {
  accessToken: string
  data: any
  workspaceId: number
  signal?: AbortSignal
}) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: searchSavedTiktokCreators 호출')
    await dummyDelay(400)
    return {
      data: dummyCreators.filter(creator => 
        creator.name.toLowerCase().includes((data.search || '').toLowerCase())
      ),
      total: 2,
      bookmarks: dummyCreators.slice(0, 2).map(creator => ({
        ...creator,
        saved_at: new Date().toISOString(),
        tags: ['태그1', '태그2']
      }))
    }
  }

  const res = await requester.post(
    getBasePath(`/workspace/${workspaceId}/bookmarks`),
    data,
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      signal,
    }
  )

  return res.data
}

export const saveBookmark = async ({
  accessToken,
  workspaceId,
  uniqueId,
  tagIds,
}: {
  accessToken: string
  workspaceId: number
  uniqueId: string
  tagIds: number[]
}) => {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: saveBookmark 호출')
    await dummyDelay(300)
    return {
      success: true,
      message: '북마크가 저장되었습니다',
      bookmark: {
        uniqueId,
        tagIds,
        saved_at: new Date().toISOString()
      }
    }
  }

  const res = await requester.post(
    getBasePath(`/workspace/${workspaceId}/bookmark/${uniqueId}`),
    {
      tagIds,
    },
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    }
  )

  return res.data
}

export const editBookmarkTags = async ({
  accessToken,
  workspaceId,
  uniqueId,
  tagIds,
}: {
  accessToken: string
  workspaceId: number
  uniqueId: string
  tagIds: number[]
}) => {
  const res = await requester.post(
    getBasePath(`/workspace/${workspaceId}/bookmark/${uniqueId}/tags`),
    {
      tagIds,
    },
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    }
  )

  return res.data
}

export const saveBookmarks = async ({
  accessToken,
  workspaceId,
  uniqueIds,
  tagIds,
}: {
  accessToken: string
  workspaceId: number
  uniqueIds: string[]
  tagIds: number[]
}) => {
  const res = await requester.post(
    getBasePath(`/workspace/${workspaceId}/bookmarks/bulk`),
    {
      tagIds,
      uniqueIds,
    },
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    }
  )

  return res.data
}

export const removeBookmark = async ({
  accessToken,
  workspaceId,
  uniqueId,
}: {
  accessToken: string
  workspaceId: number
  uniqueId: string
}) => {
  const res = await requester.delete(
    getBasePath(`/workspace/${workspaceId}/bookmark/${uniqueId}`),
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    }
  )

  return res.data
}

export const removeBookmarks = async ({
  accessToken,
  workspaceId,
  uniqueIds,
}: {
  accessToken: string
  workspaceId: number
  uniqueIds: string[]
}) => {
  const res = await requester.delete(
    getBasePath(`/workspace/${workspaceId}/bookmarks`),
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      data: {
        uniqueIds,
      },
    }
  )

  return res.data
}

export const exportExcel = ({
  accessToken,
  workspaceId,
  data,
}: {
  accessToken: string
  workspaceId: number
  data: any
}) => {
  return requester.post(
    getBasePath(`/workspace/${workspaceId}/bookmarks/excel`),
    data,
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      responseType: "blob",
    }
  )
}

export const workspaceTagList = async ({
  accessToken,
  workspaceId,
}: {
  accessToken: string
  workspaceId: number
}) => {
  const res = await requester.get(
    getBasePath(`/workspace/${workspaceId}/tags`),
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    }
  )

  return res.data
}

export const workspaceTagAdd = async ({
  accessToken,
  workspaceId,
  data,
}: {
  accessToken: string
  workspaceId: number
  data: any
}) => {
  const res = await requester.post(
    getBasePath(`/workspace/${workspaceId}/tag`),
    data,
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    }
  )

  return res.data
}

export const workspaceTagEdit = async ({
  accessToken,
  workspaceId,
  tagId,
  data,
}: {
  accessToken: string
  workspaceId: number
  tagId: number
  data: any
}) => {
  await requester.patch(
    getBasePath(`/workspace/${workspaceId}/tag/${tagId}`),
    data,
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    }
  )
}

export const workspaceTagDelete = async ({
  accessToken,
  workspaceId,
  tagId,
}: {
  accessToken: string
  workspaceId: number
  tagId: number
}) => {
  await requester.delete(
    getBasePath(`/workspace/${workspaceId}/tag/${tagId}`),
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    }
  )
}

export const workspaceSortTagList = async ({
  accessToken,
  workspaceId,
  data,
}: {
  accessToken: string
  workspaceId: number
  data: any
}) => {
  const res = await requester.post(
    getBasePath(`/workspace/${workspaceId}/tags/sort`),
    { tags: data },
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    }
  )

  return res.data
}

export const importBookmarksWithFile = async ({
  tagId,
  accessToken,
  file,
  workspaceId,
}: {
  tagId: number
  accessToken: string
  file: File
  workspaceId: number
}) => {
  const formData = new FormData()
  formData.append("file", file)
  return await requester.post(
    getBasePath(`/workspace/${workspaceId}/bookmarks/${tagId}/bulk`),
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: `Bearer ${accessToken}`,
      },
    }
  )
}

export const getTiktokProducts = async ({
  workspaceId,
}: {
  workspaceId: number
}) => {
  const res = await requester.get(
    getBasePath(`/workspace/${workspaceId}/tiktok-products`)
  )

  return res.data
}

export const refreshTiktokProducts = async ({
  workspaceId,
}: {
  workspaceId: number
}) => {
  const res = await requester.put(
    getBasePath(`/workspace/${workspaceId}/tiktok-products`)
  )

  return res.data
}
