// 브랜드별 색상 정의
export const brandColors = {
  "Skin1004 US": "#4F89FF",
  "medicube US Store": "#FF4F4F",
  "Anua Store US": "#A84FFF",
  "COSRX US": "#4FFF89",
  "Beauty of Joseon US": "#FFD94F",
  "Dr.Melaxin": "#FF4FA8"
}

// 날짜 생성 함수 - 일자별로 변경
const generateDates = (endDate: string, days: number) => {
  const dates = []
  const end = new Date(endDate)
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(end)
    date.setDate(end.getDate() - i)
    dates.push(date.toISOString().split('T')[0]) // YYYY-MM-DD 형식
  }
  
  return dates
}

// 트렌드 데이터 생성 함수
const generateTrendData = (dates: string[], brands: string[]) => {
  const baseValues = {
    "Skin1004 US": { base: 35000, variance: 5000 },
    "medicube US Store": { base: 25000, variance: 8000 },
    "Anua Store US": { base: 30000, variance: 7000 },
    "COSRX US": { base: 15000, variance: 3000 },
    "Beauty of Joseon US": { base: 12000, variance: 4000 },
    "Dr.Melaxin": { base: 10000, variance: 3000 }
  }
  
  return dates.map((date, index) => {
    const dataPoint: any = { date }
    
    brands.forEach(brand => {
      const { base, variance } = baseValues[brand as keyof typeof baseValues]
      let value = base
      
      // 각 브랜드별 특별한 트렌드 패턴 (일자별로 조정)
      if (brand === "medicube US Store") {
        // medicube는 후반부에 급격한 상승
        if (index < 15) {
          value = base + Math.random() * variance - variance / 2
        } else {
          const growth = (index - 15) * 1500 // 일별 성장률 조정
          value = base + growth + Math.random() * variance
        }
      } else if (brand === "Skin1004 US") {
        // Skin1004는 초반 상승 후 하락
        if (index < 10) {
          value = base + index * 1000 + Math.random() * variance
        } else {
          value = base + 10000 - (index - 10) * 500 + Math.random() * variance
        }
      } else if (brand === "Anua Store US") {
        // Anua는 변동성이 큰 패턴
        const wave = Math.sin(index / 5) * 10000 // 주기를 일자에 맞게 조정
        value = base + wave + Math.random() * variance
      } else {
        // 나머지는 완만한 상승
        const trend = index * 200 // 일별 트렌드 조정
        const randomVariance = Math.random() * variance - variance / 2
        value = base + trend + randomVariance
      }
      
      dataPoint[brand] = Math.round(Math.max(5000, value))
    })
    
    return dataPoint
  })
}

// 최근 30일 데이터 생성
const today = new Date()
const dates = generateDates(today.toISOString().split('T')[0], 30)
const brands = Object.keys(brandColors)

// 각 탭별 더미 데이터
export const competitorData = {
  GMV: generateTrendData(dates, brands),
  "Item solds": generateTrendData(dates, brands).map(item => {
    const newItem: any = { date: item.date }
    brands.forEach(brand => {
      newItem[brand] = Math.round(item[brand] / 50) // GMV를 단가로 나눈 값
    })
    return newItem
  }),
  Creators: generateTrendData(dates, brands).map(item => {
    const newItem: any = { date: item.date }
    brands.forEach(brand => {
      newItem[brand] = Math.round(item[brand] / 500) // 크리에이터 수
    })
    return newItem
  }),
  Video: generateTrendData(dates, brands).map(item => {
    const newItem: any = { date: item.date }
    brands.forEach(brand => {
      newItem[brand] = Math.round(item[brand] / 100) // 비디오 수
    })
    return newItem
  }),
  Live: generateTrendData(dates, brands).map(item => {
    const newItem: any = { date: item.date }
    brands.forEach(brand => {
      newItem[brand] = Math.round(item[brand] / 1000) // 라이브 수
    })
    return newItem
  }),
  Views: generateTrendData(dates, brands).map(item => {
    const newItem: any = { date: item.date }
    brands.forEach(brand => {
      newItem[brand] = item[brand] * 10 // 조회수
    })
    return newItem
  })
} 