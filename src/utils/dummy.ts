// 더미 모드 설정
export const isDummyMode = (forceReal: boolean = false) => {
  // forceReal이 true이면 실제 데이터 사용 (competitor 페이지용)
  if (forceReal) return false;
  
  return process.env.NODE_ENV === 'development' || 
         process.env.NEXT_PUBLIC_DUMMY_MODE === 'true' ||
         !process.env.NEXT_PUBLIC_SUPABASE_URL ||
         process.env.NEXT_PUBLIC_SUPABASE_URL.includes('dummy')
}

// 더미 사용자 데이터
export const dummyUser = {
  id: 'dummy-user-123',
  email: '<EMAIL>',
  user_metadata: {
    full_name: '더미 사용자',
    avatar_url: null
  },
  aud: 'authenticated',
  role: 'authenticated',
  created_at: '2024-01-01T00:00:00.000Z',
  updated_at: '2024-01-01T00:00:00.000Z'
}

// 더미 세션 데이터
export const dummySession = {
  access_token: 'dummy-access-token-123',
  refresh_token: 'dummy-refresh-token-123',
  expires_in: 3600,
  expires_at: Date.now() + 3600000,
  token_type: 'bearer',
  user: dummyUser
}

// 더미 워크스페이스 데이터
export const dummyWorkspace = {
  id: 'dummy-workspace-123',
  name: '더미 워크스페이스',
  created_at: '2024-01-01T00:00:00.000Z',
  updated_at: '2024-01-01T00:00:00.000Z'
}

// 더미 태그 데이터
export const dummyTags = [
  { 
    id: '1', 
    name: '패션', 
    color: '#FF6B6B',
    count: 12,
    is_default: false
  },
  { 
    id: '2', 
    name: '뷰티', 
    color: '#4ECDC4',
    count: 8,
    is_default: false
  },
  { 
    id: '3', 
    name: '라이프스타일', 
    color: '#45B7D1',
    count: 15,
    is_default: false
  },
  { 
    id: '4', 
    name: '음식', 
    color: '#96CEB4',
    count: 6,
    is_default: false
  },
  { 
    id: '5', 
    name: '테크', 
    color: '#FECA57',
    count: 9,
    is_default: false
  }
]

// 더미 검색 필터 데이터
export const dummySearchFilters = [
  {
    id: 1,
    parentId: null,
    filterType: 'category',
    sort: 1,
    name: '카테고리',
    key: 'category',
    values: null,
    subCategories: [
      {
        id: 11,
        parentId: 1,
        filterType: 'checkbox',
        sort: 1,
        name: '패션',
        key: 'category_fashion',
        values: ['Fashion'],
        subCategories: []
      },
      {
        id: 12,
        parentId: 1,
        filterType: 'checkbox',
        sort: 2,
        name: '뷰티',
        key: 'category_beauty',
        values: ['Beauty'],
        subCategories: []
      },
      {
        id: 13,
        parentId: 1,
        filterType: 'checkbox',
        sort: 3,
        name: '라이프스타일',
        key: 'category_lifestyle',
        values: ['Lifestyle'],
        subCategories: []
      },
      {
        id: 14,
        parentId: 1,
        filterType: 'checkbox',
        sort: 4,
        name: '음식',
        key: 'category_food',
        values: ['Food'],
        subCategories: []
      },
      {
        id: 15,
        parentId: 1,
        filterType: 'checkbox',
        sort: 5,
        name: '테크',
        key: 'category_tech',
        values: ['Tech'],
        subCategories: []
      }
    ]
  },
  {
    id: 2,
    parentId: null,
    filterType: 'range',
    sort: 2,
    name: '팔로워 수',
    key: 'followers',
    values: { min: 0, max: 10000000 },
    subCategories: [
      {
        id: 21,
        parentId: 2,
        filterType: 'checkbox',
        sort: 1,
        name: '1만-10만',
        key: 'followers_10k_100k',
        values: { min: 10000, max: 100000 },
        subCategories: []
      },
      {
        id: 22,
        parentId: 2,
        filterType: 'checkbox',
        sort: 2,
        name: '10만-50만',
        key: 'followers_100k_500k',
        values: { min: 100000, max: 500000 },
        subCategories: []
      },
      {
        id: 23,
        parentId: 2,
        filterType: 'checkbox',
        sort: 3,
        name: '50만-100만',
        key: 'followers_500k_1m',
        values: { min: 500000, max: 1000000 },
        subCategories: []
      },
      {
        id: 24,
        parentId: 2,
        filterType: 'checkbox',
        sort: 4,
        name: '100만+',
        key: 'followers_1m_plus',
        values: { min: 1000000, max: null },
        subCategories: []
      }
    ]
  },
  {
    id: 3,
    parentId: null,
    filterType: 'range',
    sort: 3,
    name: '참여율',
    key: 'engagement_rate',
    values: { min: 0, max: 20 },
    subCategories: [
      {
        id: 31,
        parentId: 3,
        filterType: 'checkbox',
        sort: 1,
        name: '1-3%',
        key: 'engagement_1_3',
        values: { min: 1, max: 3 },
        subCategories: []
      },
      {
        id: 32,
        parentId: 3,
        filterType: 'checkbox',
        sort: 2,
        name: '3-5%',
        key: 'engagement_3_5',
        values: { min: 3, max: 5 },
        subCategories: []
      },
      {
        id: 33,
        parentId: 3,
        filterType: 'checkbox',
        sort: 3,
        name: '5-10%',
        key: 'engagement_5_10',
        values: { min: 5, max: 10 },
        subCategories: []
      },
      {
        id: 34,
        parentId: 3,
        filterType: 'checkbox',
        sort: 4,
        name: '10%+',
        key: 'engagement_10_plus',
        values: { min: 10, max: null },
        subCategories: []
      }
    ]
  }
]

// 더미 사용자 정보
export const dummyUserInfo = {
  user_id: 'dummy-user-123',
  email: '<EMAIL>',
  full_name: '더미 사용자',
  workspace: dummyWorkspace,
  workspaceTags: dummyTags
}

// 더미 API 응답 지연 시뮬레이션
export const dummyDelay = (ms: number = 500) => 
  new Promise(resolve => setTimeout(resolve, ms))

// 더미 크리에이터 데이터
export const dummyCreators = [
  {
    id: '1',
    unique_id: 'kimfashion',
    name: '김패션',
    nickname: '김패션',
    handle: '@kimfashion',
    email: '<EMAIL>',
    avatar: '/images/assets/avatar.svg',
    followers: 150000,
    engagement_rate: 3.5,
    avg_views: 50000,
    category: 'Fashion',
    profile_image: '/images/assets/avatar.svg',
    bio: '패션과 스타일링을 공유하는 크리에이터입니다 ✨',
    videos_count: 245,
    likes_count: 1200000,
    is_waiting_analysis: false,
    saved: false,
    tiktok_creator_posts: [
      {
        url: 'https://www.tiktok.com/@kimfashion/video/1',
        thumbnail: '/images/assets/contents_default_image.svg',
        views: 50000,
        likes: 5000,
        comments: 200,
        shares: 100
      },
      {
        url: 'https://www.tiktok.com/@kimfashion/video/2',
        thumbnail: '/images/assets/contents_default_image.svg',
        views: 45000,
        likes: 4500,
        comments: 180,
        shares: 90
      }
    ],
    tiktok_creator_products: [
      {
        id: '1',
        name: '트렌디 백',
        product_name: '트렌디 백',
        price: 59000,
        image: '/images/assets/no-image.svg',
        cover_url: '/images/assets/no-image.svg',
        category: '패션잡화',
        total_video_cnt: 12,
        total_video_sale_cnt: 150,
        total_video_sale_gmv_amt: 8850000
      }
    ],
    custom_tags: []
  },
  {
    id: '2',
    unique_id: 'leebeauty',
    name: '이뷰티',
    nickname: '이뷰티',
    handle: '@leebeauty',
    email: '<EMAIL>',
    avatar: '/images/assets/avatar.svg',
    followers: 280000,
    engagement_rate: 4.2,
    avg_views: 75000,
    category: 'Beauty',
    profile_image: '/images/assets/avatar.svg',
    bio: '메이크업과 스킨케어 팁을 알려드려요 💄',
    videos_count: 189,
    likes_count: 980000,
    is_waiting_analysis: false,
    saved: false,
    tiktok_creator_posts: [
      {
        url: 'https://www.tiktok.com/@leebeauty/video/1',
        thumbnail: '/images/assets/contents_default_image.svg',
        views: 75000,
        likes: 7500,
        comments: 300,
        shares: 150
      },
      {
        url: 'https://www.tiktok.com/@leebeauty/video/2',
        thumbnail: '/images/assets/contents_default_image.svg',
        views: 70000,
        likes: 7000,
        comments: 280,
        shares: 140
      }
    ],
    tiktok_creator_products: [
      {
        id: '2',
        name: '수분 크림',
        product_name: '수분 크림',
        price: 35000,
        image: '/images/assets/no-image.svg',
        cover_url: '/images/assets/no-image.svg',
        category: '스킨케어',
        total_video_cnt: 8,
        total_video_sale_cnt: 220,
        total_video_sale_gmv_amt: 7700000
      }
    ],
    custom_tags: []
  },
  {
    id: '3',
    unique_id: 'parklife',
    name: '박라이프',
    nickname: '박라이프',
    handle: '@parklife',
    email: '<EMAIL>',
    avatar: '/images/assets/avatar.svg',
    followers: 95000,
    engagement_rate: 5.1,
    avg_views: 35000,
    category: 'Lifestyle',
    profile_image: '/images/assets/avatar.svg',
    bio: '일상 브이로그와 생활 꿀팁 🌱',
    videos_count: 156,
    likes_count: 650000,
    is_waiting_analysis: false,
    saved: false,
    tiktok_creator_posts: [
      {
        url: 'https://www.tiktok.com/@parklife/video/1',
        thumbnail: '/images/assets/contents_default_image.svg',
        views: 35000,
        likes: 3500,
        comments: 140,
        shares: 70
      },
      {
        url: 'https://www.tiktok.com/@parklife/video/2',
        thumbnail: '/images/assets/contents_default_image.svg',
        views: 32000,
        likes: 3200,
        comments: 128,
        shares: 64
      }
    ],
    tiktok_creator_products: [
      {
        id: '3',
        name: '미니멀 다이어리',
        product_name: '미니멀 다이어리',
        price: 15000,
        image: '/images/assets/no-image.svg',
        cover_url: '/images/assets/no-image.svg',
        category: '문구류',
        total_video_cnt: 5,
        total_video_sale_cnt: 85,
        total_video_sale_gmv_amt: 1275000
      }
    ],
    custom_tags: []
  },
  {
    id: '4',
    unique_id: 'choifood',
    name: '최푸드',
    nickname: '최푸드',
    handle: '@choifood',
    email: '<EMAIL>',
    avatar: '/images/assets/avatar.svg',
    followers: 320000,
    engagement_rate: 3.8,
    avg_views: 85000,
    category: 'Food',
    profile_image: '/images/assets/avatar.svg',
    bio: '맛집 탐방과 요리 레시피 🍳',
    videos_count: 278,
    likes_count: 1450000,
    is_waiting_analysis: false,
    saved: false,
    tiktok_creator_posts: [
      {
        url: 'https://www.tiktok.com/@choifood/video/1',
        thumbnail: '/images/assets/contents_default_image.svg',
        views: 85000,
        likes: 8500,
        comments: 340,
        shares: 170
      },
      {
        url: 'https://www.tiktok.com/@choifood/video/2',
        thumbnail: '/images/assets/contents_default_image.svg',
        views: 80000,
        likes: 8000,
        comments: 320,
        shares: 160
      }
    ],
    tiktok_creator_products: [
      {
        id: '4',
        name: '프리미엄 조미료 세트',
        product_name: '프리미엄 조미료 세트',
        price: 29000,
        image: '/images/assets/no-image.svg',
        cover_url: '/images/assets/no-image.svg',
        category: '식품',
        total_video_cnt: 15,
        total_video_sale_cnt: 320,
        total_video_sale_gmv_amt: 9280000
      }
    ],
    custom_tags: []
  },
  {
    id: '5',
    unique_id: 'techyoutuber',
    name: '테크유튜버',
    nickname: '테크유튜버',
    handle: '@techyoutuber',
    email: '<EMAIL>',
    avatar: '/images/assets/avatar.svg',
    followers: 450000,
    engagement_rate: 4.5,
    avg_views: 120000,
    category: 'Tech',
    profile_image: '/images/assets/avatar.svg',
    bio: '최신 기술과 가젯 리뷰 📱',
    videos_count: 198,
    likes_count: 2100000,
    is_waiting_analysis: false,
    saved: false,
    tiktok_creator_posts: [
      {
        url: 'https://www.tiktok.com/@techyoutuber/video/1',
        thumbnail: '/images/assets/contents_default_image.svg',
        views: 120000,
        likes: 12000,
        comments: 480,
        shares: 240
      },
      {
        url: 'https://www.tiktok.com/@techyoutuber/video/2',
        thumbnail: '/images/assets/contents_default_image.svg',
        views: 115000,
        likes: 11500,
        comments: 460,
        shares: 230
      }
    ],
    tiktok_creator_products: [
      {
        id: '5',
        name: '스마트 워치',
        product_name: '스마트 워치',
        price: 89000,
        image: '/images/assets/no-image.svg',
        cover_url: '/images/assets/no-image.svg',
        category: '전자제품',
        total_video_cnt: 20,
        total_video_sale_cnt: 180,
        total_video_sale_gmv_amt: 16020000
      }
    ],
    custom_tags: []
  }
]

// 더미 제품 데이터
export const dummyProducts = [
  {
    id: '1',
    name: '스마트폰 케이스',
    price: 25000,
    image: '/images/assets/no-image.svg',
    category: '액세서리',
    description: '고급 실리콘 소재의 스마트폰 보호 케이스'
  },
  {
    id: '2',
    name: '무선 이어폰',
    price: 89000,
    image: '/images/assets/no-image.svg',
    category: '전자제품',
    description: '노이즈 캔슬링 기능이 있는 무선 이어폰'
  },
  {
    id: '3',
    name: '스킨케어 세트',
    price: 65000,
    image: '/images/assets/no-image.svg',
    category: '뷰티',
    description: '천연 성분의 스킨케어 3종 세트'
  }
]

// 더미 캠페인 데이터
export const dummyCampaigns = [
  {
    id: '1',
    name: '신제품 론칭 캠페인',
    status: 'active',
    created_at: '2024-01-01T00:00:00.000Z',
    target_audience: '20-30대 여성',
    budget: 1000000,
    description: '새로운 뷰티 제품을 홍보하는 캠페인입니다',
    start_date: '2024-01-15T00:00:00.000Z',
    end_date: '2024-02-15T00:00:00.000Z',
    creators_count: 5,
    products: [dummyProducts[2]]
  },
  {
    id: '2',
    name: '봄 시즌 패션 캠페인', 
    status: 'draft',
    created_at: '2024-01-02T00:00:00.000Z',
    target_audience: '20-40대 남녀',
    budget: 1500000,
    description: '봄 시즌 패션 아이템을 소개하는 캠페인',
    start_date: '2024-03-01T00:00:00.000Z',
    end_date: '2024-04-30T00:00:00.000Z',
    creators_count: 8,
    products: [dummyProducts[0]]
  },
  {
    id: '3',
    name: '테크 가젯 리뷰 캠페인',
    status: 'completed',
    created_at: '2023-12-01T00:00:00.000Z',
    target_audience: '25-45대 테크 관심층',
    budget: 2000000,
    description: '최신 테크 제품들을 리뷰하는 캠페인',
    start_date: '2023-12-15T00:00:00.000Z',
    end_date: '2024-01-15T00:00:00.000Z',
    creators_count: 3,
    products: [dummyProducts[1]]
  }
]

// 더미 북마크 데이터
export const dummyBookmarks = dummyCreators.slice(0, 3).map((creator, index) => ({
  ...creator,
  bookmark_id: `bookmark-${index + 1}`,
  saved_at: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toISOString(),
  tags: dummyTags.slice(0, 2)
}))

// 더미 통계 데이터
export const dummyStats = {
  total_campaigns: 15,
  active_campaigns: 5,
  total_creators: 245,
  total_reach: 2500000,
  engagement_rate: 4.2,
  conversion_rate: 2.8
} 