import { createBrowserClient } from "@supabase/ssr"
import { isDummyMode, dummySession, dummyUser, dummyDelay } from "@/utils/dummy"

// 더미 Supabase 클라이언트 생성
const createDummyClient = () => {
  return {
    auth: {
      getSession: async () => {
        await dummyDelay(200)
        return {
          data: { session: dummySession },
          error: null
        }
      },
      signInWithPassword: async ({ email, password }: any) => {
        await dummyDelay(300)
        return {
          data: { session: dummySession, user: dummyUser },
          error: null
        }
      },
      signUp: async ({ email, password }: any) => {
        await dummyDelay(300)
        return {
          data: { session: dummySession, user: dummyUser },
          error: null
        }
      },
      signOut: async () => {
        await dummyDelay(200)
        return { error: null }
      },
      onAuthStateChange: (callback: any) => {
        // 더미 모드에서는 즉시 로그인 상태로 설정
        setTimeout(() => {
          callback('SIGNED_IN', dummySession)
        }, 100)
        
        return {
          data: {
            subscription: {
              unsubscribe: () => {}
            }
          }
        }
      },
      refreshSession: async () => {
        await dummyDelay(200)
        return {
          data: { session: dummySession, user: dummyUser },
          error: null
        }
      }
    }
  }
}

function createClient() {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: Supabase 클라이언트가 더미로 실행됩니다')
    return createDummyClient()
  }
  
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

export const supabaseClient = createClient()
