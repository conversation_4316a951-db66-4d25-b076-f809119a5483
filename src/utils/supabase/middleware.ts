import { createServerClient } from "@supabase/ssr"
import { NextResponse, type NextRequest } from "next/server"
import { isDummyMode, dummyUser } from "@/utils/dummy"

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  // 더미 모드인 경우 인증 체크를 건너뛰고 항상 로그인된 상태로 처리
  if (isDummyMode()) {
    console.log('🎭 더미 모드: middleware 인증 체크 건너뛰기')
    
    const { pathname } = request.nextUrl
    const publicAuthRoutes = ["/signin", "/signup"]
    const isPublicAuthRoute = publicAuthRoutes.some((route) =>
      pathname.startsWith(route)
    )
    const isRootPath = pathname === "/"

    // 더미 모드에서는 항상 로그인된 상태로 간주
    if (isPublicAuthRoute || isRootPath) {
      return NextResponse.redirect(new URL("/explore", request.url))
    }

    return supabaseResponse
  }

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) =>
            request.cookies.set(name, value)
          )
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  const {
    data: { user },
  } = await supabase.auth.getUser()

  const { pathname } = request.nextUrl
  const protectedRoutes = [
    "/saved",
    "/explore",
    "/onboarding",
    "/tiktok",
    "/campaign",
  ]
  const isRootPath = pathname === "/"
  const isProtectedRoute = protectedRoutes.some((route) =>
    pathname.startsWith(route)
  )

  const publicAuthRoutes = ["/signin", "/signup"]
  const isPublicAuthRoute = publicAuthRoutes.some((route) =>
    pathname.startsWith(route)
  )

  if (!user && !isPublicAuthRoute && (isProtectedRoute || isRootPath)) {
    return NextResponse.redirect(
      new URL(`/signin?redirect_uri=${pathname}`, request.url)
    )
  }

  if (user && (isPublicAuthRoute || isRootPath)) {
    return NextResponse.redirect(new URL("/explore", request.url))
  }

  return supabaseResponse
}
