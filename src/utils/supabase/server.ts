import { createServerClient, type CookieOptions } from "@supabase/ssr"
import { cookies } from "next/headers"
import { isDummyMode, dummySession, dummyUser, dummyDelay } from "@/utils/dummy"

// 더미 서버 Supabase 클라이언트 생성
const createDummyServerClient = () => {
  return {
    auth: {
      getSession: async () => {
        await dummyDelay(100)
        return {
          data: { session: dummySession },
          error: null
        }
      },
      getUser: async () => {
        await dummyDelay(100)
        return {
          data: { user: dummyUser },
          error: null
        }
      }
    }
  }
}

export async function createClient() {
  if (isDummyMode()) {
    console.log('🎭 더미 모드: 서버 Supabase 클라이언트가 더미로 실행됩니다')
    return createDummyServerClient()
  }

  const cookieStore = await cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}
