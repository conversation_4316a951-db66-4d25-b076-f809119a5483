import axios from "axios"
import { isDummyMode, dummyUserInfo, dummyCreators, dummyCampaigns, dummyDelay } from "@/utils/dummy"

export const getBasePath = (subPath: string) => {
  return `/brand${subPath}`
}

// 더미 API 응답 생성
const createDummyResponse = async (url: string, method: string = 'GET') => {
  await dummyDelay(300) // API 응답 지연 시뮬레이션
  
  // URL 기반으로 더미 데이터 반환
  if (url.includes('/user/info')) {
    return { userInfo: dummyUserInfo }
  }
  
  if (url.includes('/creators')) {
    return {
      data: dummyCreators,
      total: dummyCreators.length,
      page: 1,
      limit: 10
    }
  }
  
  if (url.includes('/campaigns')) {
    return {
      data: dummyCampaigns,
      total: dummyCampaigns.length,
      page: 1,
      limit: 10
    }
  }
  
  // 기본 성공 응답
  return { success: true, message: '더미 API 응답' }
}

// 더미 axios 인스턴스 생성
const createDummyRequester = () => {
  const dummyAxios = {
    get: async (url: string, config?: any) => createDummyResponse(url, 'GET'),
    post: async (url: string, data?: any, config?: any) => createDummyResponse(url, 'POST'),
    put: async (url: string, data?: any, config?: any) => createDummyResponse(url, 'PUT'),
    patch: async (url: string, data?: any, config?: any) => createDummyResponse(url, 'PATCH'),
    delete: async (url: string, config?: any) => createDummyResponse(url, 'DELETE'),
    interceptors: {
      request: {
        use: (onFulfilled?: any, onRejected?: any) => {}
      },
      response: {
        use: (onFulfilled?: any, onRejected?: any) => {}
      }
    }
  }
  return dummyAxios
}

// 더미 모드 확인 후 적절한 requester 반환
export const requester = isDummyMode() 
  ? (() => {
      console.log('🎭 더미 모드: API 요청이 더미로 처리됩니다')
      return createDummyRequester()
    })()
  : axios

if (!isDummyMode()) {
  requester.interceptors.request.use(
    function (config) {
      if (typeof window !== "undefined") {
        const {
          state: { auth },
        } = localStorage?.getItem("supabaseAuth")
          ? JSON.parse(<string>localStorage?.getItem("supabaseAuth"))
          : null

        if (auth?.session?.access_token) {
          config.headers!.authorization = `Bearer ${auth?.session?.access_token}`
        }
      }

      return config
    },
    function (error) {
      return Promise.reject(error)
    }
  )

  requester.interceptors.response.use(
    (res) => {
      return res.data
    },
    (error) => {
      return Promise.reject(error)
    }
  )
}
