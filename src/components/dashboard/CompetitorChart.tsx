"use client"

import React, { useState, useEffect } from "react"
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  Dot
} from "recharts"
import { createBrowserClient } from '@supabase/ssr'
import { colors, schemes, bodies } from "@/utils/theme/style"

// Supabase 설정 (더미 모드 완전 우회)
const supabase = createBrowserClient(
  'https://jqfijmjvfgzgghvjbwxc.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpxZmlqbWp2Zmd6Z2dodmpid3hjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDI1NjUyMDIsImV4cCI6MjAxODE0MTIwMn0.JItd7grxUM0vo_h-UWc6Z7KuGusTbFK0qQPEYQjrUaU'
)

// 브랜드 색상
const brandColors = {
  "Skin1004 US": "#FF6B6B",
  "medicube US Store": "#4ECDC4", 
  "COSRX US": "#45B7D1",
  "Dr.Melaxin Global": "#96CEB4",
  "Anua Store US": "#FFEAA7",
  "Beauty of Joseon US": "#DDA0DD"
}

interface CompetitorChartProps {
  activeTab: string
}

interface DailyData {
  date: string
  brand_name: string
  gmv: number
  sales_count: number
  video_count: number
  live_count: number
  product_count: number
}

interface MonthlyData {
  month: string
  [brandName: string]: number | string
}

interface ChartData {
  month: string
  [brandName: string]: number | string
}

export default function CompetitorChart({ activeTab }: CompetitorChartProps) {
  const [dailyData, setDailyData] = useState<DailyData[]>([])
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [hiddenBrands, setHiddenBrands] = useState<Set<string>>(new Set())
  const [currentValues, setCurrentValues] = useState<{[key: string]: number}>({})

  const brands = Object.keys(brandColors)

  useEffect(() => {
    const fetchDailyData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        console.log('🚀 더미 모드 완전 우회 - 실제 Supabase 연결 시작!')
        console.log('🔄 Loading daily data from Supabase with pagination...')
        
        // Pagination으로 모든 데이터 가져오기
        let allData: DailyData[] = []
        let pageSize = 1000
        let currentPage = 0
        let hasMore = true
        
        while (hasMore) {
          console.log(`📄 페이지 ${currentPage + 1} 로딩 중... (${currentPage * pageSize}~${(currentPage + 1) * pageSize - 1})`)
          
          const { data: pageData, error } = await supabase
            .schema('brand')
            .from('competitor-dashboard-daily')
            .select('*')
            .order('date', { ascending: true })
            .range(currentPage * pageSize, (currentPage + 1) * pageSize - 1)
          
          if (error) {
            console.error('❌ Supabase pagination error:', error)
            setError(`Data loading failed: ${error.message}`)
            return
          }
          
          if (!pageData || pageData.length === 0) {
            hasMore = false
            break
          }
          
          allData = [...allData, ...pageData]
          console.log(`  ✅ 페이지 ${currentPage + 1}: ${pageData.length}건 로드 (총 ${allData.length}건)`)
          
          // 페이지 데이터가 pageSize보다 작으면 마지막 페이지
          if (pageData.length < pageSize) {
            hasMore = false
          }
          
          currentPage++
          
          // 무한 루프 방지 (최대 10페이지)
          if (currentPage >= 10) {
            console.warn('⚠️ 최대 페이지 수 도달 (10페이지)')
            break
          }
        }
        
        console.log('🔍 전체 데이터 분석:');
        console.log('  총 데이터 건수:', allData.length);
        
        if (allData.length > 0) {
          const sortedData = allData.sort((a, b) => a.date.localeCompare(b.date))
          const dateRange = {
            earliest: sortedData[0].date,
            latest: sortedData[sortedData.length - 1].date
          };
          console.log('  날짜 범위:', dateRange.earliest, '~', dateRange.latest);
          
          const brandCounts = {};
          sortedData.forEach(item => {
            brandCounts[item.brand_name] = (brandCounts[item.brand_name] || 0) + 1;
          });
          console.log('  브랜드별 데이터 건수:', brandCounts);
          
          // 디버깅: Anua Store US 6월 데이터 확인
          const anuaJuneData = sortedData.filter(item => 
            item.brand_name === 'Anua Store US' && 
            item.date >= '2025-06-01' && 
            item.date < '2025-07-01'
          );
          console.log('🔍 Anua Store US 6월 데이터 건수:', anuaJuneData.length);
          
          let anuaGMV = 0, anuaSales = 0, anuaVideos = 0, anuaLive = 0;
          anuaJuneData.forEach(item => {
            anuaGMV += item.gmv || 0;
            anuaSales += item.sales_count || 0;
            anuaVideos += item.video_count || 0;
            anuaLive += item.live_count || 0;
          });
          
          console.log('🎯 Pagination으로 집계한 Anua 6월 데이터:');
          console.log(`  GMV: $${anuaGMV.toLocaleString()}`);
          console.log(`  Sales: ${anuaSales.toLocaleString()}`);
          console.log(`  Videos: ${anuaVideos.toLocaleString()}`);
          console.log(`  Live: ${anuaLive.toLocaleString()}`);
          
          setDailyData(sortedData)
        } else {
          console.warn('⚠️ No data available')
          setError('No data available')
          return
        }
        
        console.log(`✅ Pagination으로 모든 데이터 로딩 성공! ${allData.length} daily records loaded successfully`)
        
        // Aggregate daily data to monthly
        const monthly = aggregateToMonthly(allData, activeTab)
        setMonthlyData(monthly)
        
        // Set current values (latest month data)
        if (monthly.length > 0) {
          const latestMonth = monthly[monthly.length - 1]
          const values: {[key: string]: number} = {}
          brands.forEach(brand => {
            values[brand] = latestMonth[brand] as number || 0
          })
          setCurrentValues(values)
        }
        
      } catch (err) {
        console.error('❌ Unexpected error:', err)
        setError('An error occurred while loading data')
      } finally {
        setLoading(false)
      }
    }

    fetchDailyData()
  }, [activeTab])

  // Function to calculate daily differences for cumulative metrics (Video, Live)
  const calculateMonthlyDifferences = (data: DailyData[], metric: string, monthKey: string, brandName: string): number => {
    // Get data for specific brand and month, sorted by date
    const monthData = data
      .filter(item => 
        item.brand_name === brandName && 
        item.date.substring(0, 7) === monthKey
      )
      .sort((a, b) => a.date.localeCompare(b.date))
    
    if (monthData.length === 0) return 0
    
    let totalDifference = 0
    const fieldName = metric === 'Video' ? 'video_count' : 'live_count'
    
    // For the first day in the month, we need the previous day's value for comparison
    const prevMonthLastDay = data
      .filter(item => 
        item.brand_name === brandName && 
        item.date < monthData[0].date
      )
      .sort((a, b) => b.date.localeCompare(a.date))[0] // Latest date before this month
    
    let previousValue = prevMonthLastDay ? prevMonthLastDay[fieldName] || 0 : 0
    
    // Calculate differences for each day in the month
    monthData.forEach(item => {
      const currentValue = item[fieldName] || 0
      const diff = currentValue - previousValue
      
      // Only add positive differences (handle data corrections)
      if (diff >= 0) {
        totalDifference += diff
      }
      
      previousValue = currentValue
    })
    
    console.log(`📊 ${brandName} ${monthKey} ${metric} 차액 계산: ${totalDifference}`)
    
    return totalDifference
  }

  // Function to aggregate daily data to monthly
  const aggregateToMonthly = (data: DailyData[], metric: string): MonthlyData[] => {
    const monthlyMap: {[key: string]: {[brand: string]: number}} = {}
    
    // Get all unique months and brands
    const months = [...new Set(data.map(item => item.date.substring(0, 7)))].sort()
    const brandNames = [...new Set(data.map(item => item.brand_name))]
    
    months.forEach(monthKey => {
      monthlyMap[monthKey] = {}
      
      brandNames.forEach(brandName => {
        let value = 0
        
        if (metric === 'Video' || metric === 'Live') {
          // Use difference calculation for cumulative metrics
          value = calculateMonthlyDifferences(data, metric, monthKey, brandName)
        } else {
          // Use sum for non-cumulative metrics (GMV, Sales)
          const monthData = data.filter(item => 
            item.brand_name === brandName && 
            item.date.substring(0, 7) === monthKey
          )
          
          monthData.forEach(item => {
            switch (metric) {
              case 'GMV':
                value += item.gmv || 0
                break
              case 'Item solds':
                value += item.sales_count || 0
                break
              default:
                value += item.gmv || 0
            }
          })
        }
        
        monthlyMap[monthKey][brandName] = value
      })
    })
      
    // Convert object to array and sort
    const sortedMonths = Object.keys(monthlyMap).sort()
    
    // Exclude the first month for all metrics (no previous data for comparison)
    const monthsToShow = sortedMonths.slice(1) // 모든 메트릭에서 첫 달 제외
    
    return monthsToShow.map(monthKey => {
      const monthData: MonthlyData = {
        month: formatMonthForChart(monthKey)
      }
      
      // Apply ceiling to all brand values
      Object.keys(monthlyMap[monthKey]).forEach(brandName => {
        monthData[brandName] = Math.ceil(monthlyMap[monthKey][brandName])
      })
      
      return monthData
    })
  }

  // Month formatting (2025-05 → May 2025)
  const formatMonthForChart = (monthString: string): string => {
    const [year, month] = monthString.split('-')
    const monthNames = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ]
    
    // 현재 년월 확인
    const currentDate = new Date()
    const currentYear = currentDate.getFullYear().toString()
    const currentMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0')
    const currentYearMonth = `${currentYear}-${currentMonth}`
    
    const formattedMonth = `${monthNames[parseInt(month) - 1]} ${year}`
    
    // 당월인 경우 'ing' 배지 추가
    if (monthString === currentYearMonth) {
      return `${formattedMonth} (ing)`
    }
    
    return formattedMonth
  }

  const toggleBrand = (brand: string) => {
    const newHiddenBrands = new Set(hiddenBrands)
    if (newHiddenBrands.has(brand)) {
      newHiddenBrands.delete(brand)
    } else {
      newHiddenBrands.add(brand)
    }
    setHiddenBrands(newHiddenBrands)
  }

  const formatYAxis = (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`
    }
    return value.toString()
  }

  const formatTooltipValue = (value: number) => {
    if (activeTab === 'GMV') {
      return `$${value.toLocaleString()}`
    }
    return value.toLocaleString()
  }

  const formatCurrentValue = (brand: string) => {
    const value = currentValues[brand] || 0
    if (activeTab === 'GMV') {
      return `$${value.toLocaleString()}`
    }
    return value.toLocaleString()
  }

  const CustomDot = (props: any) => {
    const { cx, cy, stroke, payload, dataKey } = props
    if (hiddenBrands.has(dataKey)) return null
    
      return (
      <Dot 
          cx={cx}
          cy={cy}
        r={3} 
        fill={stroke} 
        stroke={stroke}
          strokeWidth={2}
        />
      )
    }

  const getCurrentMonthStatus = () => {
    const currentDate = new Date()
    const currentYear = currentDate.getFullYear()
    const currentMonth = currentDate.getMonth() + 1
    const currentYearMonth = `${currentYear}-${currentMonth.toString().padStart(2, '0')}`

    // 현재 월이 데이터에 포함되어 있는지 확인
    const hasCurrentMonthData = monthlyData.some(item => {
      const [year, month] = item.month.includes('(ing)') 
        ? item.month.replace(' (ing)', '').split(' ')
        : item.month.split(' ')
      
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
      const monthNumber = monthNames.indexOf(month) + 1
      const itemYearMonth = `${year}-${monthNumber.toString().padStart(2, '0')}`
      
      return itemYearMonth === currentYearMonth
    })

    return hasCurrentMonthData ? '(Current month in progress)' : ''
  }

  if (loading) {
    return (
      <div className="chart-wrapper">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Aggregating daily data to monthly...</p>
        </div>
        <style jsx>{`
          .chart-wrapper {
            width: 100%;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .loading-container {
            text-align: center;
            color: ${colors.schems.dark.onDisabled};
          }
          .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid ${colors.schems.dark.disabled};
            border-top: 3px solid ${colors.schems.dark.primary};
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    )
  }

  if (error) {
    return (
      <div className="chart-wrapper">
        <div className="error-container">
          <p>❌ {error}</p>
          <button onClick={() => window.location.reload()}>
            Retry
          </button>
        </div>
        <style jsx>{`
          .chart-wrapper {
            width: 100%;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .error-container {
            text-align: center;
            color: ${colors.schems.dark.onBase};
          }
          .error-container button {
            margin-top: ${schemes.spacing.md};
            padding: ${schemes.spacing.sm} ${schemes.spacing.md};
            background: ${colors.schems.dark.primary};
            color: ${colors.schems.dark.onPrimary};
            border: none;
            border-radius: ${schemes.radius.sm};
            cursor: pointer;
          }
        `}</style>
      </div>
    )
  }

  return (
    <div className="chart-wrapper">
      <div className="chart-header">
        <h3>Monthly Trends ({monthlyData.length} months)</h3>
        <span className="data-source">📊 Daily data → Monthly aggregation {getCurrentMonthStatus()}</span>
      </div>
      
      <div className="chart-area">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={monthlyData}
            margin={{ top: 5, right: 30, left: 50, bottom: 60 }}
          >
            <CartesianGrid 
              strokeDasharray="0" 
              stroke={colors.border.dark.bgLighten}
              strokeOpacity={0.3}
              vertical={true}
              horizontal={true}
            />
            <XAxis 
              dataKey="month" 
              stroke={colors.schems.dark.onDisabled}
              tick={{ fontSize: 10, fill: colors.schems.dark.onDisabled }}
              tickLine={false}
              axisLine={{ stroke: colors.border.dark.bgLighten }}
              height={60}
            />
            <YAxis 
              stroke={colors.schems.dark.onDisabled}
              tick={{ fontSize: 11, fill: colors.schems.dark.onDisabled }}
              tickLine={false}
              axisLine={{ stroke: colors.border.dark.bgLighten }}
              tickFormatter={formatYAxis}
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: colors.bg.dark.surfaceBright,
                border: `1px solid ${colors.border.dark.bgLighten}`,
                borderRadius: schemes.radius.sm,
                padding: schemes.spacing.md,
                fontSize: bodies.label.fontSize
              }}
              formatter={(value: number) => formatTooltipValue(value)}
              labelFormatter={(label) => `Month: ${label}`}
              cursor={{ stroke: colors.border.dark.bgLighten, strokeWidth: 1 }}
            />
            {brands.map((brand) => (
              <Line
                key={brand}
                type="linear"
                dataKey={brand}
                stroke={brandColors[brand as keyof typeof brandColors]}
                strokeWidth={2}
                dot={<CustomDot />}
                hide={hiddenBrands.has(brand)}
                activeDot={{ r: 6 }}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>
      </div>
      
      <div className="legend-area">
        <h3>Brand</h3>
        <div className="brand-list">
          {brands.map((brand) => (
            <div 
              key={brand} 
              className={`brand-item ${hiddenBrands.has(brand) ? 'hidden' : ''}`}
              onClick={() => toggleBrand(brand)}
            >
              <div 
                className="color-dot"
                style={{ backgroundColor: brandColors[brand as keyof typeof brandColors] }}
              />
              <span className="brand-name">{brand}</span>
              {currentValues[brand] && (
                <span className="current-value">
                  {formatCurrentValue(brand)}
                </span>
              )}
            </div>
          ))}
        </div>
      </div>
      
      <style jsx>{`
        .chart-wrapper {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
        }
        
        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: ${schemes.spacing.lg};
          padding-bottom: ${schemes.spacing.md};
          border-bottom: 1px solid ${colors.border.dark.bgLighten};
        }
        
        .chart-header h3 {
          color: ${colors.schems.dark.onBase};
          font-size: ${bodies.large.fontSize};
          font-weight: ${bodies.large.fontWeight};
          margin: 0;
        }
        
        .data-source {
          color: ${colors.schems.dark.onDisabled};
          font-size: ${bodies.label.fontSize};
        }
        
        .chart-area {
          flex: 1;
          height: 400px;
          margin-bottom: ${schemes.spacing.xl};
        }
        
        .legend-area {
          border-top: 1px solid ${colors.border.dark.bgLighten};
          padding-top: ${schemes.spacing.lg};
        }
        
        .legend-area h3 {
            color: ${colors.schems.dark.onBase};
          font-size: ${bodies.large.fontSize};
          font-weight: ${bodies.large.fontWeight};
          margin: 0 0 ${schemes.spacing.md} 0;
          }
          
          .brand-list {
            display: flex;
          flex-wrap: wrap;
            gap: ${schemes.spacing.md};
          }
          
          .brand-item {
            display: flex;
            align-items: center;
          gap: ${schemes.spacing.sm};
          padding: ${schemes.spacing.sm} ${schemes.spacing.md};
          background: ${colors.bg.dark.surfaceDim};
            border-radius: ${schemes.radius.sm};
            cursor: pointer;
            transition: all 0.2s ease;
          border: 1px solid transparent;
        }
        
        .brand-item:hover {
          background: ${colors.bg.dark.surfaceBright};
          border-color: ${colors.border.dark.bgLighten};
        }
        
        .brand-item.hidden {
          opacity: 0.5;
              background: ${colors.bg.dark.surface};
            }
            
            .color-dot {
              width: 12px;
              height: 12px;
              border-radius: 50%;
              flex-shrink: 0;
            }
            
            .brand-name {
              color: ${colors.schems.dark.onBase};
              font-size: ${bodies.body.fontSize};
          font-weight: ${bodies.body.fontWeight};
          min-width: 120px;
            }
            
            .current-value {
              color: ${colors.schems.dark.onDisabled};
              font-size: ${bodies.label.fontSize};
          font-weight: 600;
          margin-left: auto;
        }
      `}</style>
    </div>
  )
} 