import { useCallback, useMemo } from "react"
import { colors, fonts, schemes } from "@/utils/theme/style"
import DashboardApexBar from "@/components/charts/DashboardApexBar"
import { nFormatter } from "@/utils/util"

export default function DashboardMok() {
  const nowDate = useMemo(() => {
    return new Date()
  }, [])

  const dateRange = useMemo(() => {
    const before1Month1Day = `${nowDate.getUTCFullYear()}-${nowDate.getUTCMonth().toString().padStart(2, "0")}-${nowDate.getUTCDate().toString().padStart(2, "0")}`
    const before1Day = `${nowDate.getUTCFullYear()}-${(nowDate.getUTCMonth() + 1).toString().padStart(2, "0")}-${(nowDate.getUTCDate() - 1).toString().padStart(2, "0")}`

    return [before1Month1Day, before1Day]
  }, [nowDate])

  const dashboardData = useMemo(() => {
    return {
      funnel: [
        {
          stage: "Target Collab",
          key: "target_collab",
          count: 17700,
          conv: 100,
        },
        { stage: "Sample", key: "sample", count: 8400, conv: 47 },
        { stage: "Creator", key: "creator", count: 3477, conv: 43 },
        { stage: "Content", key: "content", count: 1034, conv: 29 },
      ],
      kpis: [
        { name: "GMV", value: 20000, isDollar: true, delta: 0.18 },
        { name: "Item Sold", value: 1347, isDollar: false, delta: -0.04 },
        { name: "AOV", value: 21.75, isDollar: true, delta: 0.0 },
        { name: "Views", value: 2164580, isDollar: false, delta: 0.11 },
        { name: "Comments", value: 10519, isDollar: false, delta: 0.08 },
        { name: "Saves", value: 5259, isDollar: false, delta: 0.07 },
      ],
    }
  }, [])

  const rateDisplay = useCallback((val: number) => {
    let delta: string = ""
    let color: string = ""
    if (val * 100 === 0) {
      delta = "-"
      color = "gray"
    } else if (val * 100 < 0) {
      delta = `${(val * 100).toLocaleString()}%`
      color = "blue"
    } else {
      delta = `+${(val * 100).toLocaleString()}%`
      color = "red"
    }
    return (
      <>
        <small className={color}>{delta}</small>
        <style jsx>{`
          small {
            font-size: ${fonts.body.body.fontSize};
            font-weight: ${fonts.body.body.fontWeight};
            line-height: ${fonts.body.body.lineHeight};

            &.red {
              color: ${colors.sys.dark.red.default};
            }

            &.blue {
              color: ${colors.sys.dark.blue.default};
            }

            &.gray {
              color: ${colors.sys.dark.gray.default};
            }
          }
        `}</style>
      </>
    )
  }, [])

  return (
    <>
      <main>
        <div>
          <div>
            <div>
              <h4>Funnel</h4>
              <div>
                <span>Last 30 days</span>
                <span>
                  {dateRange[0]} ~ {dateRange[1]}
                </span>
              </div>
            </div>
            <div>
              <DashboardApexBar
                series={[
                  {
                    name: "Funnel",
                    data: dashboardData["funnel"].map((funnel) => funnel.count),
                    group: "funnel",
                  },
                ]}
                categories={dashboardData["funnel"].map(
                  (funnel) => funnel.stage
                )}
                width={"768px"}
                height={"400px"}
              />
              <div>
                {dashboardData["funnel"].map((funnel) => (
                  <div key={funnel.stage}>
                    <p>{nFormatter(funnel.count)}</p>
                    <small>{funnel.conv}%</small>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div>
            {dashboardData["kpis"].map((item, i) => (
              <div key={i}>
                <div>
                  <span>{item.name}</span>
                </div>
                <div>
                  <span>
                    {item.isDollar ? "$" : ""}
                    {item.value.toLocaleString()}
                  </span>
                  {rateDisplay(item.delta)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
      <style jsx>{`
        main {
          min-height: 100vh;
          padding: ${schemes.spacing["5xl"]};
          > div {
            width: 60rem;
            margin: 0 auto;
            > div:first-of-type {
              background: ${colors.bg.dark.surface};
              border-radius: ${schemes.radius["2xl"]};
              background: ${colors.bg.dark.surface};
              > div:first-of-type {
                padding: ${schemes.spacing["2xl"]} ${schemes.spacing["3xl"]};
                border-bottom: 1px solid ${colors.border.dark.default};
                display: flex;
                justify-content: space-between;
                align-items: center;
                > h4 {
                  margin: 0;
                  font-size: ${fonts.heading.h4.fontSize};
                  font-weight: ${fonts.heading.h4.fontWeight};
                  line-height: ${fonts.heading.h4.lineHeight};
                }
                > h4 + div {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  gap: ${schemes.spacing.md};
                  > span {
                    font-size: ${fonts.body.body.fontSize};
                    font-weight: ${fonts.body.body.fontWeight};
                    line-height: ${fonts.body.body.lineHeight};
                    &:last-of-type {
                      border-radius: ${schemes.radius.sm};
                      background: ${colors.schems.dark.secondary};
                      padding: 0 ${schemes.spacing.md};
                    }
                  }
                }
              }
              > div:last-of-type {
                padding: ${schemes.spacing["3xl"]} 0;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                gap: ${schemes.spacing["3xl"]};
                > div:last-of-type {
                  width: 625.54px;
                  display: flex;
                  justify-content: space-between;
                  gap: ${schemes.spacing.md};
                  > div {
                    width: 130px;
                    background: ${colors.schems.dark.onBase};
                    padding: ${schemes.spacing.md} ${schemes.spacing["2xl"]};
                    border-radius: ${schemes.radius.sm};
                    text-align: center;
                    > p {
                      margin: 0;
                      color: ${colors.schems.dark.base};
                      font-size: ${fonts.body.bodyBold.fontSize};
                      font-weight: ${fonts.body.bodyBold.fontWeight};
                      line-height: ${fonts.body.bodyBold.lineHeight};
                    }
                    > small {
                      color: ${colors.schems.dark.disabled};
                      font-size: ${fonts.body.label.fontSize};
                      font-weight: ${fonts.body.label.fontWeight};
                      line-height: ${fonts.body.label.lineHeight};
                    }
                  }
                }
              }
            }
            > div:last-of-type {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: ${schemes.spacing["2xl"]};
              margin-top: ${schemes.spacing["2xl"]};
              > div {
                background: ${colors.bg.dark.surface};
                border-radius: ${schemes.radius.md};
                background: ${colors.bg.dark.surface};
                padding: ${schemes.spacing["4xl"]} ${schemes.spacing["2xl"]};
                > div {
                  text-align: center;
                  color: ${colors.schems.dark.onDisabled};
                  font-size: ${fonts.body.large.fontSize};
                  font-weight: ${fonts.body.large.fontWeight};
                  line-height: ${fonts.body.large.lineHeight};
                  &:last-of-type {
                    margin-top: ${schemes.spacing.md};
                    display: flex;
                    flex-direction: column;
                    > span {
                      color: ${colors.schems.dark.onBase};
                      font-size: ${fonts.heading.h3.fontSize};
                      font-weight: ${fonts.heading.h3.fontWeight};
                      line-height: ${fonts.heading.h3.lineHeight};
                    }
                  }
                }
              }
            }
          }
        }
      `}</style>
    </>
  )
}
