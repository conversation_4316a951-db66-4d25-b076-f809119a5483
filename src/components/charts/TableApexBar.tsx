import { colors } from "@/utils/theme/style"
import React, { useMemo } from "react"
import ReactApexChart from "react-apexcharts"

export const TableApexBar = ({
  data,
  width = "100%",
  height = "100%",
}: {
  data: number[]
  width?: string
  height?: string
}) => {
  const average = Math.round(data.reduce((a, b) => a + b, 0) / data.length)
  const options = useMemo(() => {
    return {
      chart: {
        defaultLocale: "en",
        width: `${width}`,
        height: `${height}`,
        offsetX: 0,
        offsetY: 0,
        parentHeightOffset: 0,
        dropShadow: {
          enabled: false,
        },
        toolbar: {
          show: false,
        },
        legend: {
          show: false,
          offsetX: 0,
          offsetY: 0,
        },
        zoom: {
          enabled: false,
          autoScaleYaxis: false,
          allowMouseWheelZoom: false,
        },
      },
      tooltip: {
        enabled: false,
      },
      colors: [
        function ({
          value,
          seriesIndex,
          w,
        }: {
          value: number
          seriesIndex: number
          w: number
        }) {
          if (value < average) {
            return `${colors.schems.dark.onDisabled}`
          } else {
            return `${colors.sys.dark.red.darken}`
          }
        },
      ],
      grid: {
        show: false,
        padding: {
          top: 0,
          right: 0,
          bottom: 0,
          left: 0,
        },
      },
      yaxis: {
        show: false,
        tooltip: {
          enabled: false,
        },
      },
      xaxis: {
        labels: { show: false },
        axisTicks: { show: false },
        axisBorder: { show: false },
        tooltip: {
          enabled: false,
        },
      },
      dataLabels: {
        enabled: false,
      },
      plotOptions: {
        bar: {
          borderRadius: 1,
          borderRadiusApplication: "around" as "around" | "end" | undefined,
        },
      },
    }
  }, [average, height, width])

  const series = useMemo(() => {
    return [
      {
        data: data,
      },
    ]
  }, [data])

  return <ReactApexChart options={options} series={series} type={"bar"} />
}
