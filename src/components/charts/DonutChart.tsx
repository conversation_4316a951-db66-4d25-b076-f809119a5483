import { colors } from "@/utils/theme/style"
import React, { useMemo } from "react"

function DonutChart({
  data,
  size = "5rem",
  innerSize = "2.5rem",
  background = `${colors.bg.dark.surfaceBright}`,
}: {
  data: any[]
  size?: string
  innerSize?: string
  background?: string
}) {
  const chartGradientString = useMemo(() => {
    let sumDig = 0
    const parseData = data.map((val) => {
      const thisDig = sumDig + (360 * val.value) / 100
      const returnVal = `${val.color} ${sumDig}deg ${thisDig}deg`
      sumDig = thisDig
      return returnVal
    })

    return parseData.join(",")
  }, [data])

  return (
    <>
      <div className={"chart"}>
        <div className={"chart-bar"}></div>
        <div className={"inner-circle"}></div>
      </div>
      <style jsx>{`
        .chart {
          position: relative;
          width: ${size};
          height: ${size};
          border-radius: 50%;
        }
        .inner-circle {
          background: ${background};
          position: absolute;
          top: 50%;
          left: 50%;
          width: ${innerSize};
          height: ${innerSize};
          border-radius: 50%;
          transform: translate(-50%, -50%);
        }
        .chart-bar {
          width: inherit;
          height: inherit;
          border-radius: 50%;
          background: conic-gradient(${chartGradientString});
        }
      `}</style>
    </>
  )
}

export default React.memo(DonutChart)
