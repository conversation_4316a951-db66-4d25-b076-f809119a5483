import React, { useMemo } from "react"
import ReactApex<PERSON>hart from "react-apexcharts"
import { colors, fonts } from "@/utils/theme/style"
import { ApexOptions } from "apexcharts"
import { nFormatter } from "@/utils/util"

function DashboardApexBar({
  series,
  categories,
  width = "100%",
  height = "80%",
}: {
  series: any[]
  categories: string[]
  width?: string
  height?: string
}) {
  const options = useMemo(() => {
    return {
      chart: {
        offsetX: 0,
        offsetY: 0,
        parentHeightOffset: 0,
        toolbar: {
          show: false,
        },
      },
      plotOptions: {
        bar: {
          columnWidth: 130,
          borderRadius: 8,
          borderRadiusApplication: "end",
        },
      },
      dataLabels: {
        enabled: false,
      },
      grid: {
        show: false,
      },
      tooltip: {
        enabled: true,
        theme: "dark",
        x: {
          formatter: () => "",
        },
        y: {
          formatter: undefined,
          title: {
            formatter: () => "",
          },
        },
      },
      xaxis: {
        categories: categories,
        labels: {
          style: {
            fontSize: fonts.body.bodyBold.fontSize,
            fontWeight: fonts.body.bodyBold.fontWeight,
            colors: colors.schems.dark.onBase,
          },
        },
      },
      yaxis: {
        labels: {
          formatter: (value: number) => nFormatter(value),
          style: {
            fontSize: fonts.body.label.fontSize,
            fontWeight: fonts.body.label.fontWeight,
            colors: colors.schems.dark.onDisabled,
          },
        },
      },
      colors: [`${colors.schems.dark.primary}`],
    } as ApexOptions
  }, [categories])

  return (
    <>
      <div className={"dashboard-bar"}>
        <ReactApexChart
          width={width}
          height={height}
          options={options}
          series={series}
          type={"bar"}
        />
      </div>
    </>
  )
}

export default React.memo(DashboardApexBar)
