import FilterFold from "@/components/filters/FilterFold"
import Button from "@/components/elements/Button"
import { colors, fonts, schemes } from "@/utils/theme/style"
import React, { useCallback, useEffect, useRef, useState } from "react"
import useSearchCreator from "@/store/useSearchCreator"
import { SearchFilterType } from "@/common/types/searchFilterType"
import { FilterType } from "@/common/enums/filterType"
import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"

function Filters({
  type,
  search,
  isTabs = false,
}: {
  type: FilterType
  search: (page: number) => void
  isTabs?: boolean
}) {
  const searchFilter = useSearchCreator((state) => state.searchFilters)
  const searchKeyword = useSearchCreator((state) => state.keyword)
  const setKeyword = useSearchCreator((state) => state.setKeyword)
  const searchProduct = useSearchCreator((state) => state.product)
  const setProduct = useSearchCreator((state) => state.setProduct)
  const getSearchFilter = useSearchCreator((state) => state.getSearchFilter)
  const resetSelectedFilters = useSearchCreator((state) =>
    type === FilterType.explore
      ? state.resetSelectedFilters
      : state.resetSavedSelectedFilters
  )
  const textAreaRef = useRef<HTMLTextAreaElement>(null)
  const textAreaRef2 = useRef<HTMLTextAreaElement>(null)
  const [ScrollY, setScrollY] = useState<boolean>(false)

  const handleResizeHeight = (current: any) => {
    if (current) {
      current.style.height = "auto"
      current.style.height = current.scrollHeight + "px"
    }
  }

  const textAreaTrim = useCallback(
    (val: string, setSearch: (text: string) => void) => {
      const regex = /\s/gi
      const trimmed = val.replace(regex, "")
      setSearch(trimmed)
      return trimmed
    },
    []
  )

  const textAreaNewLineTrim = useCallback(
    (val: string, setSearch: (text: string) => void) => {
      const regex = /\n/gi
      const trimmed = val.replace(regex, "")
      setSearch(trimmed)
      return trimmed
    },
    []
  )

  useEffect(() => {
    getSearchFilter()
  }, [getSearchFilter])

  const handleClickReset = () => {
    resetSelectedFilters()
    setKeyword("")
    setProduct("")
  }

  const handleFollow = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollY(
      e.currentTarget.scrollTop >=
        e.currentTarget.scrollHeight - e.currentTarget.clientHeight
    )
  }

  return (
    <>
      <div className={"filter"}>
        <div onScroll={handleFollow}>
          <div className={"search"}>
            <div>
              <h4>search</h4>
            </div>
            <div className={"search-input"}>
              <label htmlFor={"textArea"}>
                <Icon
                  path={getAssetPath("search.svg")}
                  color={colors.sys.dark.gray.default}
                  size={"1rem"}
                />
              </label>
              <textarea
                id={"textArea"}
                placeholder={"Tiktok ID, Email"}
                rows={1}
                ref={textAreaRef}
                value={searchKeyword}
                onKeyUp={(e) => {
                  if (e.key === "Enter") {
                    search(1)
                  }
                }}
                onChange={(e) => {
                  e.target.value = textAreaTrim(e.target.value, setKeyword)
                  handleResizeHeight(textAreaRef.current)
                }}
                onBlur={(e) => {
                  e.target.value = textAreaTrim(e.target.value, setKeyword)
                }}
              ></textarea>
            </div>
          </div>
          <div className={"search"}>
            <div>
              <h4>Sales Item</h4>
            </div>
            <div className={"search-input"}>
              <label htmlFor={"textArea"}>
                <Icon
                  path={getAssetPath("search.svg")}
                  color={colors.sys.dark.gray.default}
                  size={"1rem"}
                />
              </label>
              <textarea
                id={"textArea"}
                placeholder={"Search Sales Item"}
                rows={1}
                ref={textAreaRef2}
                value={searchProduct}
                onKeyUp={(e) => {
                  if (e.key === "Enter") {
                    search(1)
                  }
                }}
                onChange={(e) => {
                  e.target.value = textAreaNewLineTrim(
                    e.target.value,
                    setProduct
                  )
                  handleResizeHeight(textAreaRef2.current)
                }}
                onBlur={(e) => {
                  e.target.value = textAreaNewLineTrim(
                    e.target.value,
                    setProduct
                  )
                }}
              ></textarea>
            </div>
          </div>
          {searchFilter && Array.isArray(searchFilter) &&
            searchFilter.map((filter: SearchFilterType) => (
              <FilterFold filter={filter} key={filter.id} type={type} />
            ))}
        </div>
        <div
          className={"more"}
          style={ScrollY ? { display: "none" } : { display: "flex" }}
        >
          <Icon
            path={getAssetPath("arrow_cool_down.svg")}
            color={colors.schems.dark.onDisabled}
            size={"1rem"}
          />
          <span>More..</span>
        </div>
      </div>
      <div className={"buttons"}>
        <div>
          <Button style={"secondary"} onClick={handleClickReset}>
            Clear all
          </Button>
        </div>
        <div>
          <Button
            full={true}
            onClick={() => {
              search(1)
            }}
          >
            Apply Filters
          </Button>
        </div>
      </div>
      <style jsx>{`
        .filter {
          position: relative;
          flex: 1;

          > div:first-child {
            overflow-y: auto;
            height: calc(100vh - ${isTabs ? "9.5rem" : "6rem"});
            padding: ${schemes.spacing["3xl"]};
            display: flex;
            flex-direction: column;
            gap: ${schemes.spacing["2xl"]};
            -ms-overflow-style: none;

            &::-webkit-scrollbar {
              display: none;
            }
          }

          > .more {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 5rem;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: ${schemes.spacing.sm};
            padding: 2.69rem 0 1rem;
            background: linear-gradient(
              0deg,
              ${colors.bg.dark.surface} 25%,
              rgba(24, 24, 27, 0) 100%
            );

            > span {
              color: ${colors.schems.dark.onDisabled};
              font-size: ${fonts.body.label.fontSize};
              font-weight: ${fonts.body.label.fontWeight};
              line-height: ${fonts.body.label.lineHeight};
            }
          }

          .search {
            h4 {
              font-size: ${fonts.body.label.fontSize};
              font-weight: ${fonts.body.label.fontWeight};
              line-height: ${fonts.body.label.lineHeight};
            }

            .search-input {
              display: flex;
              min-height: 2.5rem;
              justify-content: center;
              gap: ${schemes.spacing.sm};
              padding: 0 ${schemes.spacing.md};
              border-radius: ${schemes.spacing.sm};
              border: 1px solid ${colors.border.dark.default};
              background: ${colors.interactive.dark.hover};

              &:has(:focus) {
                border: 1px solid ${colors.border.dark.primary};
              }

              > label {
                width: 1rem;
                display: flex;
                align-items: center;
              }

              > textarea {
                width: 100%;
                max-height: 187px;
                height: auto;
                border: none;
                background: none;
                padding: 0.47rem;

                color: ${colors.schems.dark.onDisabled};
                font-family: ${fonts.heading.fontFamily};
                font-size: ${fonts.body.body.fontSize};
                font-weight: ${fonts.body.body.fontWeight};
                line-height: ${fonts.body.body.lineHeight};
                resize: none;

                &:focus-visible {
                  outline: none;
                }
              }
            }
          }
        }

        .buttons {
          flex-shrink: 0;
          display: flex;
          height: 6rem;
          gap: ${schemes.spacing.md};
          padding: ${schemes.spacing["2xl"]} ${schemes.spacing["3xl"]};
          border-top: ${schemes.borderWidth.md} solid
            ${colors.border.dark.default};

          & > div:last-of-type {
            flex: 1;
          }
        }
      `}</style>
    </>
  )
}

export default React.memo(Filters)
