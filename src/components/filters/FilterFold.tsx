import Icon from "@/components/elements/Icon"
import { colors, fonts, schemes } from "@/utils/theme/style"
import React, { useState } from "react"
import FilterBundle from "@/components/filters/FilterBundle"
import { getAssetPath } from "@/utils/util"
import { SearchFilterType } from "@/common/types/searchFilterType"
import { FilterType } from "@/common/enums/filterType"

function FilterFold({
  filter,
  type,
}: {
  filter: SearchFilterType
  type: FilterType
}) {
  const [checked, setChecked] = useState<boolean>(true)
  const randNum = Math.floor(Math.random() * 10000)

  // subCategories가 undefined일 경우를 대비한 방어 코드
  const subCategories = filter?.subCategories || []

  return (
    <>
      <div className={"category"}>
        <input
          type="checkbox"
          checked={checked}
          onChange={(e) => {
            setChecked(e.target.checked)
          }}
          id={`fold-${randNum}`}
        />
        <label htmlFor={`fold-${randNum}`}>
          <h4>{filter?.name || '필터'}</h4>
          <Icon
            path={
              checked
                ? getAssetPath("line_arrow_up.svg")
                : getAssetPath("line_arrow_down.svg")
            }
            color={colors.schems.dark.disabled}
          />
        </label>
        <div>
          {subCategories.length > 0 &&
            subCategories.map((subCategory: SearchFilterType) => (
              <FilterBundle
                searchFilter={subCategory}
                key={subCategory.id}
                type={type}
              />
            ))}
        </div>
      </div>
      <style jsx>{`
        input {
          display: none;
          & + label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: ${schemes.spacing.sm};
            height: 2.5rem;
            & > h4 {
              color: ${colors.schems.dark.onDisabled};
              font-size: ${fonts.body.label.fontSize};
              font-weight: ${fonts.body.label.fontWeight};
            }
            & + div {
              border-radius: ${schemes.radius.sm};
              background: ${colors.bg.dark.surface};
              div:not(:last-of-type) {
                border-bottom: 1px solid ${colors.border.dark.bgLighten};
              }
            }
          }
          &:not(:checked) {
            & ~ div {
              display: none;
            }
          }
        }
      `}</style>
    </>
  )
}
export default React.memo(FilterFold)
