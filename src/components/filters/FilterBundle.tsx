import { colors, fonts, schemes } from "@/utils/theme/style"
import React, { useCallback, useEffect, useMemo, useState } from "react"
import RangeBar from "@/components/elements/RangeBar"
import InputButtons from "@/components/elements/InputButtons"
import Checkbox from "@/components/elements/Checkbox"
import { shuffle } from "@/utils/util"
import CheckButton from "@/components/elements/CheckButton"
import SearchSelect from "@/components/elements/SearchSelect"
import { ColorType } from "@/common/types/colorType"
import {
  SearchFilterType,
  SelectedFilterValuesType,
} from "@/common/types/searchFilterType"
import { FilterType } from "@/common/enums/filterType"
import useSearchCreator from "@/store/useSearchCreator"
import Input2 from "@/components/elements/Input2"

function FilterBundle({
  searchFilter,
  type,
}: {
  searchFilter: SearchFilterType
  type: FilterType
}) {
  const searchSelectedFilters = useSearchCreator((state) =>
    type === FilterType.explore
      ? state.selectedFilters
      : state.savedSelectedFilters
  )
  const setSelectedFilters = useSearchCreator((state) =>
    type === FilterType.explore
      ? state.setSelectedFilters
      : state.setSavedSelectedFilters
  )

  const min = useMemo(() => searchFilter.values.min, [searchFilter])
  const max = useMemo(() => searchFilter.values.max, [searchFilter])
  const [allChecked, setAllChecked] = useState<boolean>(false)
  const [radioChecked, setRadioChecked] = useState<string | null>(null)
  const colorsArr: ColorType[] = [
    "red",
    "orange",
    "yellow",
    "green",
    "cyan",
    "blue",
    "indigo",
    "purple",
    "pink",
  ]
  const [shuffleColor, setShuffleColor] = useState<ColorType[]>([])

  const colorFixedCheck = useCallback(
    (value: string, index: number) => {
      switch (value) {
        case "Etc":
          return "gray"
        case "Female":
          return "red"
        case "Male":
          return "blue"
        case "Other":
          return undefined
        default:
          return shuffleColor[index % shuffleColor.length]
      }
    },
    [shuffleColor]
  )

  useEffect(() => {
    if (searchSelectedFilters.length === 0) {
      setRadioChecked(null)
    }
  }, [searchSelectedFilters])

  const currentValues = useMemo(() => {
    const values = searchSelectedFilters?.find(
      (filter) => filter.key === searchFilter.key
    )?.values
    if (Array.isArray(values)) {
      return values || []
    } else {
      const minVal: number = values?.min ?? min
      const maxVal: number = values?.max ?? max
      return !minVal && !maxVal
        ? null
        : ({ min: minVal, max: maxVal } as SelectedFilterValuesType)
    }
  }, [searchSelectedFilters, searchFilter.key, min, max])

  const checkboxAction = (values: any) => {
    const prevValue =
      (searchSelectedFilters?.find((filter) => filter.key === searchFilter.key)
        ?.values as string[]) || []
    const checkedData = JSON.parse(values.data)
    if (values.checked) {
      setSelectedFilters({
        key: searchFilter.key,
        values: [...prevValue, checkedData],
      })
    } else {
      setSelectedFilters({
        key: searchFilter.key,
        values: [
          ...prevValue.filter((obj) => {
            if (typeof obj === "object") {
              return JSON.stringify(obj) !== checkedData
            } else {
              return obj !== checkedData
            }
          }),
        ],
      })
    }
  }

  const buttonsAction = (values: any) => {
    const prevValue =
      (searchSelectedFilters?.find((filter) => filter.key === searchFilter.key)
        ?.values as string[]) || []
    const updateValue = values.checked
      ? [...prevValue, values.data]
      : [
          ...prevValue.filter((obj) => {
            return obj !== values.data
          }),
        ]
    setAllChecked(updateValue.length === searchFilter.values.length)
    setSelectedFilters({
      key: searchFilter.key,
      values: updateValue,
    })
  }

  const radioRangeButtonAction = (obj: any) => {
    setRadioChecked(JSON.stringify(obj.data))
    if (obj.checked) {
      const data = obj.data
      const values: { min?: number; max?: number } = {}
      if (data.min !== undefined) values.min = data.min
      if (data.max !== undefined) values.max = data.max
      if (Object.keys(values).length) {
        setSelectedFilters({
          key: searchFilter.key,
          values: values,
        })
      }
    }
  }

  const setMaxValue = (maxValue: number) => {
    setRadioChecked("")
    const prevValue =
      (searchSelectedFilters?.find((filter) => filter.key === searchFilter.key)
        ?.values as object) || {}
    setSelectedFilters({
      key: searchFilter.key,
      values: {
        ...prevValue,
        max: maxValue,
      },
    })
  }

  const setMinValue = (minValue: number) => {
    const prevValue =
      (searchSelectedFilters?.find((filter) => filter.key === searchFilter.key)
        ?.values as object) || {}
    setSelectedFilters({
      key: searchFilter.key,
      values: {
        ...prevValue,
        min: minValue,
      },
    })
  }

  const searchSelectAction = (checked: boolean, value: string) => {
    const prevValue =
      (searchSelectedFilters?.find((filter) => filter.key === searchFilter.key)
        ?.values as string[]) || []
    if (checked) {
      setSelectedFilters({
        key: searchFilter.key,
        values: [...prevValue, value],
      })
    } else {
      setSelectedFilters({
        key: searchFilter.key,
        values: [
          ...prevValue.filter((obj) => {
            return obj !== value
          }),
        ],
      })
    }
  }

  const checkAllAction = (value: any) => {
    setSelectedFilters({
      key: searchFilter.key,
      values: value.checked ? searchFilter.values : [],
    })
    setAllChecked((prev) => !prev)
  }

  useEffect(() => {
    setShuffleColor(shuffle(colorsArr))
  }, [])

  return (
    <>
      <div className="bundle">
        <div>
          <h5>{searchFilter.name}</h5>
          <p>{searchFilter.values.label}</p>
        </div>
        {searchFilter.filterType === "ranges_with_button" && (
          <div className={"range-buttons"}>
            <InputButtons
              key={searchFilter.key}
              data={searchFilter.values.ranges}
              action={radioRangeButtonAction}
              col={2}
              checkValues={radioChecked}
            />
          </div>
        )}
        {(searchFilter.filterType === "ranges_with_button" ||
          searchFilter.filterType === "ranges") && (
          <div>
            <div className="slider">
              <RangeBar
                min={min}
                max={max}
                step={searchFilter.values.unit}
                minVal={Array.isArray(currentValues) ? min : currentValues?.min}
                setMinVal={setMinValue}
                maxVal={Array.isArray(currentValues) ? max : currentValues?.max}
                setMaxVal={setMaxValue}
              />
            </div>
            <div className={"range-input"}>
              <div>
                <label>Min</label>
                <Input2
                  type={"number"}
                  value={
                    Array.isArray(currentValues)
                      ? min.toString()
                      : currentValues?.min?.toString() || "0"
                  }
                  number={{
                    min: 0,
                    max: Array.isArray(currentValues)
                      ? max
                      : currentValues?.max,
                    step: searchFilter.values.unit,
                  }}
                  placeholderText={"Put number"}
                  onChange={(e) => setMinValue(Number(e.target.value))}
                  onBlur={(e) => {
                    if (!Array.isArray(currentValues)) {
                      if (
                        currentValues?.max &&
                        Number(e.target.value) > currentValues?.max
                      ) {
                        setMinValue(currentValues?.max)
                      }
                    }
                  }}
                />
              </div>
              <div>
                <label>Max</label>
                <Input2
                  type={"number"}
                  value={
                    Array.isArray(currentValues)
                      ? max.toString()
                      : currentValues?.max?.toString() || "0"
                  }
                  number={{
                    max: max,
                    step: searchFilter.values.unit,
                  }}
                  placeholderText={"Put number"}
                  onChange={(e) => setMaxValue(Number(e.target.value))}
                  onBlur={(e) => {
                    if (!Array.isArray(currentValues)) {
                      if (
                        currentValues?.min &&
                        Number(e.target.value) < currentValues?.min
                      ) {
                        setMaxValue(currentValues?.min)
                      }
                    }
                  }}
                />
              </div>
            </div>
          </div>
        )}
        {searchFilter.filterType === "search_category" && (
          <SearchSelect
            list={searchFilter.values}
            pickList={Array.isArray(currentValues) ? currentValues : []}
            action={searchSelectAction}
          />
        )}
        {searchFilter.filterType === "buttons" && (
          <div className={"button-groups"}>
            <div>
              <CheckButton
                type={"checkbox"}
                inputName={"all"}
                checked={allChecked}
                data={"all"}
                action={checkAllAction}
              >
                All
              </CheckButton>
            </div>
            <div>
              <InputButtons
                key={searchFilter.key}
                col={2}
                type={"checkbox"}
                data={searchFilter.values}
                action={buttonsAction}
                checkValues={currentValues as string[]}
              />
            </div>
          </div>
        )}
        {searchFilter.filterType === "checkboxs" && (
          <ul className={"checkboxes"}>
            {searchFilter.values.map((value: any, index: number) => (
              <li key={value}>
                <Checkbox
                  color={
                    searchFilter.name !== "Race"
                      ? colorFixedCheck(value, index)
                      : undefined
                  }
                  data={value}
                  action={checkboxAction}
                  isChecked={
                    Array.isArray(currentValues)
                      ? currentValues.includes(value)
                      : currentValues === value
                  }
                >
                  {value}
                </Checkbox>
              </li>
            ))}
          </ul>
        )}
      </div>
      <style jsx>{`
        .bundle {
          display: flex;
          flex-direction: column;
          gap: ${schemes.spacing.md};
          padding: ${schemes.spacing["2xl"]};
          & > div:first-of-type {
            display: flex;
            justify-content: space-between;
            align-items: center;
            & > h5 {
              margin: 0;
              font-size: ${fonts.body.labelBold.fontSize};
              font-weight: ${fonts.body.labelBold.fontWeight};
              line-height: ${fonts.body.labelBold.lineHeight};
            }
            & > p {
              font-size: ${fonts.body.body.fontSize};
              font-weight: ${fonts.body.body.fontWeight};
              line-height: ${fonts.body.body.lineHeight};
            }
          }
          & div {
            border: 0 !important;
          }

          & .range-buttons {
            margin-bottom: ${schemes.spacing["2xl"]};
          }
          & .slider {
            height: 1rem;
            display: flex;
            align-items: center;
          }
          & .range-input {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: ${schemes.spacing.md};
            & > div {
              flex: 1;
              &:last-of-type {
                text-align: right;
                input {
                  text-align: right;
                }
              }
              & label {
                display: inline-block;
                margin: ${schemes.spacing.sm};
                color: ${colors.schems.dark.onDisabled};
                font-size: ${fonts.body.label.fontSize};
                font-weight: ${fonts.body.label.fontWeight};
                line-height: ${fonts.body.label.lineHeight};
              }
            }
          }
          & .button-groups {
            display: flex;
            flex-direction: column;
            gap: ${schemes.spacing.md};
          }
          & .checkboxes {
            margin: 0;
            padding: 0;
            list-style: none;
            display: flex;
            flex-direction: column;
            gap: ${schemes.spacing.md};
          }
        }
      `}</style>
    </>
  )
}

export default React.memo(FilterBundle)
