"use client"

import React, { useEffect, useState } from "react"
import { colors, fonts, schemes } from "@/utils/theme/style"
import Icon from "@/components/elements/Icon"
import { usePathname, useRouter } from "next/navigation"
import { getAssetPath } from "@/utils/util"
import useUserInfo from "@/store/useUserInfo"
import { useAuth } from "@/common/hooks/useAuthProvider"

const Navs = [
  {
    name: "Home",
    icon: {
      on: getAssetPath("space-dashboard_true.svg"),
      off: getAssetPath("space-dashboard_false.svg"),
    },
    link: "/home",
    view:
      process.env.PROJECT_ENV === "production"
        ? ["0820978d-8bc0-4f6a-afcb-d24ac277d403"]
        : [
            "e828d279-4c20-4dc3-a37c-122095f274b1",
            "5924d06a-3868-416b-8fbb-0ce9a2894e1f",
          ],
  },
  {
    name: "Explore",
    icon: {
      on: getAssetPath("explore_true.svg"),
      off: getAssetPath("explore_false.svg"),
    },
    link: "/explore",
  },
  {
    name: "Saved",
    icon: {
      on: getAssetPath("bookmark_true.svg"),
      off: getAssetPath("bookmark_false.svg"),
    },
    link: "/saved",
  },
  {
    name: "Campaign",
    icon: {
      on: getAssetPath("toy_true.svg"),
      off: getAssetPath("toy_false.svg"),
    },
    link: "/campaign",
  },
  {
    name: "Competitors",
    icon: {
      on: getAssetPath("lightbulb.svg"),
      off: getAssetPath("lightbulb.svg"),
    },
    link: "/competitors",
  },
]

const NavPage = ({ children }: { children: React.ReactNode }) => {
  const auth = useAuth()
  const { userInfo } = useUserInfo()
  const router = useRouter()
  const pathname = usePathname()
  const [expires, setExpires] = useState<string>("")
  const [navOpen, setNavOpen] = useState(false)
  const [profileOpen, setProfileOpen] = useState(false)
  const hiddenNavePage = ["/signin", "/signup", "/resend-verify", "/onboarding"]

  useEffect(() => {
    const date =
      auth.session?.expires_at && new Date(auth.session?.expires_at * 1000)
    if (date) {
      setExpires(date.toLocaleString())
    }
  }, [auth.session])

  useEffect(() => {
    if (!hiddenNavePage.includes(pathname)) {
      setNavOpen(true)
    } else {
      setNavOpen(false)
    }
  }, [pathname, auth])

  return (
    <>
      {auth.session?.access_token && navOpen && (
        <nav>
          <div>
            {Navs.map((nav) => {
              if (
                !Object.hasOwn(nav, "view") ||
                (Object.hasOwn(nav, "view") &&
                  nav?.view &&
                  userInfo?.user_id &&
                  nav.view.includes(userInfo.user_id))
              ) {
                return (
                  <button
                    className={`${pathname === nav.link && "active"}`}
                    key={nav.name}
                    onClick={() => {
                      router.push(nav.link)
                    }}
                  >
                    <div>
                      <Icon
                        path={
                          pathname === nav.link ? nav.icon.on : nav.icon.off
                        }
                        color={
                          pathname === nav.link
                            ? colors.schems.dark.onSecondary
                            : colors.schems.dark.onBase
                        }
                      />
                    </div>
                    <h5>{nav.name}</h5>
                  </button>
                )
              }
            })}
          </div>
          <div>
            <button onClick={() => setProfileOpen(!profileOpen)}>
              <div>
                <span>{userInfo?.full_name?.[0]?.toUpperCase()}</span>
              </div>
              <h5>Profile</h5>
            </button>
            <div className={`${profileOpen ? "active" : ""}`}>
              <div
                onClick={() => {
                  setProfileOpen(false)
                  auth.signOut()
                }}
              >
                <span>Logout</span>
              </div>
            </div>
          </div>
        </nav>
      )}
      <main>{children}</main>
      <style jsx>{`
        nav {
          z-index: 10;
          position: absolute;
          left: 0;
          top: 0;
          width: 6rem;
          height: 100vh;
          border-right: ${schemes.borderWidth.md} solid
            ${colors.border.dark.default};
          background:
            linear-gradient(
              180deg,
              ${colors.sys.dark.blue.darken} 0%,
              ${colors.bg.dark.surface} 25%
            ),
            ${colors.bg.dark.surface};

          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;
          & > div {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: start;
            width: 100%;
            padding: ${schemes.spacing["3xl"]} ${schemes.spacing.md};
            &:first-child {
              & > button {
                width: 100%;
                background: none;
                border: none;
                padding: ${schemes.spacing.md};
                border-radius: ${schemes.spacing.md};
                &:hover {
                  filter: brightness(1.12);
                  backdrop-filter: brightness(1.12);
                }
                &:active {
                  filter: brightness(1.16);
                  backdrop-filter: brightness(1.16);
                }
                & > div {
                  display: flex;
                  width: 3.5rem;
                  height: 2rem;
                  justify-content: center;
                  align-items: center;
                  margin: 0 auto;
                  border-radius: ${schemes.radius.rounded};
                }
                & > h5 {
                  margin: ${schemes.spacing.sm} 0 0;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  color: ${colors.schems.dark.onBase};
                  text-align: center;
                  font-family: "Roboto", sans-serif;
                  font-size: ${fonts.body.bodyBold.fontSize};
                  font-weight: ${fonts.body.bodyBold.fontWeight};
                  line-height: ${fonts.body.bodyBold.lineHeight};
                  width: 100%;
                }
                &.active {
                  & > div {
                    background: ${colors.schems.dark.secondary};
                  }
                }
              }
            }
            &:last-child {
              flex-shrink: 0;
              position: relative;
              width: 100%;
              height: 6rem;
              padding: 0 ${schemes.spacing.md};
              border-top: ${schemes.borderWidth.md} solid
                ${colors.border.dark.default};
              > * {
                color: ${colors.schems.dark.onBase};
              }
              > button {
                width: 100%;
                height: 100%;
                border: none;
                background: none;
                > div {
                  width: 2.5rem;
                  height: 2.5rem;
                  margin: 0 auto ${schemes.spacing.sm};
                  padding: ${schemes.spacing.md};
                  border-radius: ${schemes.radius.sm};
                  background: ${colors.bg.dark.surfaceBright};
                  text-align: center;
                  font-size: ${fonts.body.bodyBold.fontSize};
                  font-weight: ${fonts.body.bodyBold.fontWeight};
                  line-height: ${fonts.body.bodyBold.lineHeight};
                }
                > h5 {
                  font-size: ${fonts.body.body.fontSize};
                  font-weight: ${fonts.body.body.fontWeight};
                  line-height: ${fonts.body.body.lineHeight};
                  margin: 0;
                }
                & + div {
                  display: none;
                  &.active {
                    display: flex;
                    flex-direction: column;
                    position: absolute;
                    top: -3px;
                    right: -13.75rem;

                    width: 15rem;
                    padding: ${schemes.spacing.md} 0;
                    border-radius: ${schemes.radius.sm};
                    border: 1px solid ${colors.border.dark.bgLighten};
                    background: ${colors.bg.dark.surfaceBright};

                    /* Elevation Dark/1 */
                    box-shadow:
                      0 1px 3px 1px rgba(0, 0, 0, 0.15),
                      0 1px 2px 0 rgba(0, 0, 0, 0.3);
                    > div {
                      width: 100%;
                      height: 2.5rem;
                      padding: 0 ${schemes.spacing.xl};
                      cursor: pointer;
                      > span {
                        display: block;
                        padding: 0 ${schemes.spacing.sm};
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;

                        font-size: ${fonts.body.body.fontSize};
                        font-weight: ${fonts.body.body.fontWeight};
                        line-height: 2.5rem;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      `}</style>
      <style jsx global={true}>{`
        body {
          background-color: ${colors.bg.dark.surfaceDim};
          color: ${colors.schems.dark.onBase};
          min-height: 100vh;
        }
        main {
          padding-left: ${auth.session?.access_token && navOpen ? "6rem" : "0"};
          min-height: 100vh;
        }
      `}</style>
    </>
  )
}

export default NavPage
