import Button from "@/components/elements/Button"
import Input from "@/components/elements/Input"
import { colors, schemes } from "@/utils/theme/style"
import { getAssetPath } from "@/utils/util"
import { ColorType } from "@/common/types/colorType"
import Checkbox from "@/components/elements/Checkbox"
import React, { useMemo, useState } from "react"
import { WorkSpaceTagType } from "@/common/types/WorkspaceType"
import useSearchCreator from "@/store/useSearchCreator"

export default function TagList({
  changeView,
  tagList,
  search,
}: {
  changeView: (page: "list" | "add" | "editItem" | "editList") => void
  tagList: any[]
  search: (page: number) => void
}) {
  const { addTagId, tagIds, resetTagIds } = useSearchCreator()
  const [searchKeyword, setSearchKeyword] = useState("")

  const searchTags = useMemo(() => {
    return searchKeyword.length
      ? tagList.filter((tag) =>
          tag.name.toLowerCase().includes(searchKeyword.toLowerCase())
        )
      : tagList
  }, [searchKeyword, tagList])

  return (
    <>
      <div className={"tags"}>
        <div>
          <div onClick={() => changeView("editList")}>
            <Button style={"text"} size={"xs"}>
              Edit
            </Button>
          </div>
        </div>
        <div>
          <div>
            <Input
              placeholderText={"Select Tag"}
              value={searchKeyword}
              onChange={(value) => setSearchKeyword(value as string)}
            />
          </div>
          <div onClick={() => changeView("add")}>
            <Button
              style={"text"}
              full={true}
              icon={{
                path: getAssetPath("add.svg"),
              }}
            >
              Add Tag
            </Button>
          </div>
        </div>
        <ul>
          {searchTags.length > 0 &&
            searchTags.map((tag: WorkSpaceTagType) => (
              <li key={tag.id}>
                <div>
                  <Checkbox
                    isChecked={tagIds.includes(tag.id as number)}
                    color={tag.color as ColorType}
                    action={() => addTagId(tag.id as number)}
                  >
                    <div className={"tag-name"}>
                      <div className={"name"}>{tag.name}</div>
                      <span className={"count"}>
                        ({tag.count.toLocaleString()})
                      </span>
                    </div>
                  </Checkbox>
                </div>
              </li>
            ))}
        </ul>
      </div>
      <div className={"buttons"}>
        <div>
          <Button style={"secondary"} onClick={resetTagIds}>
            Clear all
          </Button>
        </div>
        <div>
          <Button
            full={true}
            onClick={() => {
              search(1)
            }}
          >
            Apply Filters
          </Button>
        </div>
      </div>
      <style jsx>{`
        .tags {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: ${schemes.spacing.md};
          padding: ${schemes.spacing["3xl"]} ${schemes.spacing["2xl"]};
          height: calc(100vh - 6rem);
          overflow-y: auto;
          & > div {
            &:first-of-type {
              display: flex;
              justify-content: end;
              padding: 0 ${schemes.spacing.md};
            }
            &:nth-of-type(2) {
              display: flex;
              flex-direction: column;
              gap: ${schemes.spacing["2xl"]};
              padding: 0 ${schemes.spacing.md};
            }
          }
          & > ul {
            margin: 0;
            padding: 0;
            list-style: none;
            & > li {
              width: 100%;
              height: 2.5rem;
              padding: 0 ${schemes.spacing.md};
              display: flex;
              flex-direction: column;
              align-items: start;
              justify-content: center;
              & > div {
                position: relative;
                width: 100%;
                overflow: hidden;
                white-space: nowrap;
                & .tag-name {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  flex-wrap: nowrap;
                  gap: ${schemes.spacing.sm};
                  width: 100%;
                  & .name {
                    flex-grow: 1;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  }
                  & .count {
                    flex-shrink: 0;
                  }
                }
              }
            }
          }
        }
        .buttons {
          flex-shrink: 0;
          display: flex;
          height: 6rem;
          gap: ${schemes.spacing.md};
          padding: ${schemes.spacing["2xl"]} ${schemes.spacing["3xl"]};
          border-top: ${schemes.borderWidth.md} solid
            ${colors.border.dark.default};
          & > div:last-of-type {
            flex: 1;
          }
        }
      `}</style>
    </>
  )
}
