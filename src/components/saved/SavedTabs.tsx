import { colors, fonts, schemes } from "@/utils/theme/style"
import Filters from "@/components/filters/Filters"
import Tags from "@/components/saved/Tags"
import { FilterType } from "@/common/enums/filterType"
import useUserInfo from "@/store/useUserInfo"

export default function SavedTabs({
  search,
}: {
  search: (page: number) => void
}) {
  const { savedTab, setSavedTab } = useUserInfo()
  return (
    <>
      <div className={"tabs"}>
        <button
          // disabled={true}
          onClick={() => setSavedTab("tags")}
          className={`tab ${savedTab === "tags" ? "active" : ""}`}
        >
          <span>Custom Tag</span>
        </button>
        <button
          onClick={() => setSavedTab("filters")}
          className={`tab ${savedTab === "filters" ? "active" : ""}`}
        >
          <span>Detail Filter</span>
        </button>
      </div>
      {savedTab === "tags" ? (
        <Tags search={search} />
      ) : (
        <Filters type={FilterType.saved} search={search} isTabs={true} />
      )}
      <style jsx>{`
        .tabs {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 3.5rem;
          flex-shrink: 0;
          & > .tab {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background: none;
            height: 100%;
            cursor: pointer;
            border: 0;
            border-bottom: ${schemes.borderWidth.md} solid
              ${colors.border.dark.default};
            &:focus-visible {
              outline: none;
              background: ${colors.interactive.dark.hover};
            }
            &:disabled {
              background: ${colors.schems.dark.disabled};
              cursor: not-allowed;
            }
            & > span {
              color: ${colors.schems.dark.onDisabled};
              font-size: ${fonts.body.body.fontSize};
              font-weight: ${fonts.body.body.fontWeight};
              line-height: ${fonts.body.body.lineHeight};
            }
            &.active {
              border-bottom: ${schemes.borderWidth.md} solid
                ${colors.border.dark.primary};
              & > span {
                color: ${colors.schems.dark.onBase};
                font-size: ${fonts.body.bodyBold.fontSize};
                font-weight: ${fonts.body.bodyBold.fontWeight};
                line-height: ${fonts.body.bodyBold.lineHeight};
              }
            }
          }
        }
      `}</style>
    </>
  )
}
