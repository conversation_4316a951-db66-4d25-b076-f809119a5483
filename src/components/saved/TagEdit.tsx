import Button from "@/components/elements/Button"
import { colors, fonts, schemes } from "@/utils/theme/style"
import LabelTag from "@/components/elements/LabelTag"
import { ColorType } from "@/common/types/colorType"
import { useEffect, useMemo, useState } from "react"
import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"
import Input from "@/components/elements/Input"
import {
  workspaceTagAdd,
  workspaceTagDelete,
  workspaceTagEdit,
} from "@/service/workspace"
import { WorkSpaceTagType } from "@/common/types/WorkspaceType"
import { notifyError, notifySuccess } from "@/components/modal/Toasts"
import DeleteTagModal from "@/components/modal/DeleteTagModal"

export default function TagEdit({
  changeView,
  tag,
  addTag,
  updateTag,
  setTag,
  tags,
  setTags,
  workspaceId,
  accessToken,
  setModalOpen,
}: {
  changeView?: (page: "list" | "add" | "editItem" | "editList") => void
  tag: WorkSpaceTagType | null
  addTag: (workspaceTag: WorkSpaceTagType) => void
  updateTag: (id: number, workspaceTag: WorkSpaceTagType) => void
  setTag: (workspaceTag: WorkSpaceTagType | null) => void
  tags: WorkSpaceTagType[]
  setTags: (workspaceTags: WorkSpaceTagType[]) => void
  workspaceId: number
  accessToken: string | null
  setModalOpen?: (val: boolean) => void
}) {
  const [itemName, setItemName] = useState<string>(tag?.name ?? "")
  const [itemColor, setItemColor] = useState<string>(tag?.color ?? "gray")
  const [loading, setLoading] = useState(false)
  const [isDeleteLoading, setIsDeleteLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const colorsArr: ColorType[] = [
    "red",
    "orange",
    "yellow",
    "green",
    "cyan",
    "blue",
    "indigo",
    "purple",
    "pink",
    "gray",
  ]

  const hasDuplicateTag = useMemo(() => {
    return itemName.length === 0
      ? false
      : tags.filter(
          (savedTag) =>
            savedTag.name.toLowerCase() === itemName.toLowerCase() &&
            savedTag.id !== tag?.id
        ).length > 0
  }, [tags, tag, itemName])

  const addTagRequest = async () => {
    if (accessToken && workspaceId) {
      setLoading(true)
      if (tag && tag.id) {
        await workspaceTagEdit({
          accessToken: accessToken,
          workspaceId,
          tagId: tag.id,
          data: {
            color: itemColor,
            name: itemName,
          },
        })
          .then(() => {
            updateTag(tag.id as number, {
              ...tag,
              color: itemColor,
              name: itemName,
            })
            setTag(null)
            notifySuccess("Successfully saved to a custom tag.")
            if (changeView) changeView("editList")
          })
          .catch((e) => {
            console.error(e)
            notifyError("Unable to edit tag, please try again.")
          })
          .finally(() => {
            setLoading(false)
          })
      } else {
        await workspaceTagAdd({
          accessToken: accessToken,
          workspaceId,
          data: {
            color: itemColor,
            name: itemName,
          },
        })
          .then((response) => {
            addTag(response)
            notifySuccess("Successfully saved to a custom tag.")
            if (changeView) changeView("list")
          })
          .catch((e) => {
            console.error(e)
            notifyError("Unable to create tag, please try again.")
          })
          .finally(() => {
            setLoading(false)
            if (setModalOpen) setModalOpen(false)
          })
      }
    }
  }

  const deleteTag = async (tagId: number) => {
    setIsDeleteLoading(true)
    workspaceTagDelete({
      accessToken: accessToken as string,
      workspaceId: workspaceId as number,
      tagId: tagId,
    })
      .then(() => {
        setTag(null)
        setTags(tags.filter((tag) => tag.id !== tagId))
        setIsOpen(false)
        notifySuccess("Successfully deleted the user tag.")
        if (changeView) changeView("list")
      })
      .catch((e) => notifyError("Unable to delete user tag, please try again."))
      .finally(() => {
        setIsDeleteLoading(false)
      })
  }

  return (
    <>
      <div className={`add-tag ${changeView ? "" : "modal"}`}>
        <div className={"body"}>
          <div className={"item-name"}>
            <Input
              label={{ text: "Custom Tag Name" }}
              placeholderText={"Custom Tag Name"}
              onChange={setItemName}
              value={itemName}
              isRightBtn={false}
              {...(hasDuplicateTag && {
                status: "error",
                helper: { text: "A tag name that already exists." },
              })}
            />
            {tag && tag.id && (
              <div>
                <Button
                  style={"secondary"}
                  full={true}
                  icon={{
                    path: getAssetPath("delete_forever_true.svg"),
                    color: colors.sys.dark.red.default,
                  }}
                  onClick={() => setIsOpen(true)}
                  color={colors.sys.dark.red.default}
                >
                  Delete
                </Button>
              </div>
            )}
          </div>
          <div className={"bar"}>
            <hr />
          </div>
          <div className={"item-colors"}>
            <label>Custom Tag Color</label>
            <ul>
              {colorsArr.map((color: string) => (
                <li
                  key={color}
                  onClick={() => setItemColor(color as ColorType)}
                  className={`${itemColor === (color as ColorType) ? "selected" : ""}`}
                >
                  <LabelTag type={"folder"} color={color as ColorType}>
                    {color}
                  </LabelTag>
                  {itemColor === (color as ColorType) && (
                    <Icon
                      path={getAssetPath("check.svg")}
                      color={colors.schems.dark.primary}
                    />
                  )}
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className={"buttons"}>
          <div
            onClick={() => {
              if (changeView) changeView(tag && tag.id ? "editList" : "list")
              else if (setModalOpen) {
                setModalOpen(false)
              }
              setTag(null)
            }}
          >
            <Button style={"text"} full={true} disabled={loading}>
              Cancel
            </Button>
          </div>
          <div onClick={addTagRequest}>
            <Button
              full={true}
              isLoading={loading}
              disabled={loading || hasDuplicateTag || itemName.length === 0}
            >
              {tag && tag.id ? "Edit" : "Create"}
            </Button>
          </div>
        </div>
      </div>
      {tag && tag.id && (
        <DeleteTagModal
          tag={tag}
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          deleteTag={deleteTag}
          isDeleteLoading={isDeleteLoading}
        />
      )}
      <style jsx>{`
        .add-tag {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          & > .body {
            position: relative;
            overflow-y: auto;
            height: calc(100vh - 9.5rem);
            padding: ${schemes.spacing["3xl"]} ${schemes.spacing["2xl"]};
            display: flex;
            flex-direction: column;
            gap: ${schemes.spacing["2xl"]};
            -ms-overflow-style: none;
            &::-webkit-scrollbar {
              display: none;
            }

            & > div.item-name {
              padding: 0 ${schemes.spacing.md};
              > div {
                margin-top: ${schemes.spacing.md};
                button {
                  gap: 0;
                }
              }
            }

            & > div.bar {
              padding: ${schemes.spacing.md};
              & > hr {
                width: 100%;
                border-color: ${colors.border.dark.bgLighten};
                border-top: 0;
              }
            }

            & > .item-colors {
              > label {
                display: block;
                //padding: 0 ${schemes.spacing.md};
                font-size: ${fonts.body.label.fontSize};
                font-weight: ${fonts.body.label.fontWeight};
                line-height: ${fonts.body.label.lineHeight};
              }
              > ul {
                margin: 0;
                padding: 0;
                list-style: none;
                display: flex;
                flex-direction: column;
                & > li {
                  text-transform: capitalize;
                  height: 2.5rem;
                  padding: 0 ${schemes.spacing.md};
                  border-radius: ${schemes.radius.md};
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  cursor: pointer;
                  &.selected,
                  &:hover,
                  &:active {
                    background: ${colors.interactive.dark.hover};
                  }
                }
              }
            }
          }
          & > .buttons {
            display: flex;
            justify-content: center;
            align-items: start;
            height: 6rem;
            gap: ${schemes.spacing.md};
            padding: ${schemes.spacing["2xl"]} ${schemes.spacing["3xl"]};
            border-top: ${schemes.borderWidth.md} solid
              ${colors.border.dark.default};
            & > div {
              flex: 1;
            }
          }

          &.modal {
            height: 100%;
            & > .body {
              padding: 0;
              margin: 0 -0.5rem;
              flex: 1;
            }
            & > .buttons {
              padding: ${schemes.spacing["2xl"]} 0 0;
              height: fit-content;
              justify-content: right;
              > div {
                flex: unset;
              }
              button {
                width: fit-content;
              }
            }
          }
        }
      `}</style>
    </>
  )
}
