import { useMemo, useState } from "react"
import TagList from "@/components/saved/TagList"
import TagListEdit from "@/components/saved/TagListEdit"
import TagEdit from "@/components/saved/TagEdit"
import useWorkspace from "@/store/useWorkspace"
import { useAuth } from "@/common/hooks/useAuthProvider"
import { WorkSpaceTagType } from "@/common/types/WorkspaceType"

export default function Tags({ search }: { search: (page: number) => void }) {
  const auth = useAuth()
  const accessToken = auth.session?.access_token || null
  const { currentWorkspace, tags, addTag, updateTag, setTags } = useWorkspace()
  const workspaceId = currentWorkspace?.id || 0
  const [tagView, setTagView] = useState<
    "list" | "add" | "editItem" | "editList"
  >("list")
  const [tag, setTag] = useState<WorkSpaceTagType | null>(null)

  const filterTags = useMemo(
    () => (tags ? tags.filter((tag) => !tag.is_default) : []),
    [tags]
  )

  switch (tagView) {
    case "add":
    case "editItem":
      return (
        <TagEdit
          changeView={setTagView}
          addTag={addTag}
          tag={tag}
          setTag={setTag}
          tags={tags}
          setTags={setTags}
          updateTag={updateTag}
          accessToken={accessToken}
          workspaceId={workspaceId}
        />
      )
    case "editList":
      return (
        <TagListEdit
          changeView={setTagView}
          setTag={setTag}
          tagList={filterTags}
          workspaceId={workspaceId}
          accessToken={accessToken}
          setTagList={setTags}
        />
      )
    default:
      return (
        <TagList changeView={setTagView} tagList={filterTags} search={search} />
      )
  }
}
