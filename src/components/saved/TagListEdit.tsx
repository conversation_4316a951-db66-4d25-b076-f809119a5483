import Button from "@/components/elements/Button"
import { colors, schemes } from "@/utils/theme/style"
import { DragDropContext, Draggable, Droppable } from "@hello-pangea/dnd"
import { useCallback, useEffect, useState } from "react"
import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"
import LabelTag from "@/components/elements/LabelTag"
import IconButton from "@/components/elements/IconButton"
import { workspaceSortTagList } from "@/service/workspace"
import { notifyError } from "@/components/modal/Toasts"
import { WorkSpaceTagType } from "@/common/types/WorkspaceType"
import { ColorType } from "@/common/types/colorType"

export default function TagListEdit({
  changeView,
  setTag,
  tagList,
  setTagList,
  accessToken,
  workspaceId,
}: {
  changeView: (page: "list" | "add" | "editItem" | "editList") => void
  setTag: (tag: WorkSpaceTagType) => void
  tagList: WorkSpaceTagType[]
  setTagList: (list: WorkSpaceTagType[]) => void
  accessToken: string | null
  workspaceId: number
}) {
  const [items, setItems] = useState(tagList)
  const [isLoading, setIsLoading] = useState(false)
  const [disabled, setDisabled] = useState<boolean>(false)

  useEffect(() => {
    setItems(tagList)
  }, [tagList])

  const onDragEnd = useCallback(
    (result: any) => {
      if (!result.destination) return

      const reorderedItems = [...items!]
      const [movedItem] = reorderedItems.splice(result.source.index, 1)
      reorderedItems.splice(result.destination.index, 0, movedItem)
      setItems(reorderedItems)
    },
    [items]
  )

  const tagListSaveSort = useCallback(async () => {
    setIsLoading(true)
    setDisabled(true)
    if (accessToken && workspaceId) {
      await workspaceSortTagList({
        accessToken: accessToken,
        workspaceId,
        data: items,
      })
        .then(() => {
          setTagList(items)
          changeView("list")
        })
        .catch((e) => {
          console.error(e)
          notifyError(e.message)
          setIsLoading(false)
          setDisabled(false)
        })
    }
  }, [items])

  return (
    <>
      <div className="tags">
        <div className={"body"}>
          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="droppable">
              {(provided) => (
                <ul {...provided.droppableProps} ref={provided.innerRef}>
                  {items &&
                    items.map((item, index) => (
                      <Draggable
                        key={index}
                        draggableId={`list-${index}`}
                        index={index}
                      >
                        {(provided) => (
                          <li
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                          >
                            <div {...provided.dragHandleProps}>
                              <Icon path={getAssetPath("drag_indicator.svg")} />
                            </div>
                            <p>
                              <LabelTag
                                type={"folder"}
                                color={item.color as ColorType}
                              >
                                {item.name}
                              </LabelTag>
                            </p>
                            <div
                              onClick={() => {
                                setTag(item)
                                changeView("editItem")
                              }}
                            >
                              <IconButton
                                path={getAssetPath("more_horiz.svg")}
                                label={"more"}
                              />
                            </div>
                          </li>
                        )}
                      </Draggable>
                    ))}
                  {provided.placeholder}
                </ul>
              )}
            </Droppable>
          </DragDropContext>
        </div>
        <div className={"buttons"}>
          <div onClick={() => changeView("list")}>
            <Button style={"text"} full={true}>
              Cancel
            </Button>
          </div>
          <div>
            <Button
              full={true}
              onClick={tagListSaveSort}
              disabled={disabled}
              isLoading={isLoading}
            >
              Save
            </Button>
          </div>
        </div>
      </div>
      <style jsx>{`
        .tags {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          & > .body {
            position: relative;
            overflow-y: auto;
            height: calc(100vh - 9.5rem);
            padding: ${schemes.spacing["3xl"]} ${schemes.spacing["2xl"]};
            display: flex;
            flex-direction: column;
            gap: ${schemes.spacing["2xl"]};
            -ms-overflow-style: none;
            &::-webkit-scrollbar {
              display: none;
            }

            ul {
              margin: 0;
              padding: 0;
              list-style: none;
              display: flex;
              flex-direction: column;
              & > li {
                text-transform: capitalize;
                height: 2.5rem;
                padding: 0 0 0 ${schemes.spacing.md};
                border-radius: ${schemes.radius.md};
                display: flex;
                justify-content: space-between;
                align-items: center;
                &.selected,
                &:hover,
                &:active {
                  background: ${colors.interactive.dark.hover};
                }
                & > div {
                  cursor: pointer;
                }
                & > p {
                  flex: 1;
                  overflow: hidden;
                  margin: 0;
                  padding-left: ${schemes.spacing.sm};
                  label {
                    width: 100%;
                    justify-content: flex-start;
                    span:last-child {
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    }
                  }
                }
              }
            }
          }
        }
        .buttons {
          display: flex;
          justify-content: center;
          align-items: start;
          height: 6rem;
          gap: ${schemes.spacing.md};
          padding: ${schemes.spacing["2xl"]} ${schemes.spacing["3xl"]};
          border-top: ${schemes.borderWidth.md} solid
            ${colors.border.dark.default};
          & > div {
            flex: 1;
          }
        }
      `}</style>
    </>
  )
}
