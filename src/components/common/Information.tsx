import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"
import { colors, fonts, schemes } from "@/utils/theme/style"
import React, { useCallback } from "react"

export default function Information({
  type = "default",
  children,
}: {
  type?: "default" | "warning" | "danger" | "success"
  children?: React.ReactNode
}) {
  const styleIcon = useCallback(() => {
    switch (type) {
      case "warning":
        return (
          <Icon
            path={getAssetPath("warning.svg")}
            color={colors.sys.dark.yellow.default}
          />
        )
      case "danger":
        return (
          <Icon
            path={getAssetPath("cancel.svg")}
            color={colors.sys.dark.red.default}
          />
        )
      case "success":
        return (
          <Icon
            path={getAssetPath("check_circle.svg")}
            color={colors.sys.dark.green.default}
          />
        )
      default:
        return (
          <Icon
            path={getAssetPath("info.svg")}
            color={colors.schems.dark.disabled}
          />
        )
    }
  }, [type])

  return (
    <>
      <div className={`information ${type}`}>
        {styleIcon()}
        <span>{children}</span>
      </div>
      <style jsx>{`
        .information {
          display: flex;
          align-items: center;
          gap: ${schemes.spacing.md};

          width: 100%;
          padding: ${schemes.spacing.xl} ${schemes.spacing["2xl"]};
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          border-radius: ${schemes.radius.sm};
          border: 1px solid ${colors.border.dark.bgLighten};
          background: ${colors.bg.dark.surfaceBright};
          > span {
            color: ${colors.schems.dark.onBase};
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            font-size: ${fonts.body.body.fontSize};
            font-weight: ${fonts.body.body.fontWeight};
            line-height: ${fonts.body.body.lineHeight};
          }

          &.warning {
            border: none;
            background: ${colors.sys.dark.yellow.lighten};
            > span {
              color: ${colors.sys.dark.yellow.darken};
            }
          }

          &.danger {
            border: none;
            background: ${colors.sys.dark.red.lighten};
            > span {
              color: ${colors.sys.dark.red.darken};
            }
          }

          &.success {
            border: none;
            background: ${colors.sys.dark.green.lighten};
            > span {
              color: ${colors.sys.dark.green.darken};
            }
          }
        }
      `}</style>
    </>
  )
}
