import React from "react"
import { colors, fonts, schemes } from "@/utils/theme/style"
import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"

export default function Loading() {
  return (
    <>
      <div className={"loading"}>
        <h3>Loading...</h3>
        <p>
          <Icon
            path={getAssetPath("loading.svg")}
            color={colors.schems.dark.onPrimary}
            size={"2.5rem"}
            isSpin={true}
          />
        </p>
      </div>
      <style jsx>{`
        .loading {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          gap: ${schemes.spacing.md};
          width: 100%;
          height: 100%;
          & > * {
            color: ${colors.schems.dark.onDisabled};
            line-height: ${fonts.body.large.lineHeight};
            margin: 0;
            padding: 0;
          }
          & > h3 {
            font-size: ${fonts.heading.h3.fontSize};
            font-weight: ${fonts.heading.h3.fontWeight};
          }
          & > p {
            font-size: ${fonts.body.large.fontSize};
            font-weight: 500;
          }
        }
      `}</style>
    </>
  )
}
