import React from "react"
import { colors, fonts, schemes } from "@/utils/theme/style"

export default function NotFound({
  title = "No creators found.",
  message = "Try adjusting your filters and search again.",
}: {
  title?: string
  message?: string
}) {
  return (
    <>
      <div className={`not-found`}>
        <h3>{title}</h3>
        <p>{message}</p>
      </div>
      <style jsx>{`
        .not-found {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          gap: ${schemes.spacing.md};
          width: 100%;
          height: 100%;
          & > * {
            color: ${colors.schems.dark.onDisabled};
            line-height: ${fonts.body.large.lineHeight};
            margin: 0;
            padding: 0;
          }
          & > h3 {
            font-size: ${fonts.heading.h3.fontSize};
            font-weight: ${fonts.heading.h3.fontWeight};
          }
          & > p {
            font-size: ${fonts.body.large.fontSize};
            font-weight: 500;
          }
        }
      `}</style>
    </>
  )
}
