"use client"

import React, { useEffect } from "react"
import { useAuth } from "@/common/hooks/useAuthProvider"
import Script from "next/script"

export default function ClarityIntegration() {
  const { user } = useAuth()

  useEffect(() => {
    if (typeof window !== "undefined" && window.clarity && user?.id) {
      window.clarity("identify", `${user.id}`, user.email)
    }
  }, [user])

  return (
    <>
      <Script
        id="ms-clarity"
        strategy="afterInteractive"
        onLoad={() => console.log("clarity onload")}
        onReady={() => console.log("clarity onready")}
        onError={() => console.log("clarity onerror")}
        dangerouslySetInnerHTML={{
          __html: `
         (function(c,l,a,r,i,t,y){
             c[a] = c[a] || function () { (c[a].q = c[a].q || 
             []).push(arguments) };
             t=l.createElement(r);
             t.async=1;
             t.src="https://www.clarity.ms/tag/"+i;
             y=l.getElementsByTagName(r)[0];
             y.parentNode.insertBefore(t,y);
         })(window, document, "clarity", "script", "qgrpdszozs");`,
        }}
      />
    </>
  )
}
