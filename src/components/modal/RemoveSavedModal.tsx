import BaseModal from "@/components/modal/BaseModal"
import Button from "@/components/elements/Button"
import TagEdit from "@/components/saved/TagEdit"
import { WorkSpaceTagType } from "@/common/types/WorkspaceType"
import { useAuth } from "@/common/hooks/useAuthProvider"
import useWorkspace from "@/store/useWorkspace"
import { useState } from "react"
import { colors, fonts, schemes } from "@/utils/theme/style"

export default function RemoveSavedModal({
  isOpen,
  setIsOpen,
  loading,
  selectUniqueIds,
  removeSaved,
}: {
  isOpen: boolean
  setIsOpen: (val: boolean) => void
  loading: boolean
  selectUniqueIds: Set<string>
  removeSaved: () => Promise<void>
}) {
  return (
    <>
      <BaseModal isOpen={isOpen} setIsOpen={setIsOpen}>
        <div className="modal-body">
          <div>
            <h3>Remove from Saved</h3>
            <p>Are you want to remove this creator from your Saved list?</p>
            <div>A total of {selectUniqueIds.size} creators</div>
          </div>
          <div>
            <Button style={"text"} onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button
              style={"secondary"}
              color={colors.sys.dark.red.default}
              onClick={removeSaved}
              disabled={loading}
              isLoading={loading}
            >
              Delete
            </Button>
          </div>
        </div>
      </BaseModal>
      <style jsx>{`
        .modal-body {
          > div:first-of-type {
            display: flex;
            justify-content: center;
            align-items: start;
            flex-direction: column;
            gap: ${schemes.spacing["2xl"]};
            padding-bottom: ${schemes.spacing["2xl"]};
            > * {
              width: 100%;
              margin: 0;
              padding: 0;
            }
            > h3 {
              font-size: ${fonts.heading.h3.fontSize};
              font-weight: ${fonts.heading.h3.fontWeight};
              line-height: ${fonts.heading.h3.lineHeight};
            }
            > p,
            > div {
              font-size: ${fonts.body.large.fontSize};
              font-weight: 500;
              line-height: ${fonts.body.large.lineHeight};
            }
            > div {
              border-radius: ${schemes.radius.sm};
              border: 1px solid ${colors.border.dark.bgLighten};
              padding: ${schemes.spacing["2xl"]};
            }
          }
          > div:last-of-type {
            display: flex;
            flex-direction: row;
            justify-content: end;
            width: 100%;
            gap: ${schemes.spacing.md};
            padding-top: ${schemes.spacing["2xl"]};
          }
        }
      `}</style>
    </>
  )
}
