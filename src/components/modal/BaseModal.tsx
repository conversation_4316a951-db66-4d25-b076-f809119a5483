import { colors, schemes } from "@/utils/theme/style"
import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"
import React from "react"

function BaseModal({
  children,
  isOpen,
  setIsOpen,
}: {
  children?: React.ReactNode
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
}) {
  return (
    <>
      <div className={`modal ${isOpen ? "open" : ""}`}>
        <div>
          <button onClick={() => setIsOpen(false)}>
            <Icon
              path={getAssetPath("close.svg")}
              color={colors.schems.dark.onBase}
            />
          </button>
          {children}
        </div>
      </div>
      <style jsx>{`
        .modal {
          z-index: 100;
          visibility: hidden;
          position: fixed;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          &.open {
            visibility: visible;
          }
          > div {
            position: relative;
            width: 30rem;
            top: 50%;
            transform: translateY(-50%);
            margin: 0 auto;
            padding: ${schemes.spacing["3xl"]};

            background: ${colors.bg.dark.surfaceBright};
            border: 2px solid ${colors.border.dark.bgLighten};
            border-radius: ${schemes.spacing["2xl"]};
            > button {
              background: none;
              border: none;
              width: 2.5rem;
              height: 2.5rem;
              position: absolute;
              right: 16px;
              top: 19px;
              cursor: pointer;
            }
          }
        }
      `}</style>
    </>
  )
}

export default React.memo(BaseModal)
