import Link from "next/link"
import Image from "next/image"
import { colors, fonts, schemes } from "@/utils/theme/style"
import React, { SyntheticEvent } from "react"
import { getAssetPath, nFormatter } from "@/utils/util"
import Icon from "@/components/elements/Icon"

function CreatorDetailVideos({ post }: { post?: any }) {
  return (
    <>
      <li>
        {post ? (
          <Link href={post.url} target="_blank">
            <div className={"cover-frame"}>
              <div>
                {post.cover ? (
                  <Image
                    src={post.cover}
                    alt={post.cover}
                    className={"cover"}
                    width={159}
                    height={282}
                    // fetchPriority={"low"}
                    priority
                    style={{
                      display: "block",
                      borderRadius: `${schemes.radius.md}`,
                      border: `1px solid ${colors.border.dark.default}`,
                    }}
                    onError={(e: SyntheticEvent<HTMLImageElement, Event>) => {
                      e.currentTarget.src = getAssetPath(
                        "contents_error_image_large.svg"
                      )
                    }}
                  />
                ) : (
                  <Image
                    src={getAssetPath("contents_error_image_large.svg")}
                    alt={getAssetPath("contents_error_image_large.svg")}
                    className={"cover"}
                    width={159}
                    height={282}
                    style={{
                      display: "block",
                      borderRadius: `${schemes.radius.md}`,
                    }}
                    // fetchPriority={"low"}
                    priority
                  />
                )}
              </div>
              <div className={"cover-info"}>
                <div>{post.is_pinned && <div>Pinned</div>}</div>
                <div>
                  <div>
                    <Icon
                      size={"1rem"}
                      color={colors.schems.dark.onBase}
                      path={getAssetPath("play_arrow_false.svg")}
                    />
                    <span>{nFormatter(post.play_count)}</span>
                  </div>
                  <div>{new Date(post.create_time).toLocaleDateString()}</div>
                </div>
              </div>
            </div>
          </Link>
        ) : (
          <div className={"cover-frame blank"}>
            <div></div>
          </div>
        )}
        <div>
          {post ? (
            <>
              <div>
                <Icon size={"1rem"} path={getAssetPath("favorite_true.svg")} />
                <span>{nFormatter(post.digg_count)}</span>
              </div>
              <div>
                <Icon size={"1rem"} path={getAssetPath("chat_true.svg")} />
                <span>{nFormatter(post.comment_count)}</span>
              </div>
              <div>
                <Icon size={"1rem"} path={getAssetPath("bookmark_true.svg")} />
                <span>{nFormatter(post.collect_count)}</span>
              </div>
              <div>
                <Icon size={"1rem"} path={getAssetPath("arrow_outward.svg")} />
                <span>{nFormatter(post.share_count)}</span>
              </div>
            </>
          ) : (
            [...Array(4)].map((_, i) => (
              <div key={i}>
                <span>-</span>
              </div>
            ))
          )}
        </div>
      </li>
      <style jsx>{`
        li {
          a {
            &:link,
            &:hover,
            &:visited {
              color: inherit;
              text-decoration: none;
            }
          }
          .cover-frame {
            position: relative;
            cursor: pointer;
            .cover-info {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              align-items: start;
              > div:first-child {
                padding: ${schemes.spacing.md};
                > div {
                  padding: 0 ${schemes.spacing.sm};
                  border-radius: ${schemes.radius.sm};
                  background: ${colors.sys.dark.red.default};
                  font-size: ${fonts.body.label.fontSize};
                  font-weight: ${fonts.body.label.fontWeight};
                  line-height: ${fonts.body.label.lineHeight};
                }
              }
              > div:last-child {
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: ${schemes.spacing.sm} ${schemes.spacing.md};
                > * {
                  font-size: ${fonts.body.label.fontSize};
                  font-weight: ${fonts.body.label.fontWeight};
                  line-height: ${fonts.body.label.lineHeight};
                }
                > div:first-child {
                  display: flex;
                  align-items: center;
                  gap: ${schemes.spacing.sm};
                }
              }
            }
            &.blank {
              > div {
                width: 159px;
                height: 282px;
                border-radius: ${schemes.radius.md};
                border: ${schemes.borderWidth.sm} solid
                  ${colors.border.dark.bgLighten};
              }
            }
          }

          a + div,
          .cover-frame + div {
            margin-top: ${schemes.spacing.md};
            display: grid;
            grid-gap: ${schemes.spacing.sm};
            grid-template-columns: repeat(2, 1fr);
            > div {
              border-radius: ${schemes.radius.sm};
              background: ${colors.bg.dark.surfaceBright};
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: ${schemes.spacing.sm} ${schemes.spacing.md};
              span {
                color: ${colors.schems.dark.onDisabled};
              }
              &:has(:only-child) {
                justify-content: end;
              }
            }
          }

          span {
            font-size: ${fonts.body.label.fontSize};
            font-weight: ${fonts.body.label.fontWeight};
            line-height: ${fonts.body.label.lineHeight};
          }
        }
      `}</style>
    </>
  )
}
export default React.memo(CreatorDetailVideos)
