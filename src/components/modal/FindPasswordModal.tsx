import BaseModal from "@/components/modal/BaseModal"
import Input from "@/components/elements/Input"
import { useRef, useState } from "react"
import Button from "@/components/elements/Button"
import { fonts, schemes } from "@/utils/theme/style"
import { supabaseClient } from "@/utils/supabase/client"
import { notifyError, notifySuccess } from "@/components/modal/Toasts"

export default function FindPasswordModal({
  isOpen,
  setIsOpen,
}: {
  isOpen: boolean
  setIsOpen: (val: boolean) => void
}) {
  const [email, setEmail] = useState<string>("")

  const lostPasswordReset = async () => {
    if (email === "") {
      notifyError("Please enter your e-mail")
    } else {
      const { data, error } = await supabaseClient.auth.resetPasswordForEmail(
        email,
        {}
      )

      if (error) {
        notifyError(error.message)
      } else {
        notifySuc<PERSON>("Send Email in Password Reset Link")
        setIsOpen(false)
      }
    }
  }

  return (
    <>
      <BaseModal isOpen={isOpen} setIsOpen={setIsOpen}>
        <div className="modal-body">
          <div>
            <h3>Reset Your Password</h3>
            <p>Enter your work email to receive a password reset link.</p>
            <div>
              <Input
                status={"default"}
                label={{ text: "Work email" }}
                placeholderText={"<EMAIL>"}
                value={email}
                onChange={setEmail}
              />
            </div>
          </div>
          <div>
            <Button style={"text"} onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => lostPasswordReset()}>Send</Button>
          </div>
        </div>
      </BaseModal>
      <style jsx>{`
        .modal-body {
          > div:first-of-type {
            display: flex;
            justify-content: center;
            align-items: start;
            flex-direction: column;
            gap: ${schemes.spacing["2xl"]};
            padding-bottom: ${schemes.spacing["2xl"]};
            > * {
              width: 100%;
              margin: 0;
              padding: 0;
            }
            > h3 {
              font-size: ${fonts.heading.h3.fontSize};
              font-weight: ${fonts.heading.h3.fontWeight};
              line-height: ${fonts.heading.h3.lineHeight};
            }
            > p {
              font-size: ${fonts.body.large.fontSize};
              font-weight: 500;
              line-height: ${fonts.body.large.lineHeight};
            }
          }
          > div:last-of-type {
            display: flex;
            flex-direction: row;
            justify-content: end;
            width: 100%;
            gap: ${schemes.spacing.md};
            padding-top: ${schemes.spacing["2xl"]};
          }
        }
      `}</style>
    </>
  )
}
