import { colors, fonts, schemes } from "@/utils/theme/style"
import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"
import React from "react"
import Button from "@/components/elements/Button2"

function BaseModalWithTitle({
  children,
  isOpen,
  setIsOpen,
  headerText,
  confirmText,
  confirmAction,
  confirmDisabled,
  confirmLoading,
}: {
  children?: React.ReactNode
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
  headerText?: string
  confirmText?: string
  confirmAction?: () => void
  confirmDisabled?: boolean
  confirmLoading?: boolean
}) {
  return (
    <>
      <div className={`modal ${isOpen ? "open" : ""}`}>
        <div>
          <div className={"header"}>
            <h3 className={"header-title"}>{headerText}</h3>
            <button
              className={"btn-close"}
              disabled={confirmLoading}
              onClick={() => setIsOpen(false)}
            >
              <Icon
                path={getAssetPath("close.svg")}
                color={colors.schems.dark.onBase}
              />
            </button>
          </div>
          <div className={"body"}>{children}</div>
          {confirmText && (
            <div className={"footer"}>
              <Button
                styleType={"text"}
                disabled={confirmLoading}
                onClick={() => setIsOpen(false)}
              >
                Cancel
              </Button>
              <Button
                styleType={"primary"}
                disabled={confirmDisabled || confirmLoading}
                isLoading={confirmLoading}
                onClick={confirmAction}
              >
                {confirmText}
              </Button>
            </div>
          )}
        </div>
      </div>
      <style jsx>{`
        .modal {
          z-index: 100;
          visibility: hidden;
          position: fixed;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          &.open {
            visibility: visible;
          }
          > div {
            position: relative;
            width: 30rem;
            top: 50%;
            transform: translateY(-50%);
            margin: 0 auto;

            background: ${colors.bg.dark.surfaceBright};
            border: 2px solid ${colors.border.dark.bgLighten};
            border-radius: ${schemes.spacing["2xl"]};
            .body {
              padding: ${schemes.spacing["3xl"]};
            }
            .footer {
              display: flex;
              justify-content: end;
              padding: ${schemes.spacing["2xl"]} ${schemes.spacing["3xl"]}
                ${schemes.spacing["3xl"]} ${schemes.spacing["3xl"]};
              align-items: center;
              gap: ${schemes.spacing.md};
              border-top: solid 1px ${colors.border.dark.bgLighten};
            }
            .header {
              display: flex;
              justify-content: space-between;
              padding: ${schemes.spacing["3xl"]};
              align-items: center;
              gap: ${schemes.spacing.xl};
              border-bottom: solid 1px ${colors.border.dark.bgLighten};
              .header-title {
                flex-shrink: 0;
                font-size: ${fonts.body.body.fontSize};
                font-weight: ${fonts.body.bodyBold.fontWeight};
                line-height: ${fonts.heading.h3.lineHeight};
                margin: 0;
              }
              .btn-close {
                position: initial;
                width: 1.5rem;
                height: 1.5rem;
                cursor: pointer;
                background: none;
                border: none;
              }
            }
          }
        }
      `}</style>
    </>
  )
}

export default React.memo(BaseModalWithTitle)
