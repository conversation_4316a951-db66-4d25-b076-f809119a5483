import BaseSideModal from "@/components/modal/BaseSideModal"
import IconButton from "@/components/elements/IconButton"
import { getAssetPath, randNumberFromStr } from "@/utils/util"
import { ColorType } from "@/common/types/colorType"
import LabelTag from "@/components/elements/LabelTag"
import ColumnCreator from "@/components/table/columns/Creator"
import { colors, fonts, schemes } from "@/utils/theme/style"
import React, { useEffect, useMemo, useRef } from "react"
import CreatorDetailVideos from "@/components/modal/CreatorDetailVideos"
import Link from "next/link"
import Donut<PERSON>hart from "@/components/charts/DonutChart"
import { contentStyleData, contentTypeData } from "@/common/utils/tableDetail"

function CreatorDetailModal({
  isOpen,
  setIsOpen,
  creator,
}: {
  isOpen: boolean
  setIsOpen: (val: boolean) => void
  creator: any
}) {
  const gender = "Female"
  const colorsArr: ColorType[] = [
    "red",
    "orange",
    "yellow",
    "green",
    "cyan",
    "blue",
    "indigo",
    "purple",
    "pink",
  ]
  const scrollRef = useRef<HTMLDivElement>(null)

  const sortVideos = useMemo(() => {
    const videos = creator?.tiktok_creator_posts ?? []
    videos.sort((a: any, b: any) => {
      return (
        new Date(b.create_time).getTime() - new Date(a.create_time).getTime()
      )
    })

    videos.sort((a: any, b: any) => b.is_pinned - a.is_pinned)

    return videos
  }, [creator])

  const contentsTypes = useMemo<any[]>(() => {
    return contentTypeData.map((item: any) => {
      const raw = creator?.content_type_coverage?.[item.name]

      let value = 0
      if (typeof raw === "number") {
        value = raw
      }

      if (raw && typeof raw.percentage !== "undefined") {
        value = Number(String(raw.percentage).replace(/[^0-9.]+/g, ""))
      }

      return {
        name: item.name,
        value,
        color: item.color,
        colorCode: item.colorCode,
      }
    })
  }, [creator?.content_type_coverage])

  const contentsStyle = useMemo(() => {
    return Object.entries(contentStyleData)
      .map(([key, val]) => {
        return val.map((item: any) => {
          const raw = creator?.content_style_coverage?.[key]?.[item.name]
          const value = raw?.percentage
            ? Number(String(raw.percentage).replace(/[^0-9.]+/g, ""))
            : 0

          return { ...item, value }
        })
      })
      .flat()
  }, [creator?.content_style_coverage])

  useEffect(() => {
    if (scrollRef.current && isOpen) {
      scrollRef.current.scrollTop = 0
    }
  }, [isOpen])

  return (
    <>
      {creator ? (
        <BaseSideModal isOpen={isOpen} setIsOpen={setIsOpen}>
          <div className={"modal-head"}>
            <div onClick={() => setIsOpen(false)}>
              <IconButton
                path={getAssetPath("line_arrow_right.svg")}
                color={colors.schems.dark.disabled}
                label={"close"}
              ></IconButton>
            </div>
            <div>
              <h3>{creator.unique_id}</h3>
            </div>
          </div>
          <div className={"modal-body"} ref={scrollRef}>
            <div>
              <section className={"information"}>
                <div className={"creator-info"}>
                  <div>
                    <h5>Creator Info</h5>
                    <div>
                      <Link
                        href={`https://www.tiktok.com/@${creator.unique_id}`}
                        target={"_blank"}
                        style={{ textDecoration: "none" }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <ColumnCreator creator={creator} />
                      </Link>
                    </div>
                  </div>
                  <table>
                    <tbody>
                      <tr>
                        <th>Summary</th>
                        <td>{creator.signature ?? ""}</td>
                      </tr>
                      <tr>
                        <th>Age range</th>
                        <td>{creator.age_range ?? "-"}</td>
                      </tr>
                      <tr>
                        <th>Gender</th>
                        <td>
                          {creator.gender ? (
                            <LabelTag
                              color={
                                (gender === "Female"
                                  ? "red"
                                  : "blue") as ColorType
                              }
                            >
                              {creator.gender}
                            </LabelTag>
                          ) : (
                            "-"
                          )}
                        </td>
                      </tr>
                      <tr>
                        <th>Ethnicity race</th>
                        <td>
                          {creator.race ? (
                            <LabelTag color={"gray"}>{creator.race}</LabelTag>
                          ) : (
                            "-"
                          )}
                        </td>
                      </tr>
                      <tr>
                        <th>Skin types</th>
                        <td>
                          {creator.skin_types?.length
                            ? creator.skin_types.map(
                                (item: string, index: number) => (
                                  <LabelTag
                                    key={index}
                                    color={
                                      colorsArr[
                                        randNumberFromStr(item) %
                                          colorsArr.length
                                      ] as ColorType
                                    }
                                  >
                                    {item}
                                  </LabelTag>
                                )
                              )
                            : "-"}
                        </td>
                      </tr>
                      {creator.body_type && (
                        <tr>
                          <th>Body type</th>
                          <td>
                            <LabelTag
                              // key={index}
                              color={
                                colorsArr[
                                  randNumberFromStr(creator.body_type) %
                                    colorsArr.length
                                ] as ColorType
                              }
                            >
                              {creator.body_type}
                            </LabelTag>
                          </td>
                        </tr>
                      )}
                      {creator.hair_type && (
                        <tr>
                          <th>Hair type</th>
                          <td>
                            <LabelTag
                              // key={index}
                              color={
                                colorsArr[
                                  randNumberFromStr(creator.hair_type) %
                                    colorsArr.length
                                ] as ColorType
                              }
                            >
                              {creator.hair_type}
                            </LabelTag>
                          </td>
                        </tr>
                      )}
                      {creator.hair_color && (
                        <tr>
                          <th>Hair color</th>
                          <td>
                            <LabelTag
                              // key={index}
                              color={
                                colorsArr[
                                  randNumberFromStr(creator.hair_color) %
                                    colorsArr.length
                                ] as ColorType
                              }
                            >
                              {creator.hair_color}
                            </LabelTag>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
                <div className={"contents-info"}>
                  <div>
                    <div>
                      <h5>Contents Type</h5>
                      <p>Last 30 videos</p>
                    </div>
                    <div className={"chart"}>
                      <div>
                        <DonutChart data={contentsTypes} />
                      </div>
                      <div>
                        <ul>
                          {contentsTypes.map((item, i) => (
                            <li key={i}>
                              <LabelTag
                                type={"status"}
                                color={item.colorCode}
                                lightness={
                                  item.colorCode === ("gray" as ColorType)
                                    ? "lighten"
                                    : "default"
                                }
                              >
                                {item.name}
                              </LabelTag>
                              <span>{parseInt(item.value)}%</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div>
                      <label>Contents tags</label>
                      <div>
                        {creator.content_tags?.length
                          ? creator.content_tags?.map(
                              (item: string, index: number) => (
                                <LabelTag
                                  key={index}
                                  color={
                                    colorsArr[
                                      randNumberFromStr(item) % colorsArr.length
                                    ] as ColorType
                                  }
                                >
                                  {item}
                                </LabelTag>
                              )
                            )
                          : "-"}
                      </div>
                    </div>
                    <div>
                      <label>Brand mentions</label>
                      <div>
                        {creator.brand_mentions?.length
                          ? creator.brand_mentions.map(
                              (item: string, index: number) => (
                                <LabelTag
                                  key={index}
                                  color={
                                    colorsArr[
                                      randNumberFromStr(item) % colorsArr.length
                                    ] as ColorType
                                  }
                                >
                                  {item}
                                </LabelTag>
                              )
                            )
                          : "-"}
                      </div>
                    </div>
                  </div>
                </div>
                <div className={"contents-style"}>
                  <div>
                    <div>
                      <h5>Contents Style</h5>
                      <p>Last 30 videos</p>
                    </div>
                    <div>
                      <div className={"graph"}>
                        {contentsStyle.map((item, i) => (
                          <div
                            key={i}
                            style={{
                              height: `${item.value}%`,
                              background: item.color,
                            }}
                          ></div>
                        ))}
                      </div>
                      <div className={"data"}>
                        {Object.entries(contentStyleData).map(([key, val]) => (
                          <div key={key}>
                            <label>{key}</label>
                            <ul>
                              {val.map((item, i) => (
                                <li key={i}>
                                  <LabelTag
                                    type={"status"}
                                    color={item.colorCode as ColorType}
                                    lightness={
                                      item.colorCode === ("gray" as ColorType)
                                        ? "lighten"
                                        : "default"
                                    }
                                  >
                                    {item.name}
                                  </LabelTag>
                                  <span>
                                    {parseInt(
                                      contentsStyle.find(
                                        (data) => item.name === data.name
                                      ).value
                                    )}
                                    %
                                  </span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <div>
                    <label>Skin</label>
                    <div>
                      {creator.skin_concerns?.length
                        ? creator.skin_concerns.map(
                            (item: string, index: number) => (
                              <LabelTag
                                key={index}
                                color={
                                  colorsArr[
                                    randNumberFromStr(item) % colorsArr.length
                                  ] as ColorType
                                }
                              >
                                {item}
                              </LabelTag>
                            )
                          )
                        : "-"}
                    </div>
                  </div>
                </div>
                <div>
                  <div>
                    <label>Makeup</label>
                    <div>
                      {creator.makeup_style !== "" && (
                        <LabelTag color={colorsArr[0] as ColorType}>
                          {creator.makeup_style}
                        </LabelTag>
                      )}
                      {creator.makeup_categories?.length > 0 &&
                        creator.makeup_categories.map(
                          (item: string, index: number) => (
                            <LabelTag
                              key={index}
                              color={
                                colorsArr[
                                  randNumberFromStr(item) % colorsArr.length
                                ] as ColorType
                              }
                            >
                              {item}
                            </LabelTag>
                          )
                        )}
                      {creator.makeup_style === "" &&
                        creator.makeup_categories?.length === 0 &&
                        "-"}
                    </div>
                  </div>
                </div>
                <div>
                  <div>
                    <label>Hair & Body</label>
                    <div>
                      {creator.hair_concerns?.length > 0 &&
                        creator.hair_concerns.map(
                          (item: string, index: number) => (
                            <LabelTag
                              key={index}
                              color={
                                colorsArr[
                                  randNumberFromStr(item) % colorsArr.length
                                ] as ColorType
                              }
                            >
                              {item}
                            </LabelTag>
                          )
                        )}
                      {creator.bodycare_categories?.length > 0 &&
                        creator.bodycare_categories.map(
                          (item: string, index: number) => (
                            <LabelTag
                              key={index}
                              color={
                                colorsArr[
                                  randNumberFromStr(item) % colorsArr.length
                                ] as ColorType
                              }
                            >
                              {item}
                            </LabelTag>
                          )
                        )}
                      {creator.hair_concerns?.length === 0 &&
                        creator.bodycare_categories?.length === 0 &&
                        "-"}
                    </div>
                  </div>
                </div>
              </section>
              <section className={"videos"}>
                <div>
                  <h4>Videos</h4>
                  <p>
                    {new Date(creator.post_updated_at).toLocaleDateString()}{" "}
                    Update
                  </p>
                </div>
                <ul>
                  {sortVideos.map((item: any, index: number) => (
                    <CreatorDetailVideos post={item} key={index} />
                  ))}
                  {sortVideos.length < 6 &&
                    [...Array(6 - sortVideos.length)].map(
                      (_, index: number) => <CreatorDetailVideos key={index} />
                    )}
                </ul>
              </section>
            </div>
          </div>
        </BaseSideModal>
      ) : (
        <></>
      )}
      <style jsx>{`
        .modal-head {
          display: flex;
          align-items: center;
          height: 3.5rem;
          padding: 0 ${schemes.spacing["2xl"]};
          > div {
            &:first-child {
              flex-shrink: 0;
              margin-left: -0.5rem;
              margin-right: -0.5rem;
              button:hover {
                background: none;
              }
            }
            &:last-child {
              flex: 1;
              text-align: center;
              font-size: ${fonts.body.large.fontSize};
              font-weight: ${fonts.body.large.fontWeight};
              line-height: ${fonts.body.large.lineHeight};
            }
          }
        }
        .modal-body {
          height: calc(100vh - 3.5rem);
          overflow-y: scroll;
          > div {
            padding: ${schemes.spacing["3xl"]};
            .information {
              display: grid;
              grid-gap: ${schemes.spacing["2xl"]};
              grid-template-columns: repeat(3, 1fr);
              > div {
                border-radius: ${schemes.radius.sm};
                background: ${colors.bg.dark.surfaceBright};
                padding: ${schemes.spacing["3xl"]} 0;

                &.contents-info {
                  .chart {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    gap: ${schemes.spacing["3xl"]};
                    margin-top: ${schemes.spacing.md};

                    > div:last-of-type {
                      flex: 1;
                    }
                  }
                }
                > div:first-child > div:first-child {
                  display: flex;
                  align-items: center;
                  gap: ${schemes.spacing.md};
                  justify-content: space-between;
                  > p {
                    margin: 0;
                    color: ${colors.schems.dark.onDisabled};
                    font-size: ${fonts.body.label.fontSize};
                    font-weight: ${fonts.body.label.fontWeight};
                    line-height: ${fonts.body.label.lineHeight};
                  }
                }
                > div,
                > table {
                  padding: ${schemes.spacing["2xl"]} ${schemes.spacing["3xl"]};
                  border-bottom: 1px solid ${colors.border.dark.bgLighten};
                  &:first-child {
                    padding-top: 0;
                  }
                  &:last-child {
                    padding-bottom: 0;
                    border-bottom: none;
                  }
                  h5 {
                    margin: 0;
                    font-size: ${fonts.body.large.fontSize};
                    font-weight: ${fonts.body.largeBold.fontWeight};
                    line-height: ${fonts.body.large.lineHeight};
                  }
                  h5 + div {
                    margin-top: ${schemes.spacing.md};
                  }
                  th {
                    color: ${colors.schems.dark.onDisabled};
                    font-size: ${fonts.body.body.fontSize};
                    font-weight: ${fonts.body.body.fontWeight};
                    line-height: ${fonts.body.body.lineHeight};
                    text-align: left;
                  }
                  th {
                    width: 104px;
                    vertical-align: top;
                    margin-right: ${schemes.spacing.md};
                  }
                  td {
                    line-break: anywhere;
                    font-size: ${fonts.body.body.fontSize};
                    font-weight: ${fonts.body.body.fontWeight};
                    line-height: ${fonts.body.body.lineHeight};
                    &:has(label) {
                      display: flex;
                      flex-wrap: wrap;
                      gap: ${schemes.spacing.md};
                    }
                  }
                }
                > div:not(:first-child):last-child {
                  display: flex;
                  flex-direction: column;
                  gap: ${schemes.spacing.md};
                  > div:not(:first-child):last-child {
                    display: flex;
                    flex-direction: column;
                    gap: ${schemes.spacing.md};
                  }
                }
                label {
                  color: ${colors.schems.dark.onDisabled};
                  font-size: ${fonts.body.body.fontSize};
                  font-weight: ${fonts.body.body.fontWeight};
                  line-height: 25px;
                  text-align: left;
                  & + div {
                    display: flex;
                    flex-wrap: wrap;
                    gap: ${schemes.spacing.md};
                    margin-top: ${schemes.spacing.sm};
                  }
                }
                &.contents-style {
                  > div {
                    > div:last-of-type {
                      margin-top: ${schemes.spacing.md};
                      display: flex;
                      justify-content: space-between;
                      align-items: start;
                      gap: ${schemes.spacing["3xl"]};
                      > .graph {
                        width: 4px;
                        height: 362px;
                        background: ${colors.schems.dark.secondary};
                        overflow: hidden;
                      }
                      > .data {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        gap: ${schemes.spacing.sm};
                      }
                    }
                  }
                }
              }
              ul {
                margin: 0;
                padding: 0;
                width: 100%;
                list-style: none;
                > li {
                  height: 25px;
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  gap: ${schemes.spacing.md};
                  &:not(:last-of-type) {
                    border-bottom: 1px solid ${colors.border.dark.bgLighten};
                  }
                }
              }
            }
            .videos {
              margin-top: ${schemes.spacing["3xl"]};
              > div {
                display: flex;
                align-items: center;
                gap: ${schemes.spacing.md};
                h4 {
                  margin: 0;
                  font-size: ${fonts.heading.h4.fontSize};
                  font-weight: ${fonts.heading.h4.fontWeight};
                  line-height: ${fonts.heading.h4.lineHeight};
                  & + p {
                    margin: 0;
                    color: ${colors.schems.dark.onDisabled};
                    font-size: ${fonts.body.label.fontSize};
                    font-weight: ${fonts.body.label.fontWeight};
                    line-height: ${fonts.body.label.lineHeight};
                  }
                }
              }
              > ul {
                margin: ${schemes.spacing.md} 0 0;
                padding: 0;
                list-style: none;
                display: grid;
                grid-gap: ${schemes.spacing["2xl"]};
                grid-template-columns: repeat(6, 1fr);
              }
            }
          }
        }
      `}</style>
    </>
  )
}

export default React.memo(CreatorDetailModal)
