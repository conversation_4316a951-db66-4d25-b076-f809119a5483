import BaseModal from "@/components/modal/BaseModal"
import Button from "@/components/elements/Button"
import { colors, fonts, schemes } from "@/utils/theme/style"

export default function DeleteTagModal({
  tag,
  isOpen,
  setIsOpen,
  deleteTag,
  isDeleteLoading,
}: {
  tag: any
  isOpen: boolean
  setIsOpen: (val: boolean) => void
  deleteTag: (tagId: number) => Promise<void>
  isDeleteLoading: boolean
}) {
  return (
    <>
      <BaseModal isOpen={isOpen} setIsOpen={setIsOpen}>
        <div className="modal-body">
          <div>
            <h3>Delete Custom Tag</h3>
            <p>Do you want to delete the custom tag?</p>
            <div>A total of {tag.count} creators</div>
          </div>
          <div>
            <Button style={"text"} onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button
              style={"secondary"}
              color={colors.sys.dark.red.default}
              onClick={() => deleteTag(tag.id)}
              disabled={isDeleteLoading}
              isLoading={isDeleteLoading}
            >
              Delete
            </Button>
          </div>
        </div>
      </BaseModal>
      <style jsx>{`
        .modal-body {
          > div:first-of-type {
            display: flex;
            justify-content: center;
            align-items: start;
            flex-direction: column;
            gap: ${schemes.spacing["2xl"]};
            padding-bottom: ${schemes.spacing["2xl"]};
            > * {
              width: 100%;
              margin: 0;
              padding: 0;
            }
            > h3 {
              font-size: ${fonts.heading.h3.fontSize};
              font-weight: ${fonts.heading.h3.fontWeight};
              line-height: ${fonts.heading.h3.lineHeight};
            }
            > p,
            > div {
              font-size: ${fonts.body.large.fontSize};
              font-weight: 500;
              line-height: ${fonts.body.large.lineHeight};
            }
            > div {
              border-radius: ${schemes.radius.sm};
              border: 1px solid ${colors.border.dark.bgLighten};
              padding: ${schemes.spacing["2xl"]};
            }
          }
          > div:last-of-type {
            display: flex;
            flex-direction: row;
            justify-content: end;
            width: 100%;
            gap: ${schemes.spacing.md};
            padding-top: ${schemes.spacing["2xl"]};
          }
        }
      `}</style>
    </>
  )
}
