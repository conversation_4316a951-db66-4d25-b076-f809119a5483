import { bodies, colors, fonts, schemes } from "@/utils/theme/style"
import BaseModalWithTitle from "@/components/modal/BaseModalWithTitle"
import React, { useMemo, useRef, useState } from "react"
import SaveTags from "@/components/table/SaveTags"
import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"
import * as XLSX from "xlsx"
import { notifyError } from "@/components/modal/Toasts"
import { importBookmarksWithFile } from "@/service/workspace"
import { useAuth } from "@/common/hooks/useAuthProvider"
import useWorkspace from "@/store/useWorkspace"
import IconButton from "@/components/elements/IconButton"

export default function BulkSetCustomTagModal({
  isOpen,
  setIsOpen,
  setIsTagAddModal,
  reload,
}: {
  isOpen: boolean
  setIsOpen: (val: boolean) => void
  setIsTagAddModal: (val: boolean) => void
  reload: () => void
}) {
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const [dragging, setDragging] = useState(false)
  const [loading, setLoading] = useState(false)
  const [file, setFile] = useState<File | null>(null)
  const [tagId, setTagId] = useState<number | null>(null)
  const [failedUniqueIds, setFailedUniqueIds] = useState<string[]>([])
  const [importResult, setImportResult] = useState<null | {
    totalCount: number
    failedCount: number
    successCount: number
    failedUniqueIds: string[]
  }>(null)
  const [error, setError] = useState<string>("")
  const auth = useAuth()
  const { currentWorkspace, setTags } = useWorkspace()
  const workspaceId = currentWorkspace?.id

  const completed = useMemo(() => {
    return !!file && !!tagId && error.length === 0
  }, [file, tagId, error])

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  const checkValidateFile = (selectedFile: File) => {
    const extension = selectedFile.name.split(".").pop()?.toLowerCase()
    if (!["xlsx", "xls", "csv"].includes(extension ?? "")) {
      setError("Unsupported file type. Please upload a CSV or Excel file only.")
      return
    }

    const reader = new FileReader()

    if (extension === "csv") {
      reader.readAsText(selectedFile, "utf-8")
    } else {
      reader.readAsBinaryString(selectedFile)
    }

    reader.onload = (event: ProgressEvent<FileReader>) => {
      const fileContent = event.target?.result
      if (!fileContent) {
        setError("The file is empty.")
        return
      }

      try {
        let firstCellValue: string | undefined = undefined

        if (extension === "csv") {
          const text = fileContent as string

          // 첫 줄 확인
          const lines = text.split(/\r?\n/).filter(Boolean)
          if (lines.length === 0) {
            setError("The file is empty.")
            return
          }

          firstCellValue = lines[0].split(",")[0]?.trim()
          if (firstCellValue?.toLowerCase() !== "creator") {
            setError("The file format is invalid.")
            return
          }
        } else {
          const workbook = XLSX.read(fileContent, { type: "binary" })
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]

          if (!worksheet) {
            setError("Unable to find a valid Excel sheet.")
            return
          }

          const cellA1 = worksheet["A1"]
          firstCellValue = cellA1?.v
          if (firstCellValue?.toLowerCase() !== "creator") {
            setError("The file format is invalid.")
            return
          }
        }
        setError("")
      } catch (err) {
        console.error(err)
        setError(
          "An error occurred while processing the file. Please check the encoding or file contents."
        )
      }
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0])

      checkValidateFile(e.target.files[0])
    }
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setDragging(false)

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0]
      setFile(file)
      checkValidateFile(file)
    }
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setDragging(true)
  }

  const handleDragLeave = () => {
    setDragging(false)
  }

  const handleImport = async () => {
    if (!tagId) {
      notifyError("Please select a tag.")
    }

    if (!file) {
      notifyError("Please select a file.")
    }

    setImportResult(null)
    setLoading(true)
    try {
      const response = await importBookmarksWithFile({
        accessToken: auth.session?.access_token as string,
        tagId: tagId as number,
        workspaceId: workspaceId as number,
        file: file as File,
      })
      const { totalCount, failedCount, notMatchUniqueIds, successCount, tags } =
        response.data
      setFailedUniqueIds(notMatchUniqueIds)
      setImportResult({
        totalCount,
        failedCount,
        successCount,
        failedUniqueIds: notMatchUniqueIds,
      })
      setTags(tags)
      reload()
      // setIsOpen(false)
    } catch (e) {
      console.log("error", e)
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <BaseModalWithTitle
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        headerText={"Import creator"}
        confirmText={"Import"}
        confirmAction={handleImport}
        confirmDisabled={!completed}
        confirmLoading={loading}
      >
        <div className="modal">
          <input
            className={"file"}
            ref={fileInputRef}
            type="file"
            accept=".csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            onChange={handleFileChange}
          />
          <div className={"modal-body"}>
            <div className={"tags"}>
              <p>Custom tag</p>
              <SaveTags
                tagIds={tagId ? [tagId] : []}
                setTagIds={(tagIds: number[]) => {
                  setTagId(tagIds.length ? tagIds[tagIds.length - 1] : null)
                }}
                setIsTagAddModal={setIsTagAddModal}
                fullWidth={true}
              />
            </div>
            {file ? (
              <div className={"file-name"}>
                <span>{file.name}</span>
                <IconButton
                  path={getAssetPath("cancel.svg")}
                  color={colors.schems.dark.disabled}
                  size={"2rem"}
                  onClick={() => {
                    setFile(null)
                    setError("")
                    setImportResult(null)
                    if (fileInputRef.current) {
                      fileInputRef.current.value = ""
                    }
                  }}
                />
              </div>
            ) : (
              <div className={"form"}>
                <div
                  className={`form-upload ${dragging ? "on-drag" : ""}`}
                  onClick={handleClick}
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                >
                  <div> Click or Drag to upload</div>
                </div>
              </div>
            )}
            {error && (
              <div className={"error"}>
                <Icon
                  path={getAssetPath("cancel.svg")}
                  color={colors.sys.dark.red.default}
                  size={"1rem"}
                />
                <p>Upload failed : {error}</p>
              </div>
            )}
            {importResult && (
              <>
                {importResult.failedCount > 0 ? (
                  <>
                    <div className={"error"}>
                      <Icon
                        path={getAssetPath("cancel.svg")}
                        color={colors.sys.dark.red.default}
                        size={"1rem"}
                      />
                      <p>{`${importResult.failedCount} creators failed to upload.`}</p>
                    </div>
                    <ul className={"fail-list"}>
                      {failedUniqueIds.map((uniqueId) => (
                        <li key={uniqueId}>{uniqueId}</li>
                      ))}
                    </ul>
                  </>
                ) : (
                  <div className={"success"}>
                    <Icon
                      path={getAssetPath("check_circle.svg")}
                      color={colors.sys.dark.green.default}
                      size={"1rem"}
                    />
                    <p>{`${importResult.successCount} creators added.`}</p>
                  </div>
                )}
              </>
            )}
            <div className={"information"}>
              <p>Reference Example</p>
              <div className={"table"}>
                <div>Creator</div>
                <div>.alexmoreira</div>
              </div>
              <ul>
                <li>
                  Use the correct &#34;xlsx&#34;, &#34;xls&#34;, &#34;csv&#34;
                  format.
                </li>
                <li>Creator column is required.</li>
              </ul>
            </div>
          </div>
        </div>
      </BaseModalWithTitle>
      <style jsx>{`
        .modal {
          .file {
            display: none;
          }
          .modal-body {
            display: flex;
            flex-direction: column;
            gap: ${schemes.spacing["2xl"]};
            overflow-y: auto;
            height: 100%;
            .error {
              padding: ${schemes.spacing.md} ${schemes.spacing.xl};
              color: ${colors.sys.dark.red.darken};
              font-size: ${fonts.body.body.fontSize};
              font-weight: ${fonts.body.body.fontWeight};
              line-height: ${fonts.body.body.lineHeight};
              display: flex;
              justify-items: start;
              align-items: center;
              gap: ${schemes.spacing.md};
              background: ${colors.sys.dark.red.lighten};
              border-radius: ${schemes.radius.sm};
              p {
                margin: 0;
                padding: 0;
              }
            }
            .success {
              padding: ${schemes.spacing.md} ${schemes.spacing.xl};
              color: ${colors.sys.dark.green.darken};
              font-size: ${fonts.body.body.fontSize};
              font-weight: ${fonts.body.body.fontWeight};
              line-height: ${fonts.body.body.lineHeight};
              display: flex;
              justify-items: start;
              align-items: center;
              gap: ${schemes.spacing.md};
              background: ${colors.sys.dark.green.lighten};
              border-radius: ${schemes.radius.sm};
              p {
                margin: 0;
                padding: 0;
              }
            }
            .fail-list {
              margin: 0;
              color: ${colors.schems.dark.onBase};
              padding: ${schemes.spacing["2xl"]};
              background-color: ${colors.schems.dark.secondary};
              height: 215px;
              overflow-y: auto;
              list-style: none;
              border-radius: ${schemes.radius.sm};
              li {
                padding: ${schemes.spacing.sm} 0;
                font-size: ${fonts.body.body.fontSize};
                font-weight: ${fonts.body.body.fontWeight};
              }
            }
            .file-name {
              display: flex;
              justify-content: space-between;
              align-items: center;
              border: 1px solid ${colors.border.dark.bgLighten};
              border-radius: ${schemes.radius.sm};
              width: 100%;
              font-size: ${fonts.body.body.fontSize};
              font-weight: ${fonts.body.body.fontWeight};
              line-height: ${fonts.body.body.lineHeight};
              padding: ${schemes.spacing.md} 0 ${schemes.spacing.md}
                ${schemes.spacing["2xl"]};
              button {
                border: none;
                background: none;
              }
            }

            .tags {
              width: 100%;
              p {
                font-size: ${fonts.body.label.fontSize};
                font-weight: ${fonts.body.label.fontWeight};
                line-height: ${fonts.body.label.lineHeight};
                padding: 0;
                margin: 0;
              }
            }
            .form {
              height: 335px;
              .form-upload {
                &.on-drag {
                  background-color: ${colors.gray["50"]};
                }
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: ${bodies.body.fontSize};
                font-weight: ${bodies.body.fontWeight};
                line-height: ${bodies.body.lineHeight};
                border: 1px dashed ${colors.sys.dark.gray.lighten};
                border-radius: ${schemes.radius.sm};
                cursor: pointer;
              }
            }

            .information {
              display: flex;
              align-items: center;
              justify-content: center;
              flex-direction: column;
              gap: ${schemes.spacing.md};
              p {
                font-size: ${fonts.body.body.fontSize};
                font-weight: ${fonts.body.body.fontWeight};
                line-height: ${fonts.body.body.lineHeight};
                margin: 0;
                padding: 0;
                width: 100%;
              }
              .table {
                width: 100%;
                border: 1px solid ${colors.opacity.dark["08"]};
                div:first-child {
                  font-family: ${fonts.heading.fontFamily};
                  font-size: ${fonts.body.labelBold.fontSize};
                  font-weight: ${fonts.body.labelBold.fontWeight};
                  line-height: ${fonts.body.labelBold.lineHeight};
                  padding: ${schemes.spacing.md} ${schemes.spacing.xl};
                  border-bottom: 1px solid ${colors.opacity.dark["08"]};
                }
                div:last-child {
                  padding: ${schemes.spacing.md} ${schemes.spacing.xl};
                  font-size: ${fonts.body.label.fontSize};
                  font-weight: ${fonts.body.label.fontWeight};
                  line-height: ${fonts.body.label.lineHeight};
                }
              }
              ul {
                width: 100%;
                margin: 0;
                padding-left: 1rem;
                font-size: ${fonts.body.body.fontSize};
                font-weight: ${fonts.body.body.fontWeight};
                line-height: ${fonts.body.body.lineHeight};
                color: ${colors.base.white};
              }
            }
          }
        }
      `}</style>
    </>
  )
}
