import { colors } from "@/utils/theme/style"
import React from "react"

function BaseSideModal({
  children,
  isOpen,
  setIsOpen,
}: {
  children?: React.ReactNode
  isOpen: boolean
  setIsOpen: (val: boolean) => void
}) {
  return (
    <>
      <div
        className={`modal ${isOpen ? "open" : ""}`}
        onClick={() => setIsOpen(false)}
      >
        <div onClick={(e) => e.stopPropagation()}>{children}</div>
      </div>
      <style jsx>{`
        .modal {
          z-index: 100;
          visibility: hidden;
          position: fixed;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0);
          &.open {
            visibility: visible;
          }
          > div {
            position: absolute;
            max-width: 1080px;
            top: 0;
            right: 0;
            bottom: 0;
            background: ${colors.bg.dark.surface};
            border: 2px solid ${colors.border.dark.bgLighten};
          }
        }
      `}</style>
    </>
  )
}

export default React.memo(BaseSideModal)
