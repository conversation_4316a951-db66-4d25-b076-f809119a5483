import { toast, ToastOptions } from "react-hot-toast"
import { colors } from "@/utils/theme/style"

export const notifySuccess = (message: string, options?: ToastOptions) => {
  let toastOptions = options ?? {}
  toastOptions = {
    ...toastOptions,
    style: {
      backgroundColor: `${colors.sys.dark.green.lighten}`,
      color: `${colors.sys.dark.green.darken}`,
    },
    position: toastOptions.position ?? "bottom-right",
  }

  toast.success(message, toastOptions)
}

export const notifyError = (message: string, options?: ToastOptions) => {
  let toastOptions = options ?? {}
  toastOptions = {
    ...toastOptions,
    style: {
      backgroundColor: `${colors.sys.dark.red.lighten}`,
      color: `${colors.sys.dark.red.darken}`,
    },
    position: toastOptions.position ?? "bottom-right",
  }

  toast.error(message, toastOptions)
}
