import BaseModal from "@/components/modal/BaseModal"
import Button from "@/components/elements/Button"
import TagEdit from "@/components/saved/TagEdit"
import { WorkSpaceTagType } from "@/common/types/WorkspaceType"
import { useAuth } from "@/common/hooks/useAuthProvider"
import useWorkspace from "@/store/useWorkspace"
import { useState } from "react"
import { fonts, schemes } from "@/utils/theme/style"

export default function AddTagModal({
  isOpen,
  setIsOpen,
}: {
  isOpen: boolean
  setIsOpen: (val: boolean) => void
}) {
  const auth = useAuth()
  const accessToken = auth.session?.access_token || null
  const { currentWorkspace, tags, addTag, updateTag, setTags } = useWorkspace()
  const workspaceId = currentWorkspace?.id || 0
  const [tag, setTag] = useState<WorkSpaceTagType | null>(null)

  return (
    <>
      <BaseModal isOpen={isOpen} setIsOpen={setIsOpen}>
        <div className="modal-body">
          <div>
            <h3>Create custom tag</h3>
            <div>
              <TagEdit
                addTag={addTag}
                tag={tag}
                setTag={setTag}
                tags={tags}
                setTags={setTags}
                updateTag={updateTag}
                accessToken={accessToken}
                workspaceId={workspaceId}
                setModalOpen={setIsOpen}
              />
            </div>
          </div>
        </div>
      </BaseModal>
      <style jsx>{`
        .modal-body {
          height: calc(100vh - 12.5rem);
          > div {
            height: 100%;
            display: flex;
            justify-content: space-between;
            align-items: start;
            flex-direction: column;
            gap: ${schemes.spacing["2xl"]};
            > * {
              width: 100%;
              margin: 0;
              padding: 0;
            }
            > h3 {
              flex-shrink: 0;
              font-size: ${fonts.heading.h3.fontSize};
              font-weight: ${fonts.heading.h3.fontWeight};
              line-height: ${fonts.heading.h3.lineHeight};
            }
            > div {
              flex: 1;
            }
          }
        }
      `}</style>
    </>
  )
}
