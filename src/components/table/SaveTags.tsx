import useWorkspace from "@/store/useWorkspace"
import Checkbox from "@/components/elements/Checkbox2"
import { ColorType } from "@/common/types/colorType"
import React, { useEffect, useMemo, useRef, useState } from "react"
import Icon from "@/components/elements/Icon"
import { getAssetPath, useWindowDimensions } from "@/utils/util"
import { colors, fonts, schemes } from "@/utils/theme/style"
import Button from "@/components/elements/Button"

export default function SaveTags({
  tagIds = [],
  setTagIds,
  setIsTagAddModal,
  position = "left",
  fullWidth,
}: {
  tagIds: number[]
  setTagIds: (val: number[]) => void
  setIsTagAddModal?: (isAddModal: boolean) => void
  position?: "left" | "right"
  fullWidth?: boolean
}) {
  const { currentWorkspace, tags, addTag, updateTag, setTags } = useWorkspace()
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)
  const { height, width } = useWindowDimensions()

  const filterTags = useMemo(
    () => tags.filter((tag) => !tag.is_default),
    [tags]
  )

  const openPosition = useMemo(() => {
    if (isOpen) {
      const rectTop = buttonRef.current?.getBoundingClientRect().top ?? 0
      const heightSubRect = height - rectTop
      const tagHeight =
        (filterTags.length === 0
          ? 156
          : filterTags.length > 10
            ? 416
            : filterTags.length * 40 + 16) + 112

      if (height > tagHeight && heightSubRect < tagHeight) {
        return "up"
      }
    }
    return "down"
  }, [filterTags.length, height, isOpen])

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }

    // 바깥 클릭 이벤트 리스너 추가
    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      // 컴포넌트가 언마운트될 때 이벤트 리스너 제거
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [menuRef])

  const changeSelectLabel = useMemo(() => {
    switch (tagIds.length) {
      case 0:
        return "Select tags"
      case 1:
        return tags.find((tag) => tag.id === tagIds[0])?.name
      default:
        return `${tagIds.length} Selected`
    }
  }, [tagIds, tags])

  return (
    <>
      <div
        className={`edit-tags ${fullWidth ? "full-width" : ""} ${isOpen ? "open" : ""}`}
        ref={menuRef}
      >
        <button onClick={() => setIsOpen(!isOpen)} ref={buttonRef}>
          <span className={`${isOpen ? "selected" : ""}`}>
            {changeSelectLabel}
          </span>
          <Icon
            path={
              isOpen
                ? getAssetPath("line_arrow_up.svg")
                : getAssetPath("line_arrow_down.svg")
            }
            color={colors.schems.dark.onPrimary}
          />
        </button>
        <div className={`options ${openPosition}`}>
          {filterTags.length > 0 ? (
            <ul>
              {filterTags.map((tag) => (
                <li key={tag.id}>
                  <div>
                    <Checkbox
                      isChecked={tagIds.includes(tag.id as number)}
                      color={tag.color as ColorType}
                      onChange={(e) => {
                        setTagIds(
                          (e.target.checked
                            ? [...tagIds, tag.id]
                            : [
                                ...tagIds.filter(
                                  (item) => tag.id && item !== tag.id
                                ),
                              ]
                          ).filter((data) => data !== undefined)
                        )
                      }}
                    >
                      <div className={"tag-name"}>
                        <div className={"name"}>{tag.name}</div>
                        <span className={"count"}>
                          ({(tag.count || 0).toLocaleString()})
                        </span>
                      </div>
                    </Checkbox>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div>
              <div>
                <h4>Create custom tags</h4>
                <p>Categorize your creators.</p>
              </div>
              <div>
                <Button
                  style={"text"}
                  full={true}
                  icon={{
                    path: getAssetPath("add.svg"),
                  }}
                  onClick={() => {
                    if (setIsTagAddModal) setIsTagAddModal(true)
                  }}
                >
                  Add Tag
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
      <style jsx>{`
        .edit-tags {
          position: relative;
          width: fit-content;
          &.full-width {
            width: 100%;
            > button {
              width: 100%;
              text-align: left;
            }
            &.open {
              > .options {
                width: 100%;
                > ul {
                  width: 100%;
                }
              }
            }
          }
          & > button {
            border: 1px solid ${colors.border.dark.default};
            border-radius: ${schemes.radius.sm};
            background: ${colors.interactive.dark.hover};
            padding: 0 ${schemes.spacing.sm};

            width: 7.5rem;
            height: 2rem;
            display: inline-flex;
            justify-content: space-between;
            align-items: center;
            & > span {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              padding: 0 ${schemes.spacing.sm};
              color: ${colors.schems.dark.onDisabled};
              font-size: ${fonts.body.body.fontSize};
              font-weight: ${fonts.body.body.fontWeight};
              line-height: ${fonts.body.body.lineHeight};
              &.selected {
                color: ${colors.schems.dark.onBase};
              }
            }
            i {
              flex-shrink: 0;
            }
          }
          > .options {
            display: none;
          }
          &.open {
            > button {
              border-color: ${colors.border.dark.primary};
              background-color: ${colors.interactive.dark.hover};
              > span {
                color: ${colors.schems.dark.onBase};
              }
            }
            > .options {
              z-index: 10;
              position: absolute;
              display: block;
              width: 15rem;
              max-height: 26rem;
              padding: ${schemes.spacing.sm} 0;
              border: 1px solid ${colors.border.dark.bgLighten};
              border-radius: ${schemes.radius.sm};
              background: ${colors.bg.dark.surfaceBright};
              overflow-y: auto;
              white-space: nowrap;
              ${position === "right" ? "left: 0;" : "right: 0;"}
              ul {
                list-style: none;
                padding: ${schemes.spacing.md} 0;
                margin: 0;
                > li {
                  width: 100%;
                  height: 2.5rem;
                  padding: 0 ${schemes.spacing.xl};
                  display: flex;
                  flex-direction: column;
                  align-items: start;
                  justify-content: center;
                  & > div {
                    position: relative;
                    width: 100%;
                    overflow: hidden;
                    white-space: nowrap;
                    & .tag-name {
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      flex-wrap: nowrap;
                      gap: ${schemes.spacing.sm};
                      width: 100%;
                      & .name {
                        flex-grow: 1;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                      }
                      & .count {
                        flex-shrink: 0;
                      }
                    }
                  }
                }
              }
              &.up {
                bottom: 32px;
              }
              &.down {
                top: 32px;
              }
              > div {
                display: flex;
                gap: ${schemes.spacing.md};
                flex-direction: column;
                justify-content: center;
                align-items: center;
                width: 15rem;
                margin: 0 auto;
                padding: 1.84rem ${schemes.spacing["2xl"]};
                > div {
                  text-align: center;
                  width: 100%;
                  > h4 {
                    margin: 0;
                    font-size: ${fonts.body.large.fontSize};
                    font-weight: ${fonts.body.large.fontWeight};
                  }
                  > p {
                    margin: 0;
                    font-size: ${fonts.body.body.fontSize};
                    font-weight: ${fonts.body.body.fontWeight};
                    color: ${colors.schems.dark.onDisabled};
                  }
                }
                > span {
                  padding: 0 ${schemes.spacing.sm};
                  font-size: ${fonts.body.body.fontSize};
                  font-weight: ${fonts.body.body.fontWeight};
                  line-height: ${fonts.body.body.lineHeight};
                }
              }
            }
          }
        }
      `}</style>
    </>
  )
}
