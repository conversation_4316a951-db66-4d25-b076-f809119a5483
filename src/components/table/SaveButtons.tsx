import Button from "@/components/elements/Button"
import { getAssetPath } from "@/utils/util"
import SaveTags from "@/components/table/SaveTags"
import React, { useState } from "react"

export default function SaveButtons({
  creator,
  actionSaveBookmark,
  actionRemoveBookmark,
  setIsTagAddModal,
}: {
  creator: any
  actionSaveBookmark?: (uniqueId: string, tagIds: number[]) => Promise<void>
  actionRemoveBookmark?: (uniqueId: string) => Promise<void>
  setIsTagAddModal?: (isTagAddModal: boolean) => void
}) {
  const [isLoading, setIsLoading] = useState(false)
  const [tagIds, setTagIds] = useState<number[]>([])

  return (
    <>
      <div className={"button"} onClick={(e) => e.stopPropagation()}>
        {creator.saved ? (
          <Button
            style={"secondary"}
            size="sm"
            icon={{ path: getAssetPath("bookmark_true.svg") }}
            onClick={async () => {
              if (actionRemoveBookmark) {
                setIsLoading(true)
                await actionRemoveBookmark(creator.unique_id).finally(() =>
                  setIsLoading(false)
                )
              }
            }}
            isLoading={isLoading}
            disabled={isLoading}
          >
            Saved
          </Button>
        ) : (
          <>
            <SaveTags
              tagIds={tagIds}
              setTagIds={setTagIds}
              setIsTagAddModal={setIsTagAddModal}
            />
            <Button
              size="sm"
              icon={{
                path: getAssetPath("bookmark_false.svg"),
              }}
              onClick={async () => {
                if (actionSaveBookmark) {
                  setIsLoading(true)
                  await actionSaveBookmark(creator.unique_id, tagIds).finally(
                    () => setIsLoading(false)
                  )
                }
              }}
              isLoading={isLoading}
              disabled={isLoading}
            >
              Save
            </Button>
          </>
        )}
      </div>
      <style jsx>{``}</style>
    </>
  )
}
