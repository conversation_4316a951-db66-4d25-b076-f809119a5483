import Image from "next/image"
import { colors, schemes, bodies } from "@/utils/theme/style"
import ProductItem from "@/components/table/columns/ProductItem"

function Products({ products }: { products: any }) {
  return (
    <>
      {products && products.length > 0 && (
        <div className="products">
          <div className="product-images">
            {products[1] && products[1].cover_url && (
              <div>
                <Image
                  src={products[1].cover_url}
                  alt={products[1].product_name || "Product image"}
                  width="56"
                  height="56"
                  style={{ borderRadius: `${schemes.radius.sm}` }}
                />
                <div>
                  <span>+{products.length - 1}</span>
                </div>
              </div>
            )}
            {products[2] && products[2].cover_url && (
              <div>
                <Image
                  src={products[2].cover_url}
                  alt={products[2].product_name || "Product image"}
                  width="48"
                  height="48"
                  style={{ borderRadius: `${schemes.radius.sm}` }}
                />
                <div></div>
              </div>
            )}
            {products[3] && products[3].cover_url && (
              <div>
                <Image
                  src={products[3].cover_url}
                  alt={products[3].product_name || "Product image"}
                  width="32"
                  height="32"
                  style={{ borderRadius: `${schemes.radius.sm}` }}
                />
                <div></div>
              </div>
            )}
          </div>
          <div className={"product-list"}>
            <table>
              <thead>
                <tr>
                  <th>More Item</th>
                  <th>Item Sold</th>
                </tr>
              </thead>
              <tbody>
                {[...Array(5)].map((_: any, index: number) => {
                  const product = products?.[index + 1]
                  if (!product) return null
                  
                  return (
                    <tr key={product.id || `product-${index}`}>
                      <td>
                        <div className={"item-area"}>
                          <ProductItem product={product} />
                        </div>
                      </td>
                      <td>
                        {(product.total_video_sale_cnt ?? 0).toLocaleString()}
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
            {products.length > 5 && (
              <div>
                <span>+{products.length - 5} items</span>
              </div>
            )}
          </div>
        </div>
      )}
      <style jsx>{`
        .products {
          position: relative;
          .product-images {
            position: relative;
            width: 3.5rem;
            height: 3.5rem;
            > div {
              position: absolute;
              border-radius: ${schemes.radius.sm};
              > div {
                position: absolute;
                left: 0;
                top: 0;
                right: 0;
                bottom: 0;
                border-radius: ${schemes.radius.sm};
                display: flex;
                justify-content: center;
                align-items: center;
                > span {
                  text-shadow:
                    0 1px 3px rgba(0, 0, 0, 0.15),
                    0 1px 2px rgba(0, 0, 0, 0.3);
                  font-size: ${bodies.label.fontSize};
                  font-weight: ${bodies.label.fontWeight};
                  line-height: ${bodies.label.lineHeight};
                }
              }
              &:first-of-type {
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                z-index: 3;
                > div {
                  background: rgba(0, 0, 0, 0.25);
                }
              }
              &:nth-of-type(2) {
                width: 3rem;
                height: 3rem;
                left: 1rem;
                top: 50%;
                transform: translateY(-50%);
                z-index: 2;
                > div {
                  background: rgba(0, 0, 0, 0.5);
                }
              }
              &:nth-of-type(3) {
                width: 2rem;
                height: 2rem;
                left: 2.5rem;
                top: 50%;
                transform: translateY(-50%);
                z-index: 1;
                > div {
                  background: rgba(0, 0, 0, 0.75);
                }
              }
            }
          }
          .product-list {
            display: none;
          }
          &:hover > .product-list {
            display: block;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 4;
            width: 25rem;
            padding: ${schemes.spacing.md} 0;
            border-radius: ${schemes.radius.sm};
            border: 1px solid ${colors.border.dark.bgLighten};
            background: ${colors.bg.dark.surfaceBright};
            box-shadow:
              0 8px 12px 6px rgba(0, 0, 0, 0.15),
              0 4px 4px 0 rgba(0, 0, 0, 0.3);
            table {
              width: 100%;
              tr {
                &:hover {
                  background: none;
                }
              }
              > thead,
              > tbody {
                width: 100%;
                background: none;
                th {
                  width: auto !important;
                  padding: 0 1rem;
                  border-bottom: 1px solid #2c2c2c;
                  &:first-of-type {
                    text-align: left;
                  }
                }
                td {
                  padding: 0.5rem 1rem;
                  > .item-area {
                    width: 272px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                }
              }
            }
          }
        }
      `}</style>
    </>
  )
}
export default Products
