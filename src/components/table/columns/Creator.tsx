import Image from "next/image"
import React, { SyntheticEvent } from "react"
import { getAssetPath } from "@/utils/util"
import { colors, fonts, schemes } from "@/utils/theme/style"

function ColumnCreator({ creator }: { creator: any }) {
  return (
    <>
      <div className={`creator`}>
        <div>
          <Image
            src={creator.avatar || getAssetPath("avatar.svg")}
            alt={creator.nickname || creator.unique_id || 'creator'}
            className={"avatar"}
            width={48}
            height={48}
            // fetchPriority={"low"}
            priority
            onError={(e: SyntheticEvent<HTMLImageElement, Event>) => {
              e.currentTarget.src = getAssetPath("avatar.svg")
            }}
          />
        </div>
        <div>
          <h4>{creator.unique_id}</h4>
          <p>{creator.is_waiting_analysis ? "Analyzing ..." : creator.email}</p>
        </div>
      </div>
      <style jsx>{`
        .creator {
          display: flex;
          justify-content: start;
          align-items: center;
          gap: ${schemes.spacing.md};
          img {
            width: 3rem;
            height: 3rem;
            flex-shrink: 0;
            aspect-ratio: 1/1;
            border-radius: 62.5rem;
          }
          & > div:last-child {
            display: flex;
            flex-direction: column;
            align-items: stretch;
            overflow: hidden;
            white-space: nowrap;
            & > h4 {
              width: 100%;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              color: ${colors.schems.dark.onPrimary};
              font-size: ${fonts.body.body.fontSize};
              font-weight: ${fonts.body.body.fontWeight};
              line-height: ${fonts.body.body.lineHeight};
              margin: 0;
              padding: 0;
            }
            & > p {
              width: 100%;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              color: ${colors.schems.dark.onDisabled};
              font-size: ${fonts.body.label.fontSize};
              font-weight: ${fonts.body.label.fontWeight};
              line-height: ${fonts.body.label.lineHeight};
              margin: 0;
              padding: 0;
            }
          }
        }
      `}</style>
    </>
  )
}
export default React.memo(ColumnCreator)
