import React, { useCallback } from "react"
import { colors, schemes } from "@/utils/theme/style"
import ContentItem from "@/components/table/columns/ContentItem"

function ColumnContent({ creator }: { creator: any }) {
  const getContentElement = useCallback((data: any[] | undefined) => {
    let returnElement: any = ""
    if (data?.length) {
      returnElement = data.map((post: any, index: number) => {
        return <ContentItem post={post} key={index} />
      })
    }
    const blankCount = 6 - (data?.length ?? 0)
    if (blankCount > 0) {
      returnElement = [
        returnElement,
        ...[...Array(blankCount).keys()].map((_, index) => {
          return (
            <li key={index}>
              <div></div>
            </li>
          )
        }),
      ].filter((item) => item !== undefined)
    }

    return returnElement
  }, [])

  return (
    <>
      <ul className={`content`}>
        {getContentElement(
          creator.tiktok_creator_posts
            ? [...creator.tiktok_creator_posts]
                .reverse()
                .filter((post: any, index: number) => !!post.url && index < 6)
            : []
        )}
      </ul>
      <style jsx>{`
        ul.content {
          list-style: none;
          height: 5rem;
          margin: 0;
          padding: 0;
          display: flex;
          justify-content: start;
          align-items: center;
          gap: ${schemes.spacing.sm};
          li {
            height: 5rem;
            > div {
              width: 2.5rem;
              height: 5rem;
              border-radius: ${schemes.radius.xs};
              border: 1px solid ${colors.border.dark.bgLighten};
            }
          }
          img {
            width: 2.5rem;
            max-width: 2.5rem;
            height: 5rem;
            flex-shrink: 0;
            aspect-ratio: 1/2;
            border-radius: ${schemes.radius.xs};
          }
        }
      `}</style>
    </>
  )
}
export default ColumnContent