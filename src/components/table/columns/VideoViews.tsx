import { TableApexBar } from "@/components/charts/TableApexBar"
import React from "react"

function ColumnVideoViews({ creator }: { creator: any }) {
  return (
    <>
      <div className={"videos"}>
        {creator.is_waiting_analysis ? (
          "-"
        ) : (
          <div style={{ width: "120px", height: "80px" }}>
            <TableApexBar
              data={JSON.parse(
                JSON.stringify(creator.tiktok_creator_posts)
              ).map((post: any) => post.play_count)}
            />
          </div>
        )}
      </div>
    </>
  )
}

export default React.memo(ColumnVideoViews)
