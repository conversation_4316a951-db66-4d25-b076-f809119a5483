import Link from "next/link"
import Image from "next/image"
import React, { SyntheticEvent, useState } from "react"
import { getAssetPath } from "@/utils/util"
import { colors, schemes } from "@/utils/theme/style"
import TiktokPlayer from "@/components/tiktok/player"

const ContentItem = ({ post }: { post: any }) => {
  const [isHover, setIsHover] = useState(false)

  return (
    <>
      <li
        onClick={(e: any) => e.stopPropagation()}
        onMouseEnter={() => setIsHover(true)}
        onMouseLeave={() => setIsHover(false)}
      >
        <Link href={post.url} target="_blank">
          {post.cover ? (
            <Image
              src={post.cover}
              alt={post.cover}
              className={"cover"}
              width={40}
              height={80}
              priority
              onError={(e: SyntheticEvent<HTMLImageElement, Event>) => {
                e.currentTarget.src = getAssetPath("contents_error_image.svg")
              }}
            />
          ) : (
            <Image
              src={getAssetPath("contents_error_image.svg")}
              alt={getAssetPath("contents_error_image.svg")}
              className={"cover"}
              width={40}
              height={80}
              priority
            />
          )}
        </Link>
        {isHover && (
          <div
            className={"hover-video"}
            style={{
              left: 0,
              top: 0,
            }}
          >
            <TiktokPlayer url={post.url} />
          </div>
        )}
      </li>
      <style jsx>{`
        li {
          position: relative;
          height: 5rem;
          > div {
            width: 2.5rem;
            height: 5rem;
            border-radius: ${schemes.radius.xs};
            border: 1px solid ${colors.border.dark.bgLighten};
          }
          .hover-video {
            position: absolute;
            display: block;
            z-index: 9;
            border: 0;
          }
          img {
            width: 2.5rem;
            max-width: 2.5rem;
            height: 5rem;
            flex-shrink: 0;
            aspect-ratio: 1/2;
            border-radius: ${schemes.radius.xs};
          }
        }
      `}</style>
    </>
  )
}
export default ContentItem
