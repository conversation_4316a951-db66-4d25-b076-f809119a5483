import Image from "next/image"
import { colors, schemes, bodies } from "@/utils/theme/style"

function ProductItem({
  product,
  isVideoCount = true,
}: {
  product: any
  isVideoCount?: boolean
}) {
  return (
    <>
      <div className="product-item">
        <div className="image-area">
          {product && product.cover_url ? (
            <>
              <Image
                src={product.cover_url}
                alt={product.product_name || "Product image"}
                style={{ borderRadius: `${schemes.radius.sm}` }}
                width="64"
                height="64"
              />
              {isVideoCount && (
                <div className={"video-count"}>
                  {product.total_video_cnt > 99
                    ? "99+"
                    : product.total_video_cnt}{" "}
                  video
                </div>
              )}
            </>
          ) : (
            <div className={"blank"}>-</div>
          )}
        </div>
        <div className={"info-area"}>
          {product && (
            <>
              <p>Beauty & PersonalCare</p>
              <h5>{product?.product_name}</h5>
              <div>
                <span>GMV</span>
                <span>
                  $
                  {Math.round(
                    product?.total_video_sale_gmv_amt
                  ).toLocaleString()}
                </span>
              </div>
            </>
          )}
        </div>
      </div>
      <style jsx>{`
        .product-item {
          display: flex;
          justify-content: start;
          align-items: center;
          gap: ${schemes.spacing.md};
          .image-area {
            position: relative;
            width: 4rem;
            height: 4rem;
            .blank {
              line-height: 4rem;
              text-align: center;
              border-radius: ${schemes.radius.sm};
              border: 1px solid ${colors.border.dark.default};
            }
            .video-count {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 21px;
              text-align: center;
              background: rgba(0, 0, 0, 0.75);
              border-end-start-radius: ${schemes.radius.sm};
              border-end-end-radius: ${schemes.radius.sm};

              font-size: ${bodies.label.fontSize};
              font-weight: ${bodies.label.fontWeight};
              line-height: ${bodies.label.lineHeight};
            }
          }
          .info-area {
            display: flex;
            flex-direction: column;
            align-items: start;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            > p {
              margin: 0;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              color: ${colors.schems.dark.onDisabled};
              font-size: ${bodies.label.fontSize};
              font-weight: ${bodies.label.fontWeight};
              line-height: ${bodies.label.lineHeight};
            }
            > h5 {
              margin: 0;
              width: 100%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-size: ${bodies.bodyBold.fontSize};
              font-weight: ${bodies.bodyBold.fontWeight};
              line-height: ${bodies.bodyBold.lineHeight};
            }
            > div {
              display: flex;
              justify-content: start;
              align-items: center;
              gap: ${schemes.spacing.sm};
              > span {
                font-size: ${bodies.label.fontSize};
                font-weight: ${bodies.label.fontWeight};
                line-height: ${bodies.label.lineHeight};
                &:first-of-type {
                  color: ${colors.schems.dark.onDisabled};
                }
              }
            }
          }
        }
      `}</style>
    </>
  )
}
export default ProductItem
