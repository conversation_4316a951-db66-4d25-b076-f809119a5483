import Button from "@/components/elements/Button"
import { getAssetPath } from "@/utils/util"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { useEffect, useState } from "react"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import SelectBox from "@/components/elements/SelectBox"
import { createQueryString } from "@/common/utils/common"

export default function Pagination({
  currentPage,
  totalCount,
  perPage,
  setPerPage,
}: {
  currentPage: number
  totalCount: number
  perPage: number
  setPerPage: (perPage: number) => void
}) {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [page, setPage] = useState<number>(currentPage)
  const totalPages = Math.ceil(totalCount / perPage)

  useEffect(() => {
    setPage(currentPage)
  }, [currentPage])

  const handleChangePerPage = (perPage: number) => {
    const page = Math.ceil(totalCount / perPage)
    if (currentPage > page) {
      router.push(
        pathname +
          "?" +
          createQueryString("page", page.toString(), searchParams.toString())
      )
    }
    setPerPage(perPage)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    router.push(
      pathname +
        "?" +
        createQueryString("page", page.toString(), searchParams.toString())
    )
  }

  return (
    <>
      <div className={"pagination"}>
        <div>
          <span>Total</span> <span>{totalCount}</span>
        </div>
        <div>
          <div>
            <span>Page</span>
          </div>
          <div>
            <form onSubmit={handleSubmit}>
              <input
                type="number"
                value={page}
                min={1}
                max={totalPages}
                onChange={(e) => setPage(Number(e.target.value))}
                aria-label={"page"}
              />
            </form>
          </div>
          <div>
            <span>/</span>
            <span>{totalPages}</span>
          </div>
          <div className={"buttons"}>
            <Button
              style={"text"}
              size={"xs"}
              icon={{
                path: getAssetPath("line_arrow_left.svg"),
                color: colors.schems.dark.onBase,
              }}
              disabled={currentPage === 1}
              onClick={() =>
                router.push(
                  pathname +
                    "?" +
                    createQueryString(
                      "page",
                      (currentPage - 1 < 0 ? 1 : currentPage - 1).toString(),
                      searchParams.toString()
                    )
                )
              }
              areaLabel={"prev page"}
            ></Button>
            <Button
              style={"text"}
              size={"xs"}
              icon={{
                path: getAssetPath("line_arrow_right.svg"),
                color: colors.schems.dark.onBase,
              }}
              disabled={currentPage === totalPages}
              onClick={() =>
                router.push(
                  pathname +
                    "?" +
                    createQueryString(
                      "page",
                      (currentPage + 1 > totalCount
                        ? totalCount
                        : currentPage + 1
                      ).toString(),
                      searchParams.toString()
                    )
                )
              }
              areaLabel={"next page"}
            ></Button>
          </div>
          <SelectBox
            data={[25, 50, 100]}
            display={"up"}
            action={handleChangePerPage}
            defaultValue={perPage}
          />
          <div>
            <span>Show per page</span>
          </div>
        </div>
      </div>
      <style jsx>{`
        .pagination {
          width: 100%;
          height: 3.5rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          & > div:first-of-type {
            width: 19rem;
            height: 3.5rem;
            flex-shrink: 0;
            text-align: right;
            padding-right: ${schemes.spacing["2xl"]};
            border-right: ${schemes.borderWidth.sm} solid
              ${colors.border.dark.default};
            & > span {
              font-size: ${fonts.body.bodyBold.fontSize};
              font-weight: ${fonts.body.bodyBold.fontWeight};
              line-height: 3.5rem;
              &:first-of-type {
                color: ${colors.schems.dark.onDisabled};
              }
            }
          }
          & > div:last-of-type {
            display: flex;
            justify-content: right;
            align-items: center;
            gap: ${schemes.spacing.md};
            height: 3.5rem;
            padding-right: ${schemes.spacing["3xl"]};
            & > * {
              display: flex;
              justify-content: center;
              align-items: center;
              gap: ${schemes.spacing.sm};
              height: 2rem;
              &.buttons {
                gap: 0;
              }
            }
            & span {
              color: ${colors.schems.dark.onDisabled};
              font-size: ${fonts.body.large.fontSize};
              font-weight: ${fonts.body.large.fontWeight};
              line-height: ${fonts.body.large.lineHeight};
            }
            & input {
              width: 2rem;
              height: 2rem;
              background: ${colors.schems.dark.secondary};
              border-radius: ${schemes.radius.sm};
              border: ${schemes.borderWidth.sm} solid
                ${colors.border.dark.default};
              text-align: center;
              color: ${colors.schems.dark.onBase};

              /* Chrome, Safari, Edge, Opera */
              &::-webkit-outer-spin-button,
              &::-webkit-inner-spin-button {
                -webkit-appearance: none;
                margin: 0;
              }

              /* Firefox  */
              &[type="number"] {
                -moz-appearance: textfield;
              }
            }
            & select {
              height: 2rem;
              padding: 0 ${schemes.spacing.md} 0 ${schemes.spacing.xl};
              background: ${colors.schems.dark.secondary};
              border-radius: ${schemes.radius.sm};
              border: ${schemes.borderWidth.sm} solid
                ${colors.border.dark.default};
              text-align: center;
              color: ${colors.schems.dark.onBase};
            }
          }
        }
      `}</style>
    </>
  )
}
