import LabelTag from "@/components/elements/LabelTag"
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react"
import { colors, fonts, schemes } from "@/utils/theme/style"
import useWorkspace from "@/store/useWorkspace"
import { useWindowDimensions } from "@/utils/util"
import Checkbox from "@/components/elements/Checkbox2"
import { ColorType } from "@/common/types/colorType"
import { editBookmarkTags } from "@/service/workspace"
import { useAuth } from "@/common/hooks/useAuthProvider"
import { notifyError } from "@/components/modal/Toasts"

interface SavedTagsProps {
  uniqueId: string
  creatorTags: {
    id: number
    color: string
    is_default: boolean
    name: string
  }[]
}

export default function SavedTags({ uniqueId, creatorTags }: SavedTagsProps) {
  const auth = useAuth()
  const isInitialMount = useRef(true)
  const { currentWorkspace, tags, setTags } = useWorkspace()
  const workspaceId = currentWorkspace?.id
  const [isOpen, setIsOpen] = useState(false)
  const openRef = useRef<HTMLDivElement>(null)
  const [currentCreatorTags, setCurrentCreatorTags] = useState(
    new Set(creatorTags.map((creatorTag) => creatorTag.id))
  )
  const { height } = useWindowDimensions()

  const filterTags = useMemo(
    () => tags.filter((tag) => !tag.is_default),
    [tags]
  )

  useEffect(() => {
    setCurrentCreatorTags(new Set(creatorTags.map((tag) => tag.id)))
  }, [creatorTags])

  const openPosition = useMemo(() => {
    if (isOpen) {
      const rectTop = openRef.current?.getBoundingClientRect().top ?? 0
      const heightSubRect = height - rectTop
      const tagHeight =
        (filterTags.length === 0
          ? 156
          : filterTags.length > 10
            ? 416
            : filterTags.length * 40 + 16) + 112

      if (height > tagHeight && heightSubRect < tagHeight) {
        return "up"
      }
    }
    return "down"
  }, [filterTags.length, height, isOpen])

  useEffect(() => {
    const handleClickOutside = async (event: any) => {
      if (openRef.current && !openRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }

    // 바깥 클릭 이벤트 리스너 추가
    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false
      return
    }

    if (!isOpen) {
      const orgTagIds = creatorTags.map((tag) => tag.id).sort()
      const changeTagIds = filterTags
        .filter((tag) => currentCreatorTags.has(tag.id as number))
        .map((tag) => tag.id as number)
        .sort()
      if (JSON.stringify(orgTagIds) !== JSON.stringify(changeTagIds)) {
        editBookmarkTags({
          accessToken: auth.session?.access_token as string,
          workspaceId: workspaceId as number,
          uniqueId: uniqueId,
          tagIds: changeTagIds,
        })
          .then((response) => {
            setTags(response)
            setCurrentCreatorTags(new Set(changeTagIds))
          })
          .catch((e) => {
            notifyError(e.message)
          })
      }
    }
  }, [isOpen])

  const handleTag = useCallback(
    (tagId: number, isChecked: boolean) => {
      setCurrentCreatorTags((prevTags) => {
        const newTags = new Set(prevTags)
        if (isChecked) {
          newTags.add(tagId)
        } else {
          newTags.delete(tagId)
        }

        return newTags
      })
    },
    [currentCreatorTags]
  )

  return (
    <>
      <div className={`saved-tags ${isOpen ? "open" : ""}`} ref={openRef}>
        <ul onClick={() => setIsOpen(!isOpen)}>
          {filterTags
            .filter((tag) => currentCreatorTags.has(tag.id as number))
            .map((tag: any) => {
              return (
                <li key={tag.id}>
                  <LabelTag type={"folder"} color={tag.color}>
                    {tag.name}
                  </LabelTag>
                </li>
              )
            })}
        </ul>
        {filterTags.length > 0 && (
          <div className={`options ${openPosition}`}>
            <ul>
              {filterTags.map((tag) => (
                <li key={tag.id}>
                  <div>
                    <Checkbox
                      isChecked={currentCreatorTags.has(tag.id as number)}
                      color={tag.color as ColorType}
                      onChange={(e) => {
                        handleTag(tag.id as number, e.target.checked)
                      }}
                    >
                      <div className={"tag-name"}>
                        <div className={"name"}>{tag.name}</div>
                        <span className={"count"}>
                          ({tag.count.toLocaleString()})
                        </span>
                      </div>
                    </Checkbox>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      <style jsx>{`
        .saved-tags {
          position: relative;
          width: 100%;
          height: 100%;
          > ul {
            width: 100%;
            height: 100%;
            list-style: none;
            margin: 0;
            padding: ${schemes.spacing.md} ${schemes.spacing["2xl"]}
              ${schemes.spacing.md} ${schemes.spacing["3xl"]};
            display: flex;
            align-items: center;
            gap: ${schemes.spacing.md};
            cursor: pointer;

            white-space: nowrap;
            overflow-x: auto;
            -ms-overflow-style: none;
            &::-webkit-scrollbar {
              display: none;
            }
          }
          > div.options {
            display: none;
          }
          &.open {
            > div.options {
              display: block;
              position: absolute;
              left: 0;
              right: 0;
              z-index: 9;
              max-height: 26rem;
              padding: ${schemes.spacing.sm} 0;
              border: 1px solid ${colors.border.dark.bgLighten};
              border-radius: ${schemes.radius.sm};
              background: ${colors.bg.dark.surfaceBright};
              overflow-y: auto;
              white-space: nowrap;

              box-shadow:
                0 1px 3px 1px rgba(0, 0, 0, 0.15),
                0 1px 2px 0 rgba(0, 0, 0, 0.3);
              ul {
                list-style: none;
                padding: ${schemes.spacing.md} 0;
                margin: 0;
                > li {
                  width: 100%;
                  height: 2.5rem;
                  padding: 0 ${schemes.spacing.xl};
                  display: flex;
                  flex-direction: column;
                  align-items: start;
                  justify-content: center;
                  & > div {
                    position: relative;
                    width: 100%;
                    overflow: hidden;
                    white-space: nowrap;
                    & .tag-name {
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      flex-wrap: nowrap;
                      gap: ${schemes.spacing.sm};
                      width: 100%;
                      & .name {
                        flex-grow: 1;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                      }
                      & .count {
                        flex-shrink: 0;
                      }
                    }
                  }
                }
              }
              &.up {
                bottom: 97px;
              }
              &.down {
                top: 97px;
              }
              > div {
                display: flex;
                gap: ${schemes.spacing.md};
                flex-direction: column;
                justify-content: center;
                align-items: center;
                width: 15rem;
                padding: 1.84rem ${schemes.spacing["2xl"]};
                > div {
                  text-align: center;
                  width: 100%;
                  > h4 {
                    margin: 0;
                    font-size: ${fonts.body.large.fontSize};
                    font-weight: ${fonts.body.large.fontWeight};
                  }
                  > p {
                    margin: 0;
                    font-size: ${fonts.body.body.fontSize};
                    font-weight: ${fonts.body.body.fontWeight};
                    color: ${colors.schems.dark.onDisabled};
                  }
                }
                > span {
                  padding: 0 ${schemes.spacing.sm};
                  font-size: ${fonts.body.body.fontSize};
                  font-weight: ${fonts.body.body.fontWeight};
                  line-height: ${fonts.body.body.lineHeight};
                }
              }
            }
          }
        }
      `}</style>
    </>
  )
}
