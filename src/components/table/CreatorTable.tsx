import React, { useCallback, useEffect, useMemo, useState } from "react"
import { Property } from "csstype"
import { colors, fonts, schemes } from "@/utils/theme/style"
import {
  getAssetPath,
  getNumberToDollar,
  randNumberFromStr,
} from "@/utils/util"
import LabelTag from "@/components/elements/LabelTag"
import { ColorType } from "@/common/types/colorType"
import useSearchCreator from "@/store/useSearchCreator"
import Icon from "@/components/elements/Icon"
import SaveButtons from "@/components/table/SaveButtons"
import Checkbox from "@/components/elements/Checkbox2"
import SavedTags from "@/components/table/SavedTags"
import ColumnCreator from "@/components/table/columns/Creator"
import ColumnContent from "@/components/table/columns/Content"
import ColumnVideoViews from "@/components/table/columns/VideoViews"
import ProductItem from "@/components/table/columns/ProductItem"
import Products from "@/components/table/columns/Products"
import NotFound from "@/components/common/NotFound"
import Loading from "@/components/common/Loading"

const CreatorTable = ({
  type,
  columns,
  creators,
  actionSaveBookmark,
  actionRemoveBookmark,
  setIsTagAddModal,
  loading,
  setSelectCreator,
  isDetailModalOpen,
  setIsDetailModalOpen,
}: {
  type: string
  columns: {
    name: string
    data: string
    subData?: string
    sort: boolean
    align: string
    label?: string
    style?: "dollar" | "percent" | undefined
    maxWidth?: string
  }[]
  creators: any
  actionSaveBookmark?: (uniqueId: string, tagIds: number[]) => Promise<void>
  actionRemoveBookmark?: (uniqueId: string) => Promise<void>
  setIsTagAddModal?: (isTagAddModal: boolean) => void
  loading: boolean
  setSelectCreator: (creator: any) => void
  isDetailModalOpen: boolean
  setIsDetailModalOpen: (val: boolean) => void
}) => {
  const uniqueIds = useSearchCreator((state) => state.uniqueIds)
  const setUniqueId = useSearchCreator((state) => state.setUniqueId)
  const setUniqueIds = useSearchCreator((state) => state.setUniqueIds)
  const sortKey = useSearchCreator((state) =>
    type === "explore" ? state.sortKey : state.savedSortKey
  )
  const sortDirection = useSearchCreator((state) =>
    type === "explore" ? state.sortDirection : state.savedSortDirection
  )
  const changePageSort = useSearchCreator((state) =>
    type === "explore" ? state.changSort : state.changSavedSort
  )

  const colorsArr: ColorType[] = [
    "red",
    "orange",
    "yellow",
    "green",
    "cyan",
    "blue",
    "indigo",
    "purple",
    "pink",
  ]
  const [checkAll, setCheckAll] = useState(false)

  const selectUniqueIds = useMemo(() => new Set(uniqueIds), [uniqueIds])

  useEffect(() => {
    if (uniqueIds.length === 0) {
      setCheckAll(false)
    }
  }, [uniqueIds])

  const checkboxAction = useCallback((uniqueId: string) => {
    setUniqueId(uniqueId)
    setCheckAll(selectUniqueIds.size === creators.length)
  }, [])

  const parseDataMultilineLayout = useCallback(
    ({
      value,
      upDown = "",
      label = "",
    }: {
      value: string
      upDown?: string
      label?: string
    }) => {
      return (
        <div className={`labels ${label === "" ? "bottom" : ""}`}>
          <label>{label}</label>
          <span>{value}</span>
          <small
            className={
              upDown !== "-" && upDown.startsWith("-")
                ? "blue"
                : upDown !== "-" && Number(upDown) > 0
                  ? "red"
                  : "gray"
            }
          >
            {upDown && (
              <>
                {upDown !== "-" && Number(upDown) > 0 ? `+${upDown}` : upDown}
                {upDown !== "-" ? "%" : ""}
              </>
            )}
          </small>
        </div>
      )
    },
    []
  )

  const changeSort = useCallback(
    (sortable: boolean, changeSortKey: string) => {
      if (sortable) {
        if (changeSortKey === sortKey && sortDirection === "asc") {
          changePageSort("", "")
        } else if (changeSortKey === sortKey) {
          changePageSort(changeSortKey, "asc")
        } else {
          changePageSort(changeSortKey, "desc")
        }
      }
    },
    [sortDirection, sortKey]
  )

  const getKeyByValue = useCallback(
    (
      creator: any,
      value: {
        name: string
        data: string
        subData?: string
        sort: boolean
        align: string
        label?: string
        style?: "dollar" | "percent" | undefined
        maxWidth?: string
      }
    ) => {
      switch (value.name) {
        case "Creator":
          return (
            <div
              onClick={() => {
                setSelectCreator(creator)
                setIsDetailModalOpen(true)
              }}
              style={{ cursor: "pointer" }}
            >
              <ColumnCreator creator={creator} />
            </div>
          )
        case "Top Sales Item":
          return (
            <div style={{ maxWidth: value.maxWidth ?? "auto" }}>
              <ProductItem
                product={
                  creator.tiktok_creator_products
                    ? creator.tiktok_creator_products[0]
                    : null
                }
              />
            </div>
          )
        case "More Item":
          return (
            <div className={"products"}>
              {creator.tiktok_creator_products && (
                <Products products={creator.tiktok_creator_products} />
              )}
            </div>
          )
        case "Content":
          return (
            <div className={"contents"}>
              <ColumnContent creator={creator} />
            </div>
          )
        case "GMV":
        case "GPM":
        case "Video GPM":
        case "Live GMV":
        case "Items Sold":
          return !creator[value.data]
            ? "-"
            : parseDataMultilineLayout({
                value:
                  value.style === "dollar"
                    ? getNumberToDollar(creator[value.data])
                    : `${creator[value.data]?.toLocaleString()}${value.style === "percent" ? "%" : ""}`,
                upDown: (value.subData && creator[value.subData]) ?? "",
                label: value.label ?? "",
              })
        case "Video Views":
          return <ColumnVideoViews creator={creator} />
        case "Race":
          return (
            <>
              <LabelTag color={"gray"}>{creator[value.data]}</LabelTag>
            </>
          )
        case "Categories":
        case "Skin Concerns":
        case "Skin Types":
        case "Gender":
        case "Body Type":
        case "Contents Tag":
        case "Brand Mention":
          return (
            <div
              className={"tags"}
              style={{ maxWidth: value.maxWidth ?? "auto" }}
            >
              {typeof creator[value.data] === "string" ? (
                <LabelTag
                  color={
                    creator[value.data] === "Male"
                      ? ("blue" as ColorType)
                      : creator[value.data] === "Female"
                        ? ("red" as ColorType)
                        : (colorsArr[
                            randNumberFromStr(creator[value.data]) %
                              colorsArr.length
                          ] as ColorType)
                  }
                >
                  {creator[value.data]}
                </LabelTag>
              ) : (
                creator[value.data]?.map((val: string, index: number) => {
                  if (val) {
                    return (
                      <LabelTag
                        key={index}
                        color={
                          colorsArr[
                            randNumberFromStr(val) % colorsArr.length
                          ] as ColorType
                        }
                      >
                        {val}
                      </LabelTag>
                    )
                  }
                })
              )}
            </div>
          )
        case "Content Categories": {
          const valueKey = creator[value.data] as string
          const raw = creator.content_type_coverage?.[valueKey]
          const percentage =
            typeof raw === "number" ? `${raw}%` : (raw?.percentage ?? "-")
          return (
            <>
              <LabelTag
                lightness={"darken"}
                type={"status"}
                color={
                  colorsArr[
                    creator[value.data]?.length % colorsArr.length
                  ] as ColorType
                }
              >
                {creator[value.data]} ({percentage})
              </LabelTag>
            </>
          )
        }
        case "Custom tag":
          return (
            <div className={`custom-tag`} onClick={(e) => e.stopPropagation()}>
              <SavedTags
                uniqueId={creator.unique_id}
                creatorTags={creator[value.data]}
              />
            </div>
          )
        case "":
          return <></>
        default:
          return !creator[value.data]
            ? "-"
            : value.style === "dollar"
              ? getNumberToDollar(creator[value.data] || 0)
              : `${typeof creator[value.data] === "number" ? creator[value.data]?.toLocaleString() || 0 : creator[value.data] || 0}${value.style === "percent" ? "%" : ""}`
      }
    },
    []
  )

  const actionCheckAll = (checked: boolean) => {
    if (checked) {
      setUniqueIds(
        new Set(
          creators
            .filter((creator: any) => !creator.is_waiting_analysis)
            .map((creator: any) => creator.unique_id)
        )
      )
    } else {
      setUniqueIds(new Set())
    }
    setCheckAll(checked)
  }

  return (
    <>
      <div className="creator-table">
        <table>
          <thead>
            <tr>
              <th className={"checkbox"}>
                <Checkbox
                  color={checkAll || selectUniqueIds.size > 0 ? "blue" : "gray"}
                  onChange={(e) => {
                    actionCheckAll(e.target.checked)
                  }}
                  isChecked={checkAll}
                  indeterminate={selectUniqueIds.size > 0}
                />
              </th>
              {columns.map((value, index) => (
                <th
                  key={value.name}
                  data-sort={value.sort}
                  data-column={value.data}
                  style={{
                    textAlign: value.align as Property.TextAlign | undefined,
                  }}
                  className={`${index === 0 ? `profile` : ""} ${value.sort ? "sortable" : ""} ${value.data === sortKey ? `selected ${sortDirection}` : ""}`}
                  onClick={() => {
                    changeSort(value.sort, value.data)
                  }}
                >
                  {value.name}
                </th>
              ))}
              <th className={"save-button"}></th>
            </tr>
          </thead>
          <tbody>
            {creators.length > 0 &&
              !loading &&
              creators.map((creator: any) => (
                <tr key={creator.unique_id}>
                  <td
                    className={"checkbox"}
                    onClick={(e) => e.stopPropagation()}
                  >
                    {creator.is_waiting_analysis ? (
                      <Icon
                        path={getAssetPath("loading.svg")}
                        color={colors.schems.dark.primary}
                        size={"1.25rem"}
                        isSpin={true}
                      />
                    ) : (
                      <Checkbox
                        color={
                          selectUniqueIds?.has(creator.unique_id)
                            ? "blue"
                            : "gray"
                        }
                        onChange={() => {
                          checkboxAction(creator.unique_id)
                        }}
                        isChecked={
                          selectUniqueIds?.has(creator.unique_id) || false
                        }
                      />
                    )}
                  </td>
                  {columns.map(
                    (
                      value: {
                        name: string
                        data: string
                        sort: boolean
                        align: string
                      },
                      index: number
                    ) => (
                      <td
                        key={index}
                        style={{
                          textAlign: `${value.align}` as
                            | Property.TextAlign
                            | undefined,
                        }}
                        className={`${index === 0 ? `profile ` : ""}`}
                      >
                        {creator && getKeyByValue(creator, value)}
                      </td>
                    )
                  )}
                  <td className={"save-button"}>
                    {!creator.is_waiting_analysis && (
                      <SaveButtons
                        creator={creator}
                        actionSaveBookmark={actionSaveBookmark}
                        actionRemoveBookmark={actionRemoveBookmark}
                        setIsTagAddModal={setIsTagAddModal}
                      />
                    )}
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
        {!creators.length && !loading && (
          <div className={"not-found"}>
            <NotFound />
          </div>
        )}
        {loading && (
          <div className={"loading"}>
            <Loading />
          </div>
        )}
      </div>
      <style jsx>{`
        .creator-table {
          width: 100%;
          height: 100%;
          white-space: nowrap;
          overflow: scroll;
          -ms-overflow-style: none;
          &::-webkit-scrollbar {
            display: none;
          }
          & > table {
            border-collapse: separate;
            border-spacing: 0;
            min-width: 100%;
            table-layout: fixed;
            background: ${colors.schems.dark.base};
            td,
            th {
              padding: 0 1rem 0 1.5rem;
              border-bottom: ${schemes.borderWidth.sm} solid
                ${colors.border.dark.default};
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              &.checkbox {
                min-width: 4rem;
                max-width: 4rem;
                position: sticky;
                left: 0;
                background: ${colors.schems.dark.base};
                z-index: 10;
              }

              &.profile {
                min-width: 15rem;
                max-width: 15rem;
                position: sticky;
                left: 4rem;
                background: ${colors.schems.dark.base};
                border-right: ${schemes.borderWidth.sm} solid
                  ${colors.border.dark.default};
                z-index: 10;
              }

              &.save-button {
                position: sticky;
                right: 0;
                padding: 0 0 0 2.5rem;
                min-width: 134px;
                overflow: visible;
              }
            }
            & thead {
              z-index: 11;
              position: sticky;
              left: 0;
              right: 0;
              top: 0;
              table-layout: fixed;
              background: black;
              & th {
                height: 2.5rem;
                color: ${colors.schems.dark.onDisabled};
                font-size: ${fonts.body.body.fontSize};
                font-weight: ${fonts.body.body.fontWeight};
                line-height: ${fonts.body.body.lineHeight};
                position: relative;

                &.sortable {
                  cursor: pointer;
                  padding: 0 2rem 0 1.5rem;
                  &.selected {
                    background-color: ${colors.bg.dark.surface};
                  }
                  &.profile::after {
                    right: 8px;
                  }
                  &.asc::after {
                    border-top: none;
                    border-bottom: 5px solid ${colors.schems.dark.disabled};
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    background: none;
                    width: 0;
                    height: 0;
                  }

                  &.desc::after {
                    border-bottom: none;
                    border-top: 5px solid ${colors.schems.dark.disabled};
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    background: none;
                    width: 0;
                    height: 0;
                  }
                  &::after {
                    content: "";
                    position: absolute;
                    right: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 1rem;
                    height: 1rem;
                    background-position: center;
                    background-repeat: no-repeat;
                    background-size: contain;
                    background-image: url(${getAssetPath("unfold_more.svg")});
                    transition: all 0.2s ease;
                  }
                }
              }
            }
            & tbody {
              & tr {
                &:hover {
                  background: ${colors.bg.dark.surface};
                  & td {
                    &.save-button {
                      z-index: 30;
                    }
                    &.checkbox,
                    &.profile {
                      background: ${colors.bg.dark.surface};
                    }
                    & .button {
                      opacity: 1;
                    }
                  }
                }
                & td {
                  height: 6rem;
                  color: ${colors.schems.dark.onPrimary};
                  font-size: ${fonts.body.body.fontSize};
                  font-weight: ${fonts.body.body.fontWeight};
                  line-height: ${fonts.body.body.lineHeight};
                  padding: ${schemes.spacing.md} ${schemes.spacing["2xl"]}
                    ${schemes.spacing.md} ${schemes.spacing["3xl"]};
                  & .button {
                    position: relative;
                    background: ${colors.bg.dark.surface};
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    padding-right: 10px;
                    height: 6rem;
                    display: flex;
                    justify-content: right;
                    align-items: center;
                    gap: ${schemes.spacing.md};
                    &:after {
                      position: absolute;
                      content: "";
                      left: -2.5rem;
                      width: 2.5rem;
                      top: 0;
                      bottom: 0;

                      background: linear-gradient(
                        270deg,
                        ${colors.bg.dark.surface} 0%,
                        rgba(24, 24, 27, 0) 100%
                      );
                    }
                  }

                  &:has(div.contents),
                  &:has(div.products) {
                    overflow: visible;
                  }
                  &:has(div.custom-tag) {
                    padding: 0;
                    overflow: visible;
                  }
                  & > .custom-tag {
                    max-width: 25rem;
                    height: 100%;
                    border-right: ${schemes.borderWidth.sm} solid
                      ${colors.border.dark.default};
                  }
                  & .labels {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    & > * {
                      text-align: right;
                    }
                    & label,
                    & small {
                      color: ${colors.schems.dark.disabled};
                      font-size: ${fonts.body.label.fontSize};
                      font-weight: ${fonts.body.label.fontWeight};
                      line-height: ${fonts.body.label.lineHeight};
                      height: 21px;
                    }
                    & span {
                      font-size: ${fonts.body.body.fontSize};
                      font-weight: ${fonts.body.body.fontWeight};
                      line-height: ${fonts.body.body.lineHeight};
                    }
                    & small {
                      &.red {
                        color: ${colors.sys.dark.red.default};
                      }
                      &.blue {
                        color: ${colors.sys.dark.blue.default};
                      }
                    }
                  }
                  & .tags {
                    display: flex;
                    gap: ${schemes.spacing.md};

                    overflow-x: auto;
                    -ms-overflow-style: none;
                    &::-webkit-scrollbar {
                      display: none;
                    }
                  }
                }
              }
            }
          }
          & > .not-found,
          & > .loading {
            width: 100%;
            height: calc(100vh - (3.5rem * 2 + 2.5rem));
          }
        }
      `}</style>
    </>
  )
}

export default React.memo(CreatorTable)
