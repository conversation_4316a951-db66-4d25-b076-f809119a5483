import { useEffect, useMemo, useRef } from "react"
import { schemes } from "@/utils/theme/style"

const TiktokPlayer = ({
  url,
  width = 180,
  height = 320,
}: {
  url: string
  width?: number
  height?: number
}) => {
  const videoRef = useRef<HTMLIFrameElement>(null)

  // const sendMessageNoMute = () => {
  //   console.log("sendMessageNoMute")
  //   videoRef?.current?.contentWindow?.postMessage(
  //     { type: "unMute", "x-tiktok-player": true },
  //     "*"
  //   )
  // }

  const parseUrl = useMemo(() => {
    const queryObject = {
      controls: "1",
      progress_bar: "0",
      play_button: "0",
      volume_control: "0",
      fullscreen_button: "0",
      timestamp: "0",
      loop: "0",
      autoplay: "1",
      music_info: "0",
      description: "0",
      rel: "0",
      native_context_menu: "0",
      closed_caption: "0",
    }
    const queryString = new URLSearchParams(queryObject).toString()

    const splitUrl = url.split("/")
    const videoId = splitUrl[splitUrl.length - 1]
    return `https://www.tiktok.com/player/v1/${videoId}?${queryString}`
  }, [url])

  // useEffect(() => {
  //   const handleMessageEvent = (event: any) => {
  //     console.log(event)
  //   }
  //
  //   // 바깥 클릭 이벤트 리스너 추가
  //   window.addEventListener("message", handleMessageEvent)
  //   return () => {
  //     // 컴포넌트가 언마운트될 때 이벤트 리스너 제거
  //     window.addEventListener("message", handleMessageEvent)
  //   }
  // }, [url])

  return (
    <>
      <iframe
        width={width}
        height={height}
        src={parseUrl}
        allow="fullscreen"
        title={url}
        ref={videoRef}
        // onLoad={() => sendMessageNoMute()}
      ></iframe>
      <style jsx>{`
        iframe {
          border-radius: ${schemes.radius.sm};
          border: 0;
        }
      `}</style>
    </>
  )
}

export default TiktokPlayer
