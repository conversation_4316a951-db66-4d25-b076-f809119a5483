import { colors } from "@/utils/theme/style"

export default function Icon({
  path,
  color = colors.schems.dark.disabled,
  size = "1.5rem",
  width,
  height,
  isHover = false,
  hoverColor = colors.schems.dark.onDisabled,
  isSpin = false,
}: {
  path: string
  color?: string
  size?: string
  width?: string
  height?: string
  isHover?: boolean
  hoverColor?: string
  isSpin?: boolean
}) {
  return (
    <>
      <i />
      <style jsx>{`
        i {
          display: inline-block;
          width: ${width ? width : size};
          height: ${height ? height : width ? width : size};
          mask: url("${path}") no-repeat center;
          mask-size: cover;
          -webkit-mask: url("${path}") no-repeat center;
          -webkit-mask-size: cover;
          background-color: ${color};
          animation: ${isSpin && "spin 2s infinite linear"};
          ${isHover &&
          `
            &:hover {
              background-color: ${hoverColor};
            }
          `};
        }
        @keyframes spin {
          from {
            -webkit-transform: rotate(0deg);
            -o-transform: rotate(0deg);
            transform: rotate(0deg);
          }
          to {
            -webkit-transform: rotate(360deg);
            -o-transform: rotate(360deg);
            transform: rotate(360deg);
          }
        }
      `}</style>
    </>
  )
}
