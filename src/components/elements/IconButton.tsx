import Icon from "@/components/elements/Icon"
import { colors } from "@/utils/theme/style"
import React from "react"

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  type?: "button" | "submit" | "reset"
  path: string
  color?: string
  disabled?: boolean
  onClick?: () => void
  label?: string
  size?: string
  iconSize?: string
}

export default function IconButton({
  path,
  color = colors.schems.dark.onBase,
  size = "2.5rem",
  iconSize = "1.5rem",
  ...props
}: ButtonProps) {
  return (
    <>
      <button {...props} type={"button"}>
        <Icon
          path={path}
          color={props.disabled ? colors.schems.dark.disabled : color}
          size={iconSize}
        />
      </button>
      <style jsx>{`
        button {
          display: inline-flex;
          justify-content: center;
          align-items: center;
          width: ${size};
          height: ${size};
          background: none;
          border: none;
          border-radius: 100%;
          cursor: pointer;
          &:hover {
            background: ${colors.interactive.dark.hover};
          }
          &:active {
            background: ${colors.interactive.dark.pressed};
          }
          &:disabled {
            background: none;
          }
        }
      `}</style>
    </>
  )
}
