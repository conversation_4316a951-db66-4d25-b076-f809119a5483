import React, { useCallback, useEffect, useState, useRef } from "react"
import PropTypes from "prop-types"
import { colors } from "@/utils/theme/style"

const MultiRangeSlider = ({
  min,
  max,
  step,
  minVal,
  setMinVal,
  maxVal,
  setMaxVal,
}: {
  min: number
  max: number
  step: number
  minVal: number
  setMinVal: (min: number) => void
  maxVal: number
  setMaxVal: (max: number) => void
}) => {
  const range = useRef<HTMLInputElement>(null)

  // Convert to percentage
  const getPercent = useCallback(
    (value: number) => {
      return Math.round((value / max) * 10000) / 100
    },
    [min, max, minVal, maxVal]
  )

  // Set width of the range to decrease from the left side
  useEffect(() => {
    const minPercent = getPercent(minVal)

    if (range.current) {
      range.current.style.left = `${minPercent}%`
    }
  }, [minVal, getPercent])

  // Set width of the range to decrease from the right side
  useEffect(() => {
    const minPercent = getPercent(minVal)
    const maxPercent = getPercent(maxVal)

    if (range.current) {
      range.current.style.width = `${maxPercent - minPercent}%`
    }
  }, [maxVal, getPercent, minVal])

  return (
    <>
      <div className="container">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={minVal}
          onChange={(event) => {
            const value = Math.min(Number(event.target.value), maxVal - step)
            setMinVal(value)
          }}
          className="thumb thumb--left"
          aria-label={"range-left"}
          // @ts-expect-error: Not an error
          style={{ zIndex: minVal > max - 100 && "5" }}
        />
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={maxVal}
          onChange={(event) => {
            const value = Math.max(Number(event.target.value), minVal + step)
            setMaxVal(value)
          }}
          className="thumb thumb--right"
          aria-label={"range-right"}
        />

        <div className="slider">
          <div className="slider__track" />
          <div ref={range} className="slider__range" />
        </div>
      </div>
      <style jsx>{`
        .container {
          position: relative;
          width: 100%;
        }
        .slider {
          position: relative;
          width: 100%;
        }

        .slider__track,
        .slider__range {
          position: absolute;
        }

        .slider__track,
        .slider__range {
          border-radius: 3px;
          height: 0.25rem;
        }

        .slider__track {
          background-color: ${colors.schems.dark.secondary};
          width: 100%;
          z-index: 1;
        }

        .slider__range {
          background-color: ${colors.border.dark.primary};
          z-index: 2;
        }

        /* Removing the default appearance */
        .thumb,
        .thumb::-webkit-slider-thumb {
          -webkit-appearance: none;
        }

        .thumb {
          pointer-events: none;
          position: absolute;
          height: 0;
          width: 100%;
          outline: none;
          margin-left: 0;
          margin-right: 0;
        }

        .thumb--left {
          z-index: 3;
        }

        .thumb--right {
          z-index: 4;
        }

        /* For Chrome browsers */
        .thumb::-webkit-slider-thumb {
          background-color: ${colors.schems.dark.onPrimary};
          border: 2px solid ${colors.border.dark.primary};
          border-radius: 50%;
          box-shadow: none;
          cursor: pointer;
          height: 1rem;
          width: 1rem;
          pointer-events: all;
        }

        /* For Firefox browsers */
        .thumb::-moz-range-thumb {
          background-color: ${colors.schems.dark.onPrimary};
          border: 2px solid ${colors.border.dark.primary};
          border-radius: 50%;
          box-shadow: none;
          cursor: pointer;
          height: 1rem;
          width: 1rem;
          pointer-events: all;
        }
      `}</style>
    </>
  )
}

MultiRangeSlider.propTypes = {
  min: PropTypes.number.isRequired,
  max: PropTypes.number.isRequired,
}

export default MultiRangeSlider
