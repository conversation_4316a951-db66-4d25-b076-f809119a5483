import { getAssetPath } from "@/utils/util"
import Icon from "@/components/elements/Icon"
import { colors, schemes } from "@/utils/theme/style"
import React from "react"

type InputProps = React.InputHTMLAttributes<HTMLInputElement>
export default function Toggle({ ...props }: InputProps) {
  return (
    <>
      <div className={"toggle"}>
        <input {...props} type={"checkbox"} />
        <label
          htmlFor={props.id}
          className={`toggle-area ${props.disabled ? "disabled" : ""}`}
        >
          <div className={"toggle-circle"}>
            <div>
              <Icon
                path={getAssetPath("check.svg")}
                size={"1rem"}
                color={
                  props.disabled
                    ? colors.schems.dark.disabled
                    : colors.schems.dark.primary
                }
              />
            </div>
          </div>
        </label>
      </div>
      <style jsx>{`
        .toggle {
          display: inline-block;
          > input {
            display: none;
            &:checked + .toggle-area {
              border: none;
              background: ${colors.schems.dark.primary};
              &.disabled {
                background: ${colors.schems.dark.disabled};
              }
              &:not(.disabled):active {
                > .toggle-circle {
                  width: 1.75rem;
                  height: 1.75rem;
                }
              }
              > .toggle-circle {
                width: 1.5rem;
                height: 1.5rem;
                left: 36px;
                > div {
                  display: block;
                }
              }
            }
          }
          > .toggle-area {
            position: relative;
            display: block;
            width: 3.25rem;
            height: 2rem;
            border-radius: ${schemes.radius["2xl"]};
            border: ${schemes.borderWidth.md} solid ${colors.border.dark.icon};
            transition: all 0.1s linear;
            &:not(.disabled):active {
              > .toggle-circle {
                width: 1.75rem;
                height: 1.75rem;
              }
            }
            > .toggle-circle {
              width: 1rem;
              height: 1rem;
              background: ${colors.schems.dark.onBase};
              border-radius: ${schemes.radius.rounded};
              display: flex;
              align-items: center;
              justify-content: center;

              position: absolute;
              top: 50%;
              transform: translate(-50%, -50%);
              left: 14px;

              transition: all 0.1s ease-in-out;

              > div {
                display: none;
                font-size: 0;
              }
            }

            &.disabled {
              border: none;
              background: ${colors.schems.dark.disabled};
              > .toggle-circle {
                background: ${colors.schems.dark.onDisabled};
              }
            }
          }
        }
      `}</style>
    </>
  )
}
