import { colors, fonts, schemes } from "@/utils/theme/style"
import { ColorType } from "@/common/types/colorType"

export default function Label({
  children,
  color = "gray",
  isDark = false,
}: {
  children?: React.ReactNode
  color?: ColorType
  type?: "status" | "folder" | "default"
  isDark?: boolean
}) {
  return (
    <>
      <label>
        <span className="bar"></span>
        <span>{children}</span>
      </label>
      <style jsx>{`
        label {
          display: inline-flex;
          justify-content: center;
          align-content: stretch;
          gap: ${schemes.spacing.sm};
          color: ${colors.sys.light[color].lighten};
          font-size: ${fonts.body.labelBold.fontSize};
          font-weight: 500;
          line-height: 100%;
          & .bar {
            width: 0.25rem;
            border-radius: ${schemes.radius.rounded};
            background-color: ${colors.sys.light[color].lighten};
          }
        }
      `}</style>
    </>
  )
}
