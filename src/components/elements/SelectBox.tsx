import { useEffect, useRef, useState } from "react"
import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"
import { colors, fonts, schemes } from "@/utils/theme/style"

export default function SelectBox({
  data,
  defaultValue,
  width = "fit-content",
  isFull = false,
  display = "down",
  action,
  label,
  position = "absolute",
  disabled = false,
}: {
  data: any[]
  defaultValue?: any
  width?: string
  isFull?: boolean
  display?: "up" | "down"
  action?: (val: any) => void
  label?: string
  position?: "absolute" | "relative"
  disabled?: boolean
}) {
  const openRef = useRef<HTMLDivElement>(null)
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [selectValue, setSelectValue] = useState<any>(defaultValue ?? data[0])

  useEffect(() => {
    const handleClickOutside = async (event: any) => {
      if (openRef.current && !openRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [openRef])

  return (
    <>
      <div
        className={`select-box ${isFull ? "full" : ""} ${isOpen ? "open" : ""} ${label ? "label" : ""} ${disabled ? "disabled" : ""}`}
        ref={openRef}
      >
        {label && <label>{label}</label>}
        <button onClick={() => !disabled && setIsOpen(!isOpen)}>
          <span className={`${selectValue !== "" ? "selected" : ""}`}>
            {selectValue}
          </span>
          <Icon
            path={
              isOpen
                ? getAssetPath("line_arrow_up.svg")
                : getAssetPath("line_arrow_down.svg")
            }
            color={
              disabled
                ? colors.schems.dark.onDisabled
                : colors.schems.dark.onPrimary
            }
          />
        </button>
        <div className={`options ${display}`}>
          {data.map((item: any, index: number) => {
            return (
              <div
                key={index}
                onClick={() => {
                  setSelectValue(item)
                  setIsOpen(!isOpen)
                  if (action) action(item)
                }}
              >
                <span>{item}</span>
              </div>
            )
          })}
        </div>
      </div>
      <style jsx>{`
        .select-box {
          position: relative;
          width: fit-content;
          & > label {
            display: block;
            margin-bottom: ${schemes.spacing.sm};
            font-size: ${fonts.body.label.fontSize};
            font-weight: ${fonts.body.label.fontWeight};
            line-height: ${fonts.body.label.lineHeight};
          }
          & > button {
            border: 1px solid ${colors.border.dark.default};
            border-radius: ${schemes.radius.sm};
            background: ${colors.interactive.dark.hover};
            padding: 0 ${schemes.spacing.sm};

            width: ${width};
            height: 2rem;
            display: inline-flex;
            align-items: center;
            justify-content: space-between;
            & > span {
              padding: 0 ${schemes.spacing.sm};
              color: ${colors.schems.dark.onDisabled};
              font-size: ${fonts.body.body.fontSize};
              font-weight: ${fonts.body.body.fontWeight};
              line-height: ${fonts.body.body.lineHeight};
            }
            &:hover {
              background: ${colors.schems.dark.secondary};
            }
          }
          > .options {
            display: none;
          }
          &.full {
            width: 100%;
            > button {
              width: 100%;
              height: 2.5rem;
              padding: 0 ${schemes.spacing["2xl"]};
            }
          }
          &.open {
            > button {
              border-color: ${colors.border.dark.primary};
              background-color: ${colors.interactive.dark.hover};
              > span {
                color: ${colors.schems.dark.onBase};
              }
            }
            > .options {
              z-index: 10;
              position: ${position};
              display: block;
              padding: ${schemes.spacing.sm} 0;
              border: 1px solid ${colors.border.dark.bgLighten};
              border-radius: ${schemes.radius.sm};
              background: ${colors.bg.dark.surfaceBright};
              max-height: 13rem;
              white-space: nowrap;
              overflow: scroll;
              &.up {
                left: 0;
                right: 0;
                bottom: ${position === "relative" ? "0" : "32px"};
              }
              &.down {
                left: 0;
                right: 0;
                top: ${position === "relative" ? "0" : "32px"};
              }
              > div {
                display: flex;
                align-items: center;
                height: 2.5rem;
                padding: 0 ${schemes.spacing.xl};
                cursor: pointer;
                > span {
                  padding: 0 ${schemes.spacing.sm};
                  font-size: ${fonts.body.body.fontSize};
                  font-weight: ${fonts.body.body.fontWeight};
                  line-height: ${fonts.body.body.lineHeight};
                }
              }
            }
            &.full {
              > .options {
                &.up {
                  bottom: ${position === "relative" ? "0" : "40px"};
                }
                &.down {
                  top: ${position === "relative" ? "0" : "40px"};
                }
              }
              &.label {
                /* 40px + 25px */
                > .options {
                  &.up {
                    bottom: ${position === "relative"
                      ? "0"
                      : "65px"} !important;
                  }
                  &.down {
                    top: ${position === "relative" ? "0" : "65px"} !important;
                  }
                }
              }
            }
            &.label {
              /* 32px + 25px */
              > .options {
                &.up {
                  bottom: ${position === "relative" ? "0" : "57px"};
                }
                &.down {
                  top: ${position === "relative" ? "0" : "57px"};
                }
              }
            }
          }
          &.disabled {
            > button {
              cursor: not-allowed;
              background: ${colors.schems.dark.disabled};
              > span {
                color: ${colors.schems.dark.onDisabled} !important;
              }
            }
          }
        }
      `}</style>
    </>
  )
}
