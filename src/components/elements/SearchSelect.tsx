import { useState } from "react"
import Input from "@/components/elements/Input"
import { colors, schemes } from "@/utils/theme/style"
import LabelButton from "@/components/elements/LabelButton"
import { getAssetPath, randNumberFromStr } from "@/utils/util"
import { ColorType } from "@/common/types/colorType"

export default function SearchSelect({
  list,
  pickList,
  action,
}: {
  list: string[]
  pickList: string[]
  action: (checked: boolean, value: string) => void
}) {
  const [search, setSearch] = useState<string>("")
  const colorsArr = [
    "red",
    "orange",
    "yellow",
    "green",
    "cyan",
    "blue",
    "indigo",
    "purple",
    "pink",
  ]

  return (
    <>
      <div className="search-select">
        <div>
          <Input
            name={"search"}
            value={search}
            beforeIcon={{
              path: getAssetPath("search.svg"),
              color: colors.schems.dark.disabled,
            }}
            onChange={setSearch}
          />
        </div>
        <div className={"search-list"}>
          <div>
            {list
              .filter((item) => !pickList.includes(item))
              .map((item) => {
                if (search.length > 0) {
                  if (item.toLowerCase().indexOf(search.toLowerCase()) !== -1) {
                    return (
                      <div key={item} onClick={() => action(true, item)}>
                        <LabelButton>{item}</LabelButton>
                      </div>
                    )
                  }
                } else {
                  return (
                    <div key={item} onClick={() => action(true, item)}>
                      <LabelButton>{item}</LabelButton>
                    </div>
                  )
                }
              })}
          </div>
        </div>
        <div>
          {pickList.map((item) => {
            return (
              <div key={item} onClick={() => action(false, item)}>
                <LabelButton
                  color={
                    colorsArr[
                      randNumberFromStr(item) % colorsArr.length
                    ] as ColorType
                  }
                >
                  {item}
                </LabelButton>
              </div>
            )
          })}
        </div>
      </div>
      <style jsx>{`
        .search-select {
          display: flex;
          flex-direction: column;
          gap: ${schemes.spacing.md};

          & .search-list {
            height: 15rem;
            padding: ${schemes.spacing["2xl"]};
            border-radius: ${schemes.radius.sm};
            border: 1px solid ${colors.border.dark.default};
            background: ${colors.bg.dark.surfaceBright};
            overflow-y: scroll;
            -ms-overflow-style: none;
            &::-webkit-scrollbar {
              display: none;
            }
            & > div,
            & + div {
              display: flex;
              flex-direction: column;
              gap: ${schemes.spacing.md};

              & > div {
                flex: none;
                width: fit-content;
              }
            }
          }

          div {
            border: 0 !important;
          }
        }
      `}</style>
    </>
  )
}
