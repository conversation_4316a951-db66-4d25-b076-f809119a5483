import { colors, fonts, schemes } from "@/utils/theme/style"
import Icon from "@/components/elements/Icon"
import { ColorType } from "@/common/types/colorType"
import { getAssetPath } from "@/utils/util"

export default function LabelButton({
  children,
  color,
  action,
  size = "md",
}: {
  children?: React.ReactNode
  color?: ColorType | undefined
  action?: () => void | undefined
  size?: "sm" | "md"
}) {
  return (
    <>
      <button onClick={action} className={`${size}`}>
        <span>{children}</span>
        {color && (
          <Icon
            path={getAssetPath("cancel.svg")}
            size={"1rem"}
            color={colors.sys.dark[color].darken}
          />
        )}
      </button>
      <style jsx>{`
        button {
          max-width: 100%;
          cursor: ${action || color ? "pointer" : "cursor"};
          border: none;
          border-radius: ${schemes.radius.rounded};
          display: inline-flex;
          justify-content: center;
          align-items: center;
          height: 1.5rem;
          padding: 0 ${schemes.spacing.sm};
          flex-shrink: 0;
          background-color: ${color
            ? colors.sys.dark[color].lighten
            : colors.schems.dark.disabled};
          & span {
            display: inline-block;
            width: 100%;
            min-width: 1rem;
            text-align: center;
            padding: 0 ${schemes.spacing.sm};
            color: ${color
              ? colors.sys.dark[color].darken
              : colors.schems.dark.onDisabled};
            font-size: ${fonts.body.label.fontSize};
            font-weight: ${fonts.body.label.fontWeight};
            line-height: ${fonts.body.label.lineHeight};
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          &.sm {
            height: 1.25rem;
            & span {
              padding: 0;
              min-width: 0.75rem;
              font-size: ${fonts.body.bodyBold.fontSize};
              line-height: 1.25rem;
            }
          }
        }
      `}</style>
    </>
  )
}
