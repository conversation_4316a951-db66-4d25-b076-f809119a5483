import Icon from "@/components/elements/Icon"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { ColorType } from "@/common/types/colorType"
import { getAssetPath } from "@/utils/util"
import { useEffect } from "react"
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  type?: "checkbox"
  color?: ColorType | undefined
  textColor?: string
  isBlock?: boolean
  defaultChecked?: boolean
  children?: React.ReactNode
}

export default function Checkbox({
  type = "checkbox",
  color,
  textColor = colors.schems.dark.onBase,
  isBlock = false,
  defaultChecked,
  children,
  ...props
}: InputProps) {
  return (
    <>
      <label
        className={`checkbox-label ${isBlock ? "block" : ""} ${props?.disabled ? "disabled" : ""}`}
      >
        <input defaultChecked={defaultChecked} {...props} type={type} />
        <div className={"icon"}>
          <div className={"false"}>
            <Icon
              path={getAssetPath("checkbox_false.svg")}
              color={
                props?.disabled
                  ? colors.schems.dark.disabled
                  : color !== undefined
                    ? colors.sys.dark?.[color]?.default
                    : colors.schems.dark.onBase
              }
            />
          </div>
          <div className={"true"}>
            <Icon
              path={getAssetPath("checkbox_true.svg")}
              color={
                props?.disabled
                  ? colors.schems.dark.disabled
                  : color
                    ? colors.sys.dark?.[color]?.default
                    : colors.schems.dark.primary
              }
            />
          </div>
        </div>
        {children && <div className={"children"}>{children}</div>}
      </label>
      <style jsx>{`
        .checkbox-label {
          flex: 1;
          display: flex;
          justify-content: left;
          align-items: center;
          width: 100%;
          & > input[type="checkbox"] {
            display: none;
            & + div {
              .false {
                display: block;
                font-size: 0;
              }
              .true {
                display: none;
                font-size: 0;
              }
            }
            &:checked {
              & + div {
                .false {
                  display: none;
                }
                .true {
                  display: block;
                }
              }
            }
          }
          & > div.icon {
            width: 1.5rem;
            height: 1.5rem;
          }
          & > div.children {
            display: inline-block;
            width: 100%;
            padding: 0 ${schemes.spacing.sm};
            color: ${textColor};
            font-size: ${fonts.body.body.fontSize};
            font-weight: ${fonts.body.body.fontWeight};
            line-height: ${fonts.body.body.lineHeight};

            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          &.block {
            height: 2.5rem;
            padding: 0 ${schemes.spacing.xl};
            border-radius: ${schemes.radius.sm};
            border: 1px solid ${colors.border.dark.default};
            background: ${colors.schems.dark.secondary};
          }
          &.disabled {
            & > div:last-child {
              color: ${colors.schems.dark.onDisabled};
            }
          }
        }
      `}</style>
    </>
  )
}
