import Icon from "@/components/elements/Icon"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { ColorType } from "@/common/types/colorType"
import { getAssetPath } from "@/utils/util"
import React from "react"

export default function Checkbox({
  color,
  textColor = colors.schems.dark.onBase,
  disabled = false,
  isBlock = false,
  children,
  onChange,
  isChecked,
  indeterminate,
}: {
  color?: ColorType | undefined
  textColor?: string
  disabled?: boolean
  isBlock?: boolean
  children?: React.ReactNode
  onChange: (value: React.ChangeEvent<HTMLInputElement>) => void
  isChecked: boolean
  indeterminate?: boolean
}) {
  return (
    <>
      <label
        className={`checkbox-label ${isBlock ? "block" : ""} ${disabled ? "disabled" : ""}`}
      >
        <input
          type="checkbox"
          checked={isChecked}
          onChange={onChange}
          disabled={disabled}
        />
        <div className={"icon"}>
          <Icon
            path={
              isChecked
                ? getAssetPath("checkbox_true.svg")
                : indeterminate
                  ? getAssetPath("indeterminate_check_box.svg")
                  : getAssetPath("checkbox_false.svg")
            }
            color={
              disabled
                ? colors?.schems?.dark?.disabled || '#71717b'
                : color
                  ? colors?.sys?.dark?.[color]?.default || '#26a4ff'
                  : isChecked
                    ? colors?.schems?.dark?.primary || '#26a4ff'
                    : colors?.schems?.dark?.onBase || '#e4e4e7'
            }
          />
        </div>
        {children && <div className={"children"}>{children}</div>}
      </label>
      <style jsx>{`
        .checkbox-label {
          flex: 1;
          display: flex;
          justify-content: left;
          align-items: center;
          & > input {
            display: none;
          }
          & > div.icon {
            width: 1.5rem;
            height: 1.5rem;
          }
          & > div.children {
            display: inline-block;
            width: 100%;
            padding: 0 ${schemes.spacing.sm};
            color: ${textColor};
            font-size: ${fonts.body.body.fontSize};
            font-weight: ${fonts.body.body.fontWeight};
            line-height: ${fonts.body.body.lineHeight};

            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          &.block {
            height: 2.5rem;
            padding: 0 ${schemes.spacing.xl};
            border-radius: ${schemes.radius.sm};
            border: 1px solid ${colors?.border?.dark?.default || '#27272a'};
            background: ${colors?.schems?.dark?.secondary || '#71717b'};
          }
          &.disabled {
            & > div:last-child {
              color: ${colors?.schems?.dark?.onDisabled || '#9f9fa9'};
            }
          }
        }
      `}</style>
    </>
  )
}
