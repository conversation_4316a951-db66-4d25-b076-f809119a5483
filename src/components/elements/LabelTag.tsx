import { colors, fonts, schemes } from "@/utils/theme/style"
import { ColorType } from "@/common/types/colorType"
import { useMemo } from "react"

export default function LabelTag({
  children,
  color = "gray",
  type = "default",
  lightness = "default",
  gap = schemes.spacing.sm,
}: {
  children?: React.ReactNode
  color?: ColorType
  type?: "status" | "folder" | "default"
  lightness?: "default" | "darken" | "lighten"
  gap?: string
}) {
  const fillColors = useMemo(() => {
    if (type === "default" && color === "gray") {
      return {
        text: colors.sys.dark.gray.darken,
        background: colors.sys.dark.gray.default,
      }
    }

    return {
      text: colors.sys.dark[color][lightness],
      background: colors.sys.dark[color].lighten,
    }
  }, [color, lightness, type])

  return (
    <>
      <label className={`${type}`}>
        {type === "status" && (
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="8"
              height="8"
              viewBox="0 0 8 8"
              fill="none"
            >
              <circle cx="4" cy="4" r="4" fill={`${fillColors.text}`} />
            </svg>
          </span>
        )}
        {type === "folder" && (
          <span>
            <svg
              width="4"
              height="12"
              viewBox="0 0 4 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 2C0 0.895431 0.895431 0 2 0C3.10457 0 4 0.895431 4 2V10C4 11.1046 3.10457 12 2 12C0.895431 12 0 11.1046 0 10V2Z"
                fill={`${fillColors.text}`}
              />
            </svg>
          </span>
        )}
        <span>{children}</span>
      </label>
      <style jsx>{`
        label {
          display: inline-flex;
          justify-content: center;
          align-content: center;
          &.default {
            padding: 0 ${schemes.spacing.sm};
            border-radius: ${schemes.radius.sm};
            background-color: ${fillColors.background};
            & span {
              color: ${colors.sys.dark[color].darken};
              font-size: ${fonts.body.label.fontSize};
              font-weight: ${fonts.body.label.fontWeight};
              line-height: ${fonts.body.label.lineHeight};
              white-space: nowrap;
            }
          }
          &.status,
          &.folder {
            gap: ${gap};
            & span {
              color: ${colors.schems.dark.onBase};
              font-size: ${fonts.body.body.fontSize};
              font-weight: ${fonts.body.body.fontWeight};
              line-height: ${fonts.body.body.lineHeight};
              white-space: nowrap;
            }
          }
          &.status {
            span:first-of-type {
              width: 0.5rem;
              height: 0.5rem;
            }
          }
          &.folder {
            span:first-of-type {
              width: 0.25rem;
              height: 0.75rem;
            }
          }
        }
      `}</style>
    </>
  )
}
