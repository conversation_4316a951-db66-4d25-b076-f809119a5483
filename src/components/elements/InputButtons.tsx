import { schemes } from "@/utils/theme/style"
import CheckButton from "@/components/elements/CheckButton"

export default function InputButtons({
  type = "radio",
  data,
  action,
  col = 3,
  checkValues,
}: {
  type?: "checkbox" | "radio"
  data: any[]
  action: (value?: string) => void
  col?: number
  checkValues?: string[] | string | object | null
}) {
  const randNum = Math.floor(Math.random() * 10000)
  return (
    <>
      <div className={`radio-group`}>
        {data.map((obj, index) => (
          <CheckButton
            key={index}
            type={type}
            inputName={`radio-${randNum}`}
            data={obj}
            action={action}
            checked={
              !checkValues
                ? false
                : Array.isArray(checkValues)
                  ? checkValues.includes(obj)
                  : typeof obj === "string"
                    ? checkValues === obj
                    : checkValues === JSON.stringify(obj)
            }
          >
            {typeof obj === "object" ? obj.label : obj}
          </CheckButton>
        ))}
      </div>
      <style jsx>{`
        .radio-group {
          display: grid;
          grid-gap: ${schemes.spacing.md};
          grid-template-columns: repeat(${col}, 1fr);
          div {
            border-bottom: 0 !important;
          }
        }
      `}</style>
    </>
  )
}
