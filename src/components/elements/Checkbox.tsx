import Icon from "@/components/elements/Icon"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { ColorType } from "@/common/types/colorType"
import { getAssetPath } from "@/utils/util"

export default function Checkbox({
  color,
  textColor = colors.schems.dark.onBase,
  data,
  action,
  disabled = false,
  isBlock = false,
  children,
  isChecked,
}: {
  color?: ColorType | undefined
  textColor?: string
  data?: any
  action?: (value?: any) => void
  disabled?: boolean
  isBlock?: boolean
  children?: React.ReactNode
  isChecked?: boolean
}) {
  return (
    <>
      <label
        className={`checkbox-label ${isBlock ? "block" : ""} ${disabled ? "disabled" : ""}`}
      >
        <input
          type="checkbox"
          checked={isChecked}
          value={JSON.stringify(data)}
          onChange={(e) => {
            if (action) {
              action({ checked: e.target.checked, data: e.target.value })
            }
          }}
          disabled={disabled}
        />
        <div className={"icon"}>
          <Icon
            path={
              isChecked
                ? getAssetPath("checkbox_true.svg")
                : getAssetPath("checkbox_false.svg")
            }
            color={
              disabled
                ? colors.schems.dark.disabled
                : color
                  ? colors.sys.dark[color].default
                  : isChecked
                    ? colors.schems.dark.primary
                    : colors.schems.dark.onBase
            }
          />
        </div>
        {children && <div className={"children"}>{children}</div>}
      </label>
      <style jsx>{`
        .checkbox-label {
          flex: 1;
          display: flex;
          justify-content: left;
          align-items: center;
          & > input {
            display: none;
          }
          & > div.icon {
            width: 1.5rem;
            height: 1.5rem;
          }
          & > div.children {
            display: inline-block;
            width: 100%;
            padding: 0 ${schemes.spacing.sm};
            color: ${textColor};
            font-size: ${fonts.body.body.fontSize};
            font-weight: ${fonts.body.body.fontWeight};
            line-height: ${fonts.body.body.lineHeight};

            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          &.block {
            height: 2.5rem;
            padding: 0 ${schemes.spacing.xl};
            border-radius: ${schemes.radius.sm};
            border: 1px solid ${colors.border.dark.default};
            background: ${colors.schems.dark.secondary};
          }
          &.disabled {
            & > div:last-child {
              color: ${colors.schems.dark.onDisabled};
            }
          }
        }
      `}</style>
    </>
  )
}
