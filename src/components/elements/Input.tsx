import Icon from "@/components/elements/Icon"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { useState } from "react"
import { getAssetPath } from "@/utils/util"

export default function Input({
  type = "text",
  value,
  name,
  placeholderText = "Place Holder",
  autoComplete,
  status = "default",
  onChange,
  isRightBtn = true,
  isRequired = false,
  disabled = false,
  label,
  beforeIcon,
  helper,
  number,
  onFocus,
  onBlur,
}: {
  type?: "text" | "password" | "email" | "number"
  value?: string
  name?: string
  placeholderText?: string
  autoComplete?: string
  status?: "default" | "success" | "error" | "disabled"
  onChange?: (value: any) => void
  isRightBtn?: boolean
  isRequired?: boolean
  disabled?: boolean
  label?: {
    text: string
  }
  beforeIcon?: {
    path: string
    color: string
  }
  helper?: {
    text: string
  }
  number?: {
    min: number
    max: number
    step: number
  }
  onFocus?: () => void
  onBlur?: () => void
}) {
  const [isView, setIsView] = useState(false)

  return (
    <>
      <div className={`${status}`}>
        {label && <label>{label.text}</label>}
        <div className={`inputArea`}>
          {beforeIcon && (
            <div className={`beforeIcon`}>
              <Icon path={beforeIcon.path} color={beforeIcon.color} />
            </div>
          )}
          <input
            name={name}
            defaultValue={value}
            autoComplete={autoComplete ? autoComplete : "one-time-code"}
            type={`${type === "password" ? (isView ? "text" : "password") : type}`}
            placeholder={placeholderText}
            required={isRequired}
            onChange={(e) => {
              const val = e.target.value
              if (onChange) {
                if (type === "number" && number) {
                  if (Number(val) > number?.max) {
                    onChange(number?.max)
                  } else if (Number(val) < number?.min) {
                    onChange(number?.min)
                  } else {
                    onChange(Number(val))
                  }
                } else {
                  onChange(val)
                }
              }
            }}
            min={number?.min}
            max={number?.max}
            step={number?.step}
            disabled={disabled}
            onFocus={onFocus}
            onBlur={onBlur}
          />
          {isRightBtn && type === "password" && (
            <button type={"button"} onClick={() => setIsView(!isView)}>
              <Icon
                path={
                  isView
                    ? getAssetPath("visibility_true.svg")
                    : getAssetPath("visibility_off_true.svg")
                }
                color={colors.schems.dark.onBase}
                size={"1rem"}
              />
            </button>
          )}
          {isRightBtn && type === "text" && (
            <button
              type={"button"}
              onClick={() => onChange && onChange("")}
              className={"clear"}
            >
              <Icon
                path={getAssetPath("cancel.svg")}
                color={colors.schems.dark.onBase}
                size={"1rem"}
              />
            </button>
          )}
        </div>
        {helper && (
          <div className={"helper"}>
            {status === "success" && (
              <Icon
                path={getAssetPath("check.svg")}
                color={colors.sys.dark.green.default}
                size={"1rem"}
              />
            )}
            {status === "error" && (
              <Icon
                path={getAssetPath("cancel.svg")}
                color={colors.sys.dark.red.default}
                size={"1rem"}
              />
            )}
            <p>{helper.text}</p>
          </div>
        )}
      </div>
      <style jsx>{`
        .inputArea {
          position: relative;
          width: 100%;
          .beforeIcon {
            width: 1.5rem;
            height: 1.5rem;
            position: absolute;
            left: ${schemes.spacing.xl};
            top: 50%;
            transform: translateY(-50%);
          }
          & > input {
            gap: ${schemes.spacing.md};
            width: 100%;
            height: 2.5rem;
            padding: 0 ${beforeIcon ? "2.5rem" : schemes.spacing["2xl"]};

            outline: none;
            border: 1px solid ${colors.border.dark.default};
            border-radius: ${schemes.radius.sm};
            box-shadow: none;
            background: ${colors.interactive.dark.hover};
            caret-color: ${colors.border.dark.primary};

            color: ${colors.schems.dark.onBase};
            font-weight: ${fonts.body.body.fontWeight};
            line-height: ${fonts.body.body.lineHeight};

            &:-webkit-autofill::placeholder,
            &::placeholder {
              overflow: hidden;
              color: ${colors.schems.dark.onDisabled};
              text-overflow: ellipsis;
              font-size: ${fonts.body.body.fontSize};
              font-weight: ${fonts.body.body.fontWeight};
              line-height: ${fonts.body.body.lineHeight};
            }

            &:focus-visible {
              border-color: ${colors.border.dark.primary};
            }

            &:-webkit-autofill,
            &:-webkit-autofill:hover,
            &:-webkit-autofill:focus-visible {
              -webkit-text-fill-color: ${colors.schems.dark.onBase};
              -webkit-box-shadow: 0 0 0 0 ${colors.interactive.dark.hover} inset;
              transition: background-color 5000s ease-in-out 0s;
            }
            /* Chrome, Safari, Edge, Opera */
            &::-webkit-outer-spin-button,
            &::-webkit-inner-spin-button {
              -webkit-appearance: none;
              margin: 0;
            }

            /* Firefox  */
            &[type="number"] {
              -moz-appearance: textfield;
            }

            &:disabled {
              background: ${colors.schems.dark.disabled};
              color: ${colors.schems.dark.onDisabled};
            }
          }
          button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 1.5rem;
            height: 1.5rem;
            padding: 0;
            background: transparent;
            border: 0;
            position: absolute;
            right: ${schemes.spacing["2xl"]};
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            border-radius: 99px;
            &:hover {
              background: ${colors.interactive.dark.hover};
            }
            &:active {
              background: ${colors.interactive.dark.pressed};
            }
            &.clear {
              display: none;
            }
          }
          input:focus-visible ~ button.clear,
          input:not(:placeholder-shown) ~ button.clear,
          button.clear:hover {
            display: flex;
          }
        }
        label {
          display: block;
          margin-bottom: ${schemes.spacing.sm};
          font-size: ${fonts.body.label.fontSize};
          font-weight: ${fonts.body.label.fontWeight};
          line-height: ${fonts.body.label.lineHeight};
        }
        .helper {
          margin-top: ${schemes.spacing.sm};
          display: flex;
          justify-content: left;
          align-items: center;
          gap: ${schemes.spacing.sm};
        }
        p {
          margin: 0;
          color: ${colors.schems.dark.onDisabled};
          font-size: ${fonts.body.label.fontSize};
          font-weight: ${fonts.body.label.fontWeight};
          line-height: ${fonts.body.label.lineHeight};
        }
        .success {
          p {
            color: ${colors.sys.dark.green.default};
          }
        }
        .error {
          input {
            border: 1px solid ${colors.sys.dark.red.default} !important;
          }
          p {
            color: ${colors.sys.dark.red.default};
          }
        }
        .disabled {
        }
      `}</style>
    </>
  )
}
