import React from "react"
import Icon from "@/components/elements/Icon"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { getAssetPath } from "@/utils/util"

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  type?: "button" | "submit" | "reset"
  icon?: {
    path: string
    color?: string
  }
  styleType?: "primary" | "secondary" | "text"
  size?: "xs" | "sm" | "default"
  full?: boolean
  isLoading?: boolean
  children?: React.ReactNode
  color?: string
}
export default function Button({
  type = "button",
  icon,
  styleType = "primary",
  size = "default",
  full = false,
  isLoading = false,
  children,
  color,
  ...props
}: ButtonProps) {
  return (
    <>
      <button
        {...props}
        type={type}
        className={`${styleType} ${size} ${full && "full"}`}
      >
        {icon && !isLoading && (
          <Icon
            path={icon.path}
            color={
              props?.disabled
                ? colors.schems.dark.onDisabled
                : (icon.color ??
                  (styleType === "primary"
                    ? colors.schems.dark.onPrimary
                    : colors.schems.light.onSecondary))
            }
            size={"1rem"}
          />
        )}
        {isLoading && (
          <Icon
            path={getAssetPath("loading.svg")}
            color={
              props?.disabled
                ? colors.schems.dark.onDisabled
                : styleType === "primary"
                  ? colors.schems.dark.onPrimary
                  : colors.schems.light.onSecondary
            }
            size={"1rem"}
            isSpin={true}
          />
        )}
        {children && (
          <span style={{ color: `${color ? color : ""}` }}>{children}</span>
        )}
      </button>
      <style jsx>{`
        button {
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 0.25rem;
          height: 2.5rem;
          padding: 0 ${schemes.spacing.xl};
          border-radius: ${schemes.radius.sm};
          font-family: inherit;
          border: none;
          &:hover {
            filter: brightness(1.12);
          }
          &:active {
            filter: brightness(1.16);
          }
          &:disabled {
            background: ${colors.schems.dark.disabled} !important;
            cursor: not-allowed;
            filter: none;
            span {
              color: ${colors.schems.dark.onDisabled} !important;
            }
          }
          &.primary {
            background: ${colors.schems.dark.primary};
            span {
              color: ${colors.schems.dark.onPrimary};
            }
          }
          &.secondary {
            background: ${colors.schems.dark.secondary};
            span {
              color: ${colors.schems.dark.onSecondary};
            }
          }
          &.text {
            background: none;
            span {
              color: ${colors.schems.dark.onSecondary};
            }
            &:hover {
              background: ${colors.interactive.dark.hover};
              filter: none;
              span {
                filter: brightness(1.12);
              }
            }
            &:active {
              background: ${colors.interactive.dark.pressed};
              filter: none;
              span {
                filter: brightness(1.16);
              }
            }
          }

          &.xs {
            height: 1.5rem;
            padding: ${schemes.spacing.sm};
          }
          &.sm {
            height: 2rem;
            padding: ${schemes.spacing.md};
          }
          &.full {
            width: 100%;
          }

          span {
            display: inline-block;
            padding: 0 ${schemes.spacing.sm};
            text-align: center;
            font-size: ${fonts.body.bodyBold.fontSize};
            font-weight: ${fonts.body.bodyBold.fontWeight};
            line-height: ${fonts.body.bodyBold.lineHeight};
          }
        }
      `}</style>
    </>
  )
}
