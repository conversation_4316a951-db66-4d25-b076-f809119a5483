import { ReactNode } from "react"
import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"
import { colors, fonts, schemes } from "@/utils/theme/style"
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  type?: "radio"
  trueColor?: string
  falseColor?: string
  children: ReactNode
}

export default function Radio({
  type = "radio",
  trueColor = `${colors.schems.dark.primary}`,
  falseColor = `${colors.schems.dark.onBase}`,
  children,
  ...props
}: InputProps) {
  return (
    <>
      <label>
        <input {...props} type={type} />
        <div>
          <div className={"false"}>
            <Icon
              path={getAssetPath("radio_false.svg")}
              color={props?.disabled ? colors.schems.dark.disabled : falseColor}
            />
          </div>
          <div className={"true"}>
            <Icon path={getAssetPath("radio_true.svg")} color={trueColor} />
          </div>
          <div className={props?.disabled ? "disabled" : ""}>{children}</div>
        </div>
      </label>
      <style jsx>{`
        input[type="radio"] {
          display: none;
          & + div {
            display: flex;
            align-items: center;
            gap: ${schemes.spacing.sm};
            > div:last-child {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;

              font-size: ${fonts.body.body.fontSize};
              font-weight: ${fonts.body.body.fontWeight};
              line-height: ${fonts.body.body.lineHeight};
              &.disabled {
                color: ${colors.schems.dark.disabled};
              }
            }
            > .false {
              display: block;
              font-size: 0;
            }
            > .true {
              display: none;
              font-size: 0;
            }
          }
          &:checked + div {
            > .false {
              display: none;
            }
            > .true {
              display: block;
            }
          }
        }
      `}</style>
    </>
  )
}
