import Icon from "@/components/elements/Icon"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { getAssetPath } from "@/utils/util"

export default function CheckButton({
  type = "radio",
  inputName,
  data,
  checked,
  setChecked,
  action,
  children,
}: {
  type?: "checkbox" | "radio"
  inputName?: string
  data?: any
  checked?: boolean
  setChecked?: (checked: boolean) => void
  action?: (value?: any) => void
  children?: React.ReactNode
}) {
  return (
    <>
      <div>
        <label>
          {checked !== undefined && setChecked ? (
            <input
              type={type}
              name={inputName}
              defaultChecked={checked}
              onClick={() => {
                setChecked(!checked)
                if (action && data) {
                  action({ checked: !checked, data: JSON.stringify(data) })
                }
              }}
            />
          ) : (
            <>
              {checked !== undefined ? (
                <input
                  type={type}
                  name={inputName}
                  checked={checked}
                  onChange={(e) => {
                    if (action && data) {
                      action({ checked: !checked, data: data })
                    }
                  }}
                />
              ) : (
                <input
                  type={type}
                  name={inputName}
                  value={data && JSON.stringify(data)}
                  onChange={(e) => {
                    if (action) {
                      action({
                        checked: e.target.checked,
                        data: e.target.value,
                      })
                    }
                  }}
                />
              )}
            </>
          )}
          <div>
            <div>
              <Icon
                path={getAssetPath("check.svg")}
                size={"1rem"}
                color={colors.schems.dark.primary}
              />
            </div>
            <span>{children}</span>
          </div>
        </label>
      </div>
      <style jsx>{`
        label {
          & > input {
            display: none;
            & + div {
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;
              height: 2rem;
              border-radius: ${schemes.radius.sm};
              font-family: inherit;
              border: none;
              background: ${colors.schems.dark.secondary};
              &:hover {
                filter: brightness(1.12);
              }
              &:active {
                filter: brightness(1.16);
              }
              & > div {
                display: none;
                height: 1rem;
              }
              & > span {
                display: inline-block;
                padding: 0 ${schemes.spacing.sm};
                color: ${colors.schems.dark.onDisabled};
                font-size: ${fonts.body.bodyBold.fontSize};
                font-weight: ${fonts.body.bodyBold.fontWeight};
                line-height: ${fonts.body.bodyBold.lineHeight};
              }
            }
            &:checked {
              & + div {
                & > div {
                  display: inline-block;
                }
                & span {
                  color: ${colors.schems.dark.primary};
                }
              }
            }
          }
        }
      `}</style>
    </>
  )
}
