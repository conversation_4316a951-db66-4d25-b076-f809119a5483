import Input from "@/components/elements/Input"
import { getAssetPath } from "@/utils/util"
import { colors, schemes } from "@/utils/theme/style"
import Button from "@/components/elements/Button"
import React, { useMemo, useState } from "react"
import useWorkspace from "@/store/useWorkspace"
import Checkbox from "@/components/elements/Checkbox3"
import { ColorType } from "@/common/types/colorType"
import Link from "next/link"
import BulkSetCustomTagModal from "@/components/modal/BulkSetCustomTagModal"
import { getUserInfo } from "@/service/user"
import { WorkSpaceTagType, WorkspaceType } from "@/common/types/WorkspaceType"
import { useAuth } from "@/common/hooks/useAuthProvider"
import useUserInfo from "@/store/useUserInfo"
import Loading from "@/components/common/Loading"
import Information from "@/components/common/Information"
import AddTagModal from "@/components/modal/AddTagModal"

export default function SelectCreators({
  creatorTags,
  addTag,
  removeTag,
  prev,
  next,
  nextDisabled,
}: {
  creatorTags: number[]
  addTag: (tag: number) => void
  removeTag: (tag: number) => void
  prev: () => void
  next: () => void
  nextDisabled: boolean
}) {
  const auth = useAuth()
  const { userInfo, setUserInfo } = useUserInfo()
  const { setTags, setWorkspace } = useWorkspace()
  const userId = userInfo?.user_id ?? auth.user?.id
  const accessToken = auth.session?.access_token
  const { tags } = useWorkspace()
  const [searchTag, setSearchTag] = useState<string>("")
  const [bulkImportModal, setBulkImportModal] = useState(false)
  const [isTagsLoading, setIsTagsLoading] = useState(false)
  const [isTagAddModal, setIsTagAddModal] = useState(false)

  const filterTags = useMemo(() => {
    return searchTag.length
      ? tags.filter(
          (tag) =>
            tag.name.toLowerCase().indexOf(searchTag.toLowerCase()) !== -1
        )
      : tags
  }, [searchTag, tags])

  const refreshTags = () => {
    if (userId && accessToken) {
      setIsTagsLoading(true)
      getUserInfo(userId, accessToken)
        .then((res) => {
          if (res.userInfo) {
            setUserInfo(res.userInfo)
            setWorkspace(res.userInfo.workspace as WorkspaceType)
            setTags(res.userInfo.workspaceTags as WorkSpaceTagType[])
          }
        })
        .catch((err) => {
          console.error(err.message)
        })
        .finally(() => setIsTagsLoading(false))
    }
  }

  return (
    <>
      <div className={"creator-list"}>
        <div className={"list"}>
          <Input
            type={"text"}
            value={searchTag}
            placeholderText={"Search tag"}
            beforeIcon={{
              path: getAssetPath("search.svg"),
              color: colors.schems.dark.disabled,
            }}
            onChange={setSearchTag}
          />
          <ul>
            {!isTagsLoading ? (
              filterTags.length > 0 && filterTags[0].count > 0 ? (
                filterTags.map((tag) => (
                  <li key={tag.id}>
                    <Checkbox
                      color={tag.color as ColorType}
                      onChange={(e) => {
                        if (e.target.checked) {
                          addTag(tag.id as number)
                        } else {
                          removeTag(tag.id as number)
                        }
                      }}
                      checked={creatorTags.includes(tag.id as number)}
                    >
                      <div className={"tag-name"}>
                        <div className={"name"}>
                          {tag.name === "default" ? "All" : tag.name}
                        </div>
                        <span className={"count"}>
                          ({tag.count.toLocaleString()})
                        </span>
                      </div>
                    </Checkbox>
                  </li>
                ))
              ) : (
                <div>
                  <p>Need a list? Create one in Saved.</p>
                  <div>
                    <Link href="/saved" style={{ textDecoration: "none" }}>
                      <Button
                        style={"secondary"}
                        icon={{ path: getAssetPath("arrow_outward.svg") }}
                      >
                        Go to Saved
                      </Button>
                    </Link>
                  </div>
                </div>
              )
            ) : (
              <Loading />
            )}
          </ul>
        </div>
        <Information>
          You can still edit your creator list before sending.
        </Information>
        <div className={"buttons"}>
          <div>
            <Link href="/explore" style={{ textDecoration: "none" }}>
              <Button
                style={"secondary"}
                icon={{ path: getAssetPath("arrow_outward.svg") }}
              >
                Go to Explore
              </Button>
            </Link>
            <Link href="/saved" style={{ textDecoration: "none" }}>
              <Button
                style={"secondary"}
                icon={{ path: getAssetPath("arrow_outward.svg") }}
              >
                Go to Saved
              </Button>
            </Link>
            <Button
              type={"button"}
              icon={{ path: getAssetPath("upload.svg") }}
              style={"secondary"}
              onClick={() => {
                setBulkImportModal((prev) => !prev)
              }}
            >
              Import
            </Button>
          </div>
          <div>
            <Button style={"text"} onClick={prev}>
              Previous
            </Button>
            <Button onClick={next} disabled={creatorTags.length < 1}>
              Next
            </Button>
          </div>
        </div>
      </div>
      {bulkImportModal && (
        <>
          <BulkSetCustomTagModal
            isOpen={bulkImportModal}
            setIsTagAddModal={setIsTagAddModal}
            reload={() => {
              refreshTags()
            }}
            setIsOpen={() => {
              setBulkImportModal((prev) => !prev)
            }}
          />
          <AddTagModal isOpen={isTagAddModal} setIsOpen={setIsTagAddModal} />
        </>
      )}
      <style jsx>{`
        .creator-list {
          display: flex;
          flex-direction: column;
          gap: ${schemes.spacing["2xl"]};

          > .list {
            display: flex;
            flex-direction: column;
            gap: ${schemes.spacing.md};
            > ul {
              list-style: none;
              margin: 0;
              padding: ${schemes.spacing.md} 0;
              height: 16.5rem;
              border-radius: ${schemes.radius.sm};
              border: 1px solid ${colors.border.dark.bgLighten};
              background: ${colors.bg.dark.surfaceBright};
              box-shadow:
                0 1px 3px 1px rgba(0, 0, 0, 0.15),
                0 1px 2px 0 rgba(0, 0, 0, 0.3);
              overflow-y: scroll;
              -ms-overflow-style: none;
              &::-webkit-scrollbar {
                display: none;
              }
              > li {
                padding: 0 ${schemes.spacing.xl};
                height: 2.5rem;
                display: flex;
                align-items: center;
                > * {
                  flex: 1;
                }
                .tag-name {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                }
              }
              > div {
                height: 100%;
                padding: 0 ${schemes.spacing.xl};
                display: flex;
                flex-direction: column;
                gap: ${schemes.spacing.md};
                align-items: center;
                justify-content: center;
                > * {
                  margin: 0;
                }
              }
            }
          }
          > .buttons {
            display: flex;
            justify-content: space-between;
            > div {
              display: flex;
              gap: ${schemes.spacing.md};
              &:first-child {
                justify-content: start;
              }
              &:last-child {
                justify-content: end;
              }
            }
          }
        }
      `}</style>
    </>
  )
}
