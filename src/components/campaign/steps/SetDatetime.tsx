import SelectBox from "@/components/elements/SelectBox"
import Checkbox from "@/components/elements/Checkbox3"
import { colors, fonts, schemes } from "@/utils/theme/style"
import Button from "@/components/elements/Button"
import React, { useCallback, useEffect, useMemo, useState } from "react"
import { getTimezonesForCountry } from "countries-and-timezones"
import { calcDateByTimezone, dateFormatYmd } from "@/common/utils/date"
import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"
import Toggle from "@/components/elements/Toggle"
import Information from "@/components/common/Information"

const weeks = [
  {
    name: "Sun",
    value: parseInt("0000001", 2),
    defaultChecked: false,
  },
  {
    name: "Mon",
    value: parseInt("0000010", 2),
    defaultChecked: true,
  },
  {
    name: "Tue",
    value: parseInt("0000100", 2),
    defaultChecked: true,
  },
  {
    name: "Wed",
    value: parseInt("0001000", 2),
    defaultChecked: true,
  },
  {
    name: "Thu",
    value: parseInt("0010000", 2),
    defaultChecked: true,
  },
  {
    name: "Fri",
    value: parseInt("0100000", 2),
    defaultChecked: true,
  },
  {
    name: "Sat",
    value: parseInt("1000000", 2),
    defaultChecked: false,
  },
]

export default function SetDatetime({
  schedule,
  setSchedule,
  isSchedule,
  setIsSchedule,
  prev,
  done,
  isLoading,
  isEdit = false,
}: {
  schedule: any | null
  setSchedule: (datetime: any) => void
  isSchedule: boolean
  setIsSchedule: (isSchedule: boolean) => void
  prev?: () => void
  done?: () => void
  isLoading?: boolean
  isEdit?: boolean
}) {
  const [defaultDate] = useState(new Date())
  const handleSetDatetime = useCallback(
    (values: any) => {
      if (isEdit) {
        setSchedule((prev: any) => ({ ...prev, ...values }))
      } else {
        setSchedule({ ...schedule, ...values })
      }
    },
    [isEdit, schedule, setSchedule]
  )

  useEffect(() => {
    console.log(schedule)
  }, [schedule])

  const usTimezone = useMemo(() => {
    return getTimezonesForCountry("US").map((timezone) => {
      return {
        ...timezone,
        selectName: `${timezone.name} (UTC${timezone.dstOffsetStr})`,
      }
    })
  }, [])

  const defaultTimezone = useMemo(() => {
    const timezoneName = schedule?.timezoneName ?? "America/Los_Angeles"
    return usTimezone.find((timezone) => timezone.name === timezoneName)
  }, [schedule?.timezoneName, usTimezone])

  useEffect(() => {
    if (
      (schedule && !Object.hasOwn(schedule, "timezoneArray")) ||
      schedule?.timezoneArray === null
    ) {
      handleSetDatetime({
        timezoneArray: defaultTimezone,
      })
    }
  }, [defaultTimezone, schedule, schedule?.timezoneArray])

  const startDateParseSelectTimezone = useMemo(() => {
    const timezoneOffset =
      schedule?.timezoneArray?.dstOffset ?? defaultTimezone?.dstOffset

    return dateFormatYmd(
      calcDateByTimezone({ date: defaultDate, offset: timezoneOffset })
    )
  }, [
    schedule?.timezoneArray?.dstOffset,
    defaultTimezone?.dstOffset,
    defaultDate,
  ])

  const changeWeeks = (val: number) => {
    if (schedule.weeks & val) {
      handleSetDatetime({
        weeks: schedule.weeks - val,
      })
    } else {
      handleSetDatetime({
        weeks: schedule.weeks + val,
      })
    }
  }

  return (
    <>
      {schedule && (
        <div className={"date-time"}>
          <div className={"schedule-toggle"}>
            <div>
              <Icon
                path={getAssetPath("calendar_false.svg")}
                color={colors.schems.dark.onDisabled}
              />
              <span>Enable scheduled sending</span>
            </div>
            <div>
              <Toggle
                checked={isSchedule || false}
                onChange={(e) => {
                  if (!e.target.checked) {
                    handleSetDatetime({
                      startDate: "",
                      endDate: "",
                    })
                  }
                  setIsSchedule(e.target.checked)
                }}
                id={"scheduleToggle"}
              />
            </div>
          </div>
          {isSchedule && (
            <div className={"schedule-date"}>
              <div>
                <label>Start date</label>
                <input
                  type="date"
                  min={startDateParseSelectTimezone}
                  value={schedule?.startDate}
                  onChange={(e) => {
                    if (
                      e.target.value !== "" &&
                      schedule?.endDate !== "" &&
                      new Date(e.target.value) > new Date(schedule?.endDate)
                    ) {
                      handleSetDatetime({
                        startDate: e.target.value,
                        endDate: e.target.value,
                      })
                    } else {
                      handleSetDatetime({
                        startDate: e.target.value,
                      })
                    }
                  }}
                  data-placeholder={"Now"}
                />
              </div>
              <div>
                <label>End date</label>
                <input
                  type="date"
                  min={schedule?.startDate || startDateParseSelectTimezone}
                  value={schedule?.endDate}
                  onChange={(e) => {
                    handleSetDatetime({
                      endDate: e.target.value,
                    })
                  }}
                  data-placeholder={"No end date"}
                />
              </div>
            </div>
          )}
          <Information>
            Messages will be sent during your selected time range.
          </Information>
          <div className={"schedule-time"}>
            <div>
              <label>From</label>
              <input
                type="time"
                value={schedule?.startTime}
                onChange={(e) =>
                  handleSetDatetime({
                    startTime: e.target.value,
                  })
                }
              />
            </div>
            <div>
              <label>To</label>
              <input
                type="time"
                value={schedule?.endTime}
                onChange={(e) =>
                  handleSetDatetime({
                    endTime: e.target.value,
                  })
                }
              />
            </div>
          </div>
          <div className={"timezone"}>
            <label>Time Zone</label>
            <div key={defaultTimezone?.selectName}>
              <SelectBox
                data={usTimezone.map((timezone) => timezone.selectName)}
                defaultValue={defaultTimezone?.selectName}
                action={(name) => {
                  handleSetDatetime({
                    timezoneArray: usTimezone.find(
                      (timezone) => timezone.selectName === name
                    ),
                  })
                }}
                isFull={true}
                position={"relative"}
              />
            </div>
          </div>
          <div className={"schedule-weeks"}>
            <label>Days</label>
            <div key={schedule?.weeks}>
              {weeks.map((week) => {
                return (
                  <div key={week.value}>
                    <Checkbox
                      value={week.value}
                      checked={schedule && schedule?.weeks & week.value}
                      onClick={() => changeWeeks(week.value)}
                      readOnly
                    >
                      {week.name}
                    </Checkbox>
                  </div>
                )
              })}
            </div>
          </div>
          {prev && done && (
            <div className={"buttons"}>
              <div></div>
              <div>
                <Button style={"text"} onClick={prev}>
                  Previous
                </Button>
                <Button
                  onClick={done}
                  disabled={
                    !(
                      schedule &&
                      ((isSchedule &&
                        schedule?.startDate?.length > 0 &&
                        schedule?.endDate?.length > 0) ||
                        !isSchedule) &&
                      schedule.startTime &&
                      schedule.endTime &&
                      schedule?.timezoneArray &&
                      schedule.weeks > 0
                    ) || isLoading
                  }
                  isLoading={isLoading}
                >
                  Done
                </Button>
              </div>
            </div>
          )}
        </div>
      )}

      <style jsx>{`
        .date-time {
          display: flex;
          flex-direction: column;
          gap: ${schemes.spacing["2xl"]};
          > div {
            &.schedule-toggle {
              display: flex;
              justify-content: space-between;
              align-items: center;
              > div {
                display: flex;
                align-items: center;
                gap: ${schemes.spacing.md};
                > span {
                  font-size: ${fonts.body.large.fontSize};
                  font-weight: ${fonts.body.large.fontWeight};
                }
              }
            }

            &.schedule-date,
            &.schedule-time {
              display: grid;
              grid-gap: ${schemes.spacing.md};
              grid-template-columns: repeat(2, 1fr);

              &.schedule-date {
                border-radius: ${schemes.radius.sm};
                border: 1px solid ${colors.border.dark.default};
                padding: ${schemes.spacing["2xl"]};
              }
              > div {
                > label {
                  display: block;
                  margin-bottom: ${schemes.spacing.sm};
                  font-size: ${fonts.body.label.fontSize};
                  font-weight: ${fonts.body.label.fontWeight};
                  line-height: ${fonts.body.label.lineHeight};

                  & + input[type="date"],
                  & + input[type="time"] {
                    position: relative;
                    width: 100%;
                    height: 2.5rem;
                    padding: 0 ${schemes.spacing["2xl"]};
                    border-radius: ${schemes.radius.sm};
                    border: 1px solid ${colors.border.dark.default};
                    background: ${colors.schems.dark.secondary};

                    color: ${colors.schems.dark.onBase};
                    font-size: ${fonts.body.body.fontSize};
                    font-weight: ${fonts.body.body.fontWeight};
                    line-height: ${fonts.body.body.lineHeight};

                    &:focus-visible {
                      outline: none;
                      border: 1px solid ${colors.border.dark.primary};
                    }

                    &::-webkit-calendar-picker-indicator {
                      position: absolute;
                      right: 0;
                      top: 0;
                      width: 100%;
                      height: 100%;
                      background: none;
                    }
                    &::before {
                      content: "";
                      position: absolute;
                      right: ${schemes.spacing["2xl"]};
                      top: 50%;
                      transform: translateY(-50%);
                      width: 1.5rem;
                      height: 1.5rem;
                      mask: url("/images/assets/line_arrow_down.svg") no-repeat
                        center;
                      mask-size: cover;
                      -webkit-mask: url("/images/assets/line_arrow_down.svg")
                        no-repeat center;
                      -webkit-mask-size: cover;
                      background-color: ${colors.schems.dark.onBase};
                    }
                  }
                }
              }
            }

            > label {
              display: block;
              margin-bottom: ${schemes.spacing.sm};
              font-size: ${fonts.body.label.fontSize};
              font-weight: ${fonts.body.label.fontWeight};
              line-height: ${fonts.body.label.lineHeight};
              & + div {
                display: flex;
                align-items: center;
                justify-content: start;
                gap: ${schemes.spacing.md};
                label {
                  flex: none;
                }
              }
            }
          }
          > .buttons {
            display: flex;
            justify-content: space-between;
            > div {
              display: flex;
              gap: ${schemes.spacing.md};
              &:first-child {
                justify-content: start;
              }
              &:last-child {
                justify-content: end;
              }
            }
          }
        }
      `}</style>
    </>
  )
}
