import Input from "@/components/elements/Input3"
import { getAssetPath, randNumberFromStr } from "@/utils/util"
import { colors, schemes } from "@/utils/theme/style"
import Button from "@/components/elements/Button"
import React, { useEffect, useMemo, useState } from "react"
import { getTiktokProducts, refreshTiktokProducts } from "@/service/workspace"
import useWorkspace from "@/store/useWorkspace"
import Loading from "@/components/common/Loading"
import LabelButton from "@/components/elements/LabelButton"
import { ColorType } from "@/common/types/colorType"

export default function SelectProduct({
  products,
  setProduct,
  removeProduct,
  prev,
  next,
  nextDisabled,
}: {
  products: any[]
  setProduct: (product: any) => void
  removeProduct: (product: any) => void
  prev?: () => void
  next?: () => void
  nextDisabled?: boolean
}) {
  const { currentWorkspace } = useWorkspace()
  const workspaceId = currentWorkspace?.id
  const [productList, setProductList] = useState<any[]>([])
  const [search, setSearch] = useState<string>("")
  const [isProductLoading, setIsProductLoading] = useState(false)
  const colorsArr = [
    "red",
    "orange",
    "yellow",
    "green",
    "cyan",
    "blue",
    "indigo",
    "purple",
    "pink",
  ]

  const filterProducts = useMemo(() => {
    return search.length
      ? productList.filter(
          (product) =>
            product.name.toLowerCase().indexOf(search.toLowerCase()) !== -1
        )
      : productList
  }, [search, productList])

  const getProducts = () => {
    if (workspaceId) {
      setIsProductLoading(true)
      getTiktokProducts({ workspaceId })
        .then((res) => {
          setProductList(res.products)
        })
        .catch((err) => {
          console.log(err.message)
        })
        .finally(() => setIsProductLoading(false))
    }
  }

  const refreshProducts = () => {
    if (workspaceId) {
      setIsProductLoading(true)
      refreshTiktokProducts({ workspaceId })
        .then((res) => {
          setProductList(res.products)
        })
        .catch((err) => {
          console.log(err.message)
        })
        .finally(() => setIsProductLoading(false))
    }
  }

  const productSelectHandler = (checked: boolean, item: any) => {
    if (checked) setProduct(item)
    else removeProduct(item)
  }

  useEffect(() => {
    getProducts()
  }, [])

  return (
    <>
      <div className={"select-product"}>
        <Input
          type={"text"}
          value={search}
          placeholder={"Search Product"}
          beforeIcon={{
            path: getAssetPath("search.svg"),
            color: colors.schems.dark.disabled,
          }}
          clear={setSearch}
          onChange={(e) => setSearch(e.target.value)}
        />
        <div className={"product-list"}>
          {!isProductLoading ? (
            filterProducts?.length > 0 ? (
              filterProducts?.map((item, index) => {
                if (products.find((product) => product.name === item.name)) {
                  return (
                    <div
                      key={index}
                      onClick={() => productSelectHandler(false, item)}
                    >
                      <LabelButton
                        color={
                          colorsArr[
                            randNumberFromStr(item.name) % colorsArr.length
                          ] as ColorType
                        }
                      >
                        {item.name}
                      </LabelButton>
                    </div>
                  )
                } else {
                  return (
                    <div
                      key={index}
                      onClick={() => productSelectHandler(true, item)}
                    >
                      <LabelButton>{item.name}</LabelButton>
                    </div>
                  )
                }
              })
            ) : (
              <div className={"no-item"}>No products found.</div>
            )
          ) : (
            <Loading />
          )}
        </div>
        <div className={"pick-list"}>
          {products?.map((item, index) => {
            return (
              <div
                key={index}
                onClick={() => productSelectHandler(false, item)}
              >
                <LabelButton
                  color={
                    colorsArr[
                      randNumberFromStr(item.name) % colorsArr.length
                    ] as ColorType
                  }
                >
                  {item.name}
                </LabelButton>
              </div>
            )
          })}
        </div>
        {prev && next && (
          <div className={"buttons"}>
            <div>
              <Button
                style={"secondary"}
                icon={{ path: getAssetPath("upload.svg") }}
                onClick={refreshProducts}
                isLoading={isProductLoading}
                disabled={isProductLoading}
              >
                Refresh
              </Button>
            </div>
            <div>
              <Button style={"text"} onClick={prev}>
                Previous
              </Button>
              <Button onClick={next} disabled={nextDisabled}>
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
      <style jsx>{`
        .select-product {
          display: flex;
          flex-direction: column;
          gap: ${schemes.spacing.md};
          > .product-list {
            overflow-x: hidden;
            overflow-y: scroll;
            -ms-overflow-style: none;
            &::-webkit-scrollbar {
              display: none;
            }
            height: 16.5rem;
            padding: ${schemes.spacing.md} 0;
            border-radius: ${schemes.radius.sm};
            border: 1px solid ${colors.border.dark.bgLighten};
            background: ${colors.bg.dark.surfaceBright};
            box-shadow:
              0 1px 3px 1px rgba(0, 0, 0, 0.15),
              0 1px 2px 0 rgba(0, 0, 0, 0.3);
            > div {
              height: 2.5rem;
              padding: 0 ${schemes.spacing.xl};
              display: flex;
              align-items: center;
              &.no-item {
                position: relative;
                top: 50%;
                transform: translateY(-50%);
                justify-content: center;
              }
            }
          }
          > .pick-list {
            display: flex;
            flex-direction: column;
            gap: ${schemes.spacing.md};
          }
          > .buttons {
            display: flex;
            justify-content: space-between;
            > div {
              display: flex;
              gap: ${schemes.spacing.md};
              &:first-child {
                justify-content: start;
              }
              &:last-child {
                justify-content: end;
              }
            }
          }
        }
      `}</style>
    </>
  )
}
