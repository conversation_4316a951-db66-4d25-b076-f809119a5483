import { getAssetPath } from "@/utils/util"
import LabelTag from "@/components/elements/LabelTag"
import Button from "@/components/elements/Button"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { CampaignPurposeType } from "@/common/enums/campaignTypes"
import Icon from "@/components/elements/Icon"

const purposes = [
  {
    title: "First outreach",
    description: "Send a first message or reminder to many creators",
    image: getAssetPath("campaign01.png"),
    disabled: false,
    type: CampaignPurposeType.firstContact,
  },
  {
    title: "Content reminder",
    description: "Nudge sampled creators to film & upload",
    image: getAssetPath("campaign02.png"),
    disabled: true,
    type: CampaignPurposeType.contentRemind,
  },
  {
    title: "Request more content",
    description: "Ask top creators for more content",
    image: getAssetPath("campaign03.png"),
    disabled: true,
    type: CampaignPurposeType.reProductionRequest,
  },
]

export default function Purpose({
  purpose,
  setPurpose,
  setPurposeName,
  next,
  nextDisabled,
}: {
  purpose: string | null
  setPurpose: (purpose: string | null) => void
  setPurposeName: (name: string | null) => void
  next: () => void
  nextDisabled: boolean
}) {
  return (
    <>
      <div className={"campaign-purpose"}>
        <div>
          {purposes.map((item: any, index: number) => (
            <label key={index}>
              <input
                type="radio"
                name={"purposeCheck"}
                checked={item.type === purpose}
                disabled={item.disabled}
                onChange={(e) => {
                  if (e.target.checked) {
                    setPurpose(item.type)
                    setPurposeName(item.title)
                  }
                }}
              />
              <div>
                <div>
                  <div className={"false"}>
                    <Icon
                      path={getAssetPath("radio_false.svg")}
                      color={
                        item.disabled
                          ? colors.schems.dark.disabled
                          : colors.schems.dark.onBase
                      }
                    />
                  </div>
                  <div className={"true"}>
                    <Icon
                      path={getAssetPath("radio_true.svg")}
                      color={colors.schems.dark.primary}
                    />
                  </div>
                  <h3 className={item.disabled ? "disabled" : ""}>
                    {item.title}
                  </h3>
                </div>
                <div>{item.description}</div>
                <div>
                  {item.disabled && (
                    <div style={{ position: "relative" }}>
                      <LabelTag color={"gray"}>Coming Soon</LabelTag>
                    </div>
                  )}
                </div>
              </div>
            </label>
          ))}
        </div>
        <div className={"buttons"}>
          <div></div>
          <div>
            <Button onClick={next} disabled={nextDisabled}>
              Next
            </Button>
          </div>
        </div>
      </div>
      <style jsx>{`
        .campaign-purpose {
          display: flex;
          flex-direction: column;
          gap: ${schemes.spacing["2xl"]};
          > div:first-child {
            display: grid;
            grid-gap: ${schemes.spacing.md};
            grid-template-columns: repeat(3, 1fr);
            > label {
              > div {
                height: 100%;
                padding: ${schemes.spacing.md};
                border-radius: ${schemes.spacing.sm};
                border: 1px solid ${colors.border.dark.bgLighten};
                background: ${colors.bg.dark.surfaceBright};
                > div:first-of-type {
                  display: flex;
                  align-items: center;
                  gap: ${schemes.spacing.sm};
                  > h3 {
                    width: 100%;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                    font-size: ${fonts.body.body.fontSize};
                    font-weight: ${fonts.body.body.fontWeight};
                    line-height: ${fonts.body.body.lineHeight};
                    margin: 0;
                  }
                }
                > div:nth-of-type(2) {
                  margin-top: ${schemes.spacing.sm};
                  margin-bottom: ${schemes.spacing["2xl"]};

                  color: ${colors.schems.dark.onDisabled};
                  font-size: ${fonts.body.label.fontSize};
                  font-weight: ${fonts.body.label.fontWeight};
                  line-height: ${fonts.body.label.lineHeight};
                }
                > div:last-of-type {
                  position: relative;
                  width: 100%;
                  height: 7.5rem;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  border-radius: ${schemes.radius.sm};
                  background: ${colors.schems.dark.base};
                  &::before {
                    content: "";
                    position: absolute;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    top: 0;
                    background-image: url(${getAssetPath("campaign01.png")});
                    background-repeat: no-repeat;
                    background-size: 100px 100px;
                    background-position: center;
                  }
                }
              }
              &:nth-of-type(2) {
                > div > div:last-of-type::before {
                  background-image: url(${getAssetPath("campaign02.png")});
                  opacity: 0.5;
                }
              }
              &:nth-of-type(3) {
                > div > div:last-of-type::before {
                  background-image: url(${getAssetPath("campaign03.png")});
                  background-size: 100px 80px;
                  opacity: 0.5;
                }
              }
            }
          }
          > .buttons {
            display: flex;
            justify-content: space-between;
            > div {
              display: flex;
              gap: ${schemes.spacing.md};
              &:first-child {
                justify-content: start;
              }
              &:last-child {
                justify-content: end;
              }
            }
          }
        }
        input[type="radio"] {
          display: none;
          & + div {
            .false {
              display: block;
              font-size: 0;
            }
            .true {
              display: none;
              font-size: 0;
            }
          }
          &:checked {
            & + div {
              border: 1px solid ${colors.border.dark.primary} !important;
              .false {
                display: none;
              }
              .true {
                display: block;
              }
            }
          }
        }
        .disabled {
          color: ${colors.schems.dark.disabled};
        }
      `}</style>
    </>
  )
}
