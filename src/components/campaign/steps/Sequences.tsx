import React, { useCallback, useState } from "react"
import SelectBox from "@/components/elements/SelectBox"
import <PERSON>ton from "@/components/elements/Button"
import { getAssetPath } from "@/utils/util"
import { colors, fonts, schemes } from "@/utils/theme/style"
import {
  CampaignMessageStatus,
  CampaignMessageType,
} from "@/common/enums/campaignTypes"
import IconButton from "@/components/elements/IconButton"
import Icon from "@/components/elements/Icon"

export default function Sequences({
  sequences,
  addSequence,
  setSequences,
  removeSequences,
  isCollabs,
  prev,
  next,
  nextDisabled,
}: {
  sequences: { type: string; days: number; message: string }[]
  addSequence: () => void
  setSequences: (
    messages: { type: string; days: number; message: string }[]
  ) => void
  removeSequences: (index: number) => void
  isCollabs: boolean
  prev?: () => void
  next?: () => void
  nextDisabled?: boolean
}) {
  const [sequenceTypes] = useState([
    {
      type: CampaignMessageType.message,
      label: "Affiliate DM",
    },
  ])

  const setSequenceHandler = (
    index: number,
    column: string,
    value: string | number
  ) => {
    setSequences(
      sequences.map((data: any, i: number) => {
        if (index === i) {
          data[column] = value
        }
        return data
      })
    )
  }

  const typeLabel = useCallback(
    (type: string) => {
      const findType = sequenceTypes.find((seq) => seq.type === type)
      if (findType?.label) {
        return findType?.label
      }
      return type
    },
    [sequenceTypes]
  )

  const checkSequenceStatus = (sequence: any) => {
    return (
      !Object.hasOwn(sequence, "status") ||
      (Object.hasOwn(sequence, "status") &&
        sequence?.status !== CampaignMessageStatus.sent)
    )
  }

  return (
    <>
      <div className={"sequences"}>
        <div className={"step"}>
          <Icon
            path={getAssetPath("flag_true.svg")}
            color={colors.schems.dark.onDisabled}
          />
          <span>Start campaign</span>
        </div>
        <div className={"next-arrow"}>
          <Icon
            path={getAssetPath("arrow-down-big.svg")}
            width={"16px"}
            height={"25px"}
            color={colors.schems.dark.onDisabled}
          />
        </div>
        {isCollabs && (
          <div className={"step"}>
            <Icon
              path={getAssetPath("target.svg")}
              color={colors.schems.dark.onDisabled}
            />
            <span>Invite target collaboration</span>
          </div>
        )}
        <div className={"messages"}>
          {sequences.map((sequence, index) => (
            <div key={index}>
              <div>
                <div>
                  <Icon
                    path={getAssetPath("send_true.svg")}
                    color={colors.schems.dark.onDisabled}
                  />
                  <span>Send</span>
                  <div key={sequence.type}>
                    <SelectBox
                      data={sequenceTypes.map((seq) => seq.label)}
                      width={"7.5rem"}
                      defaultValue={typeLabel(sequence.type)}
                      action={(val) => {
                        setSequenceHandler(index, "type", typeLabel(val))
                      }}
                      disabled={!checkSequenceStatus(sequence)}
                    />
                  </div>
                  <span>in</span>
                  <div key={sequence.days}>
                    <SelectBox
                      data={[0, 1, 2, 3]}
                      width={"7.5rem"}
                      defaultValue={sequence.days}
                      action={(val) => {
                        setSequenceHandler(index, "days", val)
                      }}
                      disabled={!checkSequenceStatus(sequence)}
                    />
                  </div>
                  <span>Days</span>
                </div>
                <div>
                  {checkSequenceStatus(sequence) && (
                    <IconButton
                      path={getAssetPath("delete_forever_false.svg")}
                      onClick={() => removeSequences(index)}
                    />
                  )}
                </div>
              </div>
              <hr />
              <div key={sequence.message}>
                <textarea
                  placeholder={"Start typing here"}
                  defaultValue={sequence.message}
                  onBlur={(e) =>
                    setSequenceHandler(index, "message", e.target.value)
                  }
                  disabled={!checkSequenceStatus(sequence)}
                  style={
                    !checkSequenceStatus(sequence)
                      ? {
                          cursor: "not-allowed",
                        }
                      : {}
                  }
                ></textarea>
              </div>
            </div>
          ))}
        </div>
        {sequences.length < 3 && (
          <div className={"add-message"}>
            <Button
              style={"secondary"}
              icon={{ path: getAssetPath("add.svg") }}
              onClick={addSequence}
            >
              Add a step
            </Button>
          </div>
        )}
        {prev && next && (
          <div className={"buttons"}>
            <div></div>
            <div>
              <Button style={"text"} onClick={prev}>
                Previous
              </Button>
              <Button onClick={next} disabled={nextDisabled}>
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
      <style jsx>{`
        .sequences {
          display: flex;
          flex-direction: column;
          gap: ${schemes.spacing["2xl"]};
          > .step {
            display: flex;
            gap: ${schemes.spacing.md};
            align-items: center;
            padding: ${schemes.spacing["2xl"]} ${schemes.spacing["3xl"]};
            border-radius: ${schemes.radius.sm};
            background: ${colors.bg.dark.surfaceBright};
          }
          > .next-arrow {
            display: flex;
            align-items: center;
            justify-content: center;
          }
          > .messages {
            display: flex;
            flex-direction: column;
            gap: ${schemes.spacing["2xl"]};
            > div {
              display: flex;
              flex-direction: column;
              gap: ${schemes.spacing["2xl"]};
              padding: ${schemes.spacing["3xl"]};
              border-radius: ${schemes.radius.sm};
              background: ${colors.bg.dark.surfaceBright};
              > * {
                width: 100%;
              }
              > span {
                font-size: ${fonts.body.large.fontSize};
                font-weight: ${fonts.body.large.fontWeight};
                line-height: ${fonts.body.large.lineHeight};
              }
              > div:first-of-type {
                display: flex;
                justify-content: space-between;
                align-items: center;
                > div {
                  display: flex;
                  gap: ${schemes.spacing.md};
                  align-items: center;
                  & > div:nth-of-type(2) {
                    width: 7.5rem;
                  }
                }
              }
              > hr {
                height: 0;
                margin: 0;
                border: none;
                border-top: 1px solid ${colors.border.dark.bgLighten};
              }
              > div:last-of-type {
                > textarea {
                  width: 100%;
                  background: ${colors.interactive.dark.hover};
                  border-radius: ${schemes.radius.sm};
                  border: 1px solid ${colors.border.dark.bgLighten};
                  padding: ${schemes.spacing.xl};
                  height: 15rem;

                  color: ${colors.schems.dark.onBase};
                  font-family: ${fonts.heading.fontFamily};
                  font-size: ${fonts.body.large.fontSize};
                  font-weight: ${fonts.body.large.fontWeight};
                  line-height: ${fonts.body.large.lineHeight};
                  resize: none;

                  &:focus-visible {
                    outline: none;
                    border-color: ${colors.border.dark.primary};
                  }
                }
              }
            }
          }
          > .add-message {
            display: flex;
            justify-content: center;
          }
          > .buttons {
            display: flex;
            justify-content: space-between;
            > div {
              display: flex;
              gap: ${schemes.spacing.md};
              &:first-child {
                justify-content: start;
              }
              &:last-child {
                justify-content: end;
              }
            }
          }
        }
      `}</style>
    </>
  )
}
