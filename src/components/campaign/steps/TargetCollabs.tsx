import Button from "@/components/elements/Button"
import React, { useEffect } from "react"
import { colors, fonts, schemes } from "@/utils/theme/style"
import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"
import Toggle from "@/components/elements/Toggle"
import Input from "@/components/elements/Input3"
import LabelBox from "@/components/layout/LabelBox"
import Radio from "@/components/elements/Radio"
import { dateFormatYmd } from "@/common/utils/date"
import { removeEmojis } from "@/common/utils/common"

export default function TargetCollabs({
  collabs,
  setCollabs,
  isCollabs,
  setIsCollabs,
  products,
  updateProduct,
  prev,
  next,
  nextDisabled,
  bgColor = colors.bg.dark.surfaceBright,
  allDisabled,
}: {
  collabs: any
  setCollabs: (collabs: any) => void
  isCollabs: boolean
  setIsCollabs: (isCollabs: boolean) => void
  products: any
  updateProduct: (products: any, index: number) => void
  prev?: () => void
  next?: () => void
  nextDisabled?: boolean
  bgColor?: string
  allDisabled?: boolean
}) {
  useEffect(() => {
    if (collabs === null || collabs?.hasFreeSample === null) {
      setCollabs({
        ...collabs,
        hasFreeSample: true,
        isSampleApprovalExempt: false,
      })
    }
  }, [collabs, setCollabs])

  useEffect(() => {
    console.log(collabs)
  }, [collabs])

  return (
    <>
      <div className={"content"}>
        <div>
          <div>
            <div>
              <Icon
                path={getAssetPath("target.svg")}
                color={colors.schems.dark.onDisabled}
              />
              <span>Invite target collaboration</span>
            </div>
            <div>
              <Toggle
                checked={isCollabs}
                onChange={(e) => setIsCollabs(e.target.checked)}
                id={"collabs"}
                disabled={allDisabled}
              />
            </div>
          </div>
          {isCollabs && (
            <>
              <hr />
              <div>
                <LabelBox
                  label={`Invitation name (${collabs?.name?.length || 0}/30)`}
                >
                  <Input
                    type={"text"}
                    placeholder={"Invitation name"}
                    value={collabs?.name || ""}
                    disabled={allDisabled}
                    clear={(val) => setCollabs({ name: val })}
                    maxLength={30}
                    onChange={(e) =>
                      setCollabs({ name: removeEmojis(e.target.value) })
                    }
                  />
                </LabelBox>
                <LabelBox label={"Valid until"}>
                  <input
                    type="date"
                    min={dateFormatYmd(new Date())}
                    value={collabs?.endTime || ""}
                    disabled={allDisabled}
                    onChange={(e) => {
                      setCollabs({ endTime: e.target.value })
                    }}
                    style={
                      collabs?.endTime
                        ? {}
                        : {
                            color: `${colors.schems.dark.onDisabled}`,
                          }
                    }
                  />
                </LabelBox>
                <LabelBox label={"Contact Email Address"}>
                  <Input
                    type={"email"}
                    placeholder={"Contact Email Address"}
                    value={collabs?.email || ""}
                    disabled={allDisabled}
                    clear={(val) => setCollabs({ email: val })}
                    onChange={(e) => {
                      setCollabs({ email: e.target.value })
                    }}
                  />
                </LabelBox>
                <LabelBox
                  label={`Message (${collabs?.message?.length || 0}/400)`}
                  labelFor={"target-message"}
                >
                  <textarea
                    name="target-message"
                    id="target-message"
                    placeholder={"Start typing here"}
                    maxLength={400}
                    value={collabs?.message || ""}
                    disabled={allDisabled}
                    onChange={(e) =>
                      setCollabs({ message: removeEmojis(e.target.value) })
                    }
                  ></textarea>
                </LabelBox>
                <LabelBox label={"Commission"}>
                  <div className={"products"}>
                    {products.map((product: any, index: number) => (
                      <div key={index}>
                        <div>
                          <small>Skin Care Kits</small>
                          <h5>
                            <span>{product.name}</span>
                          </h5>
                          <p>
                            <span>${product.price}</span>
                            <span>
                              {product?.commission &&
                              Number(product?.commission) > 0
                                ? `($${Math.round(product.price * (Number(product?.commission) / 100))})`
                                : "-"}
                            </span>
                          </p>
                        </div>
                        <div>
                          <Input
                            placeholder={"1 ~ 80"}
                            type="number"
                            min={1}
                            max={80}
                            value={product?.commission?.toString() || ""}
                            disabled={allDisabled}
                            onChange={(e) =>
                              updateProduct(
                                {
                                  ...product,
                                  commission: e.target.value,
                                },
                                index
                              )
                            }
                            onBlur={(e) => {
                              if (Number(e.target.value) <= 0) {
                                updateProduct(
                                  { ...product, commission: "0" },
                                  index
                                )
                              }
                              if (Number(e.target.value) > 80) {
                                updateProduct(
                                  { ...product, commission: "80" },
                                  index
                                )
                              }
                            }}
                          />
                          <span>%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </LabelBox>
                <div className={"free-sample"}>
                  <label>
                    <Toggle
                      checked={collabs?.hasFreeSample || false}
                      onChange={(e) =>
                        setCollabs({ hasFreeSample: e.target.checked })
                      }
                      id={"free-sample"}
                      disabled={allDisabled}
                    />
                    <span>Offer free sample</span>
                  </label>
                </div>
                {collabs?.hasFreeSample && (
                  <LabelBox label={"Free sample request"}>
                    <div className={"free-sample-request"}>
                      <div>
                        <Radio
                          name={"free-sample-request"}
                          checked={collabs?.isSampleApprovalExempt || false}
                          onChange={(e) => {
                            if (e.target.checked)
                              setCollabs({ isSampleApprovalExempt: true })
                          }}
                          disabled={allDisabled}
                        >
                          Auto approve requests
                        </Radio>
                      </div>
                      <div>
                        <Radio
                          name={"free-sample-request"}
                          checked={!collabs?.isSampleApprovalExempt || false}
                          onChange={(e) => {
                            if (e.target.checked)
                              setCollabs({ isSampleApprovalExempt: false })
                          }}
                          disabled={allDisabled}
                        >
                          Manually review requests
                        </Radio>
                      </div>
                    </div>
                  </LabelBox>
                )}
              </div>
            </>
          )}
        </div>
        {prev && next && (
          <div className={"buttons"}>
            <div></div>
            <div>
              <Button style={"text"} onClick={prev}>
                Previous
              </Button>
              <Button onClick={next} disabled={nextDisabled}>
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
      <style jsx>{`
        .content {
          display: flex;
          flex-direction: column;
          gap: ${schemes.spacing["2xl"]};
          > div:first-of-type {
            display: flex;
            flex-direction: column;
            gap: ${schemes.spacing["2xl"]};
            padding: ${schemes.spacing["3xl"]};
            border-radius: ${schemes.radius.sm};
            background: ${bgColor};
            > div:first-of-type {
              display: flex;
              justify-content: space-between;
              align-items: center;
              > div {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: ${schemes.spacing.md};
              }
            }
            > hr {
              height: 0;
              margin: 0;
              border: none;
              border-top: 1px solid ${colors.border.dark.bgLighten};
            }
            > hr + div {
              display: flex;
              flex-direction: column;
              gap: ${schemes.spacing["2xl"]};
            }
            input[type="date"] {
              position: relative;
              width: 100%;
              height: 2.5rem;
              padding: 0 ${schemes.spacing["2xl"]};
              border-radius: ${schemes.radius.sm};
              border: 1px solid ${colors.border.dark.default};
              background: ${colors.schems.dark.secondary};

              color: ${colors.schems.dark.onBase};
              font-size: ${fonts.body.body.fontSize};
              font-weight: ${fonts.body.body.fontWeight};
              line-height: ${fonts.body.body.lineHeight};

              &:disabled {
                background: ${colors.schems.dark.disabled};
                color: ${colors.schems.dark.onDisabled};
              }

              &:focus-visible {
                outline: none;
                border: 1px solid ${colors.border.dark.primary};
              }

              &::-webkit-calendar-picker-indicator {
                position: absolute;
                right: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background: none;
              }
              &::before {
                content: "";
                position: absolute;
                right: ${schemes.spacing["2xl"]};
                top: 50%;
                transform: translateY(-50%);
                width: 1.5rem;
                height: 1.5rem;
                mask: url("/images/assets/line_arrow_down.svg") no-repeat center;
                mask-size: cover;
                -webkit-mask: url("/images/assets/line_arrow_down.svg")
                  no-repeat center;
                -webkit-mask-size: cover;
                background-color: ${colors.schems.dark.onBase};
              }
            }
            textarea {
              width: 100%;
              background: ${colors.interactive.dark.hover};
              border-radius: ${schemes.radius.sm};
              border: 1px solid ${colors.border.dark.bgLighten};
              padding: ${schemes.spacing.xl};
              height: 15rem;

              color: ${colors.schems.dark.onBase};
              font-family: ${fonts.heading.fontFamily};
              font-size: ${fonts.body.large.fontSize};
              font-weight: ${fonts.body.large.fontWeight};
              line-height: ${fonts.body.large.lineHeight};
              resize: none;

              &:focus-visible {
                outline: none;
                border-color: ${colors.border.dark.primary};
              }
              &:disabled {
                color: ${colors.schems.dark.onDisabled};
                background: ${colors.schems.dark.disabled};
              }
            }
            .products {
              display: flex;
              flex-direction: column;
              gap: ${schemes.spacing.sm};
              > div {
                display: flex;
                justify-content: space-between;
                align-items: center;
                gap: ${schemes.spacing["2xl"]};
                border-radius: ${schemes.radius.sm};
                border: 1px solid ${colors.border.dark.bgLighten};
                padding: ${schemes.spacing["2xl"]};

                > div:first-of-type {
                  display: flex;
                  flex-direction: column;
                  align-items: start;
                  flex-shrink: 1;
                  width: 24.5rem;
                  > small {
                    color: ${colors.schems.dark.onDisabled};
                    font-size: ${fonts.body.label.fontSize};
                    font-weight: ${fonts.body.label.fontWeight};
                    line-height: ${fonts.body.label.lineHeight};
                  }
                  > h5 {
                    margin: 0;
                    font-size: ${fonts.body.bodyBold.fontSize};
                    font-weight: ${fonts.body.bodyBold.fontWeight};
                    line-height: ${fonts.body.bodyBold.lineHeight};
                    width: 100%;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                  }
                  > p {
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: ${schemes.spacing.sm};
                    > span {
                      font-size: ${fonts.body.body.fontSize};
                      font-weight: ${fonts.body.body.fontWeight};
                      line-height: ${fonts.body.body.lineHeight};
                    }
                    > span:last-of-type {
                      color: ${colors.schems.dark.onDisabled};
                    }
                  }
                }
                > div:last-of-type {
                  position: relative;
                  flex-shrink: 0;
                  width: 5rem;
                  input {
                    padding-right: ${schemes.spacing["3xl"]};
                    padding-left: ${schemes.spacing.sm};
                    text-align: right;
                  }
                  > span {
                    position: absolute;
                    display: block;
                    top: 50%;
                    transform: translateY(-50%);
                    right: ${schemes.spacing.xl};
                  }
                }
              }
            }
            .free-sample > label {
              display: flex;
              align-items: center;
              gap: ${schemes.spacing.md};
              > span {
                font-size: ${fonts.body.body.fontSize};
                font-weight: ${fonts.body.body.fontWeight};
                line-height: ${fonts.body.body.lineHeight};
              }
            }
            .free-sample-request {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: ${schemes.spacing.md};
              > div {
                border-radius: ${schemes.radius.sm};
                border: 1px solid ${colors.border.dark.bgLighten};
                padding: 0 ${schemes.spacing.md};
                height: 2.5rem;
                display: flex;
                align-items: center;
              }
            }
          }
          > .buttons {
            display: flex;
            justify-content: space-between;
            > div {
              display: flex;
              gap: ${schemes.spacing.md};
              &:first-child {
                justify-content: start;
              }
              &:last-child {
                justify-content: end;
              }
            }
          }
        }
      `}</style>
    </>
  )
}
