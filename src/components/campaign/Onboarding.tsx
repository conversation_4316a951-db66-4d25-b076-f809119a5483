import LabelButton from "@/components/elements/LabelButton"
import Button from "../elements/Button"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { getAssetPath } from "@/utils/util"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import {
  getTikTokAccessTokenWithCode,
  getTiktokAuthUrl,
} from "@/service/tiktokShop"
import useUserInfo from "@/store/useUserInfo"
import { useCallback, useEffect, useMemo, useState } from "react"
import { isObjectNotEmpty } from "@/common/utils/common"
import useWorkspace from "@/store/useWorkspace"
import { notifyError } from "@/components/modal/Toasts"

export default function CampaignOnboarding() {
  const { userInfo, setUserInfo } = useUserInfo()
  const { currentWorkspace } = useWorkspace()
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const code = searchParams.get("code")
  const [isLoading, setIsLoading] = useState(false)

  const workspaceId = useMemo(() => currentWorkspace?.id, [currentWorkspace])

  const tiktokAuthInfo = useMemo(() => {
    return userInfo?.user_tiktok_info || null
  }, [userInfo?.user_tiktok_info])

  const isLoadUserTiktokInfo = useMemo(() => {
    return (
      tiktokAuthInfo === null ||
      !isObjectNotEmpty(tiktokAuthInfo) ||
      (tiktokAuthInfo &&
        tiktokAuthInfo.version !== process.env.TTS_VERIFY_VERSION)
    )
  }, [tiktokAuthInfo])

  const processTiktokAuth = async () => {
    const location = `${window.location.origin}${pathname}`
    setIsLoading(true)
    const tiktokAuthUrl = await getTiktokAuthUrl(workspaceId!, location)
    router.push(tiktokAuthUrl)
  }

  const processTiktokShopAuthWithCode = useCallback(
    async (workspaceId: number, code: string) => {
      getTikTokAccessTokenWithCode(workspaceId, code)
        .then((res: any) => {
          const data = res.data
          if (res.message === "success" && userInfo) {
            setUserInfo({
              ...userInfo,
              user_tiktok_info: {
                shop_id: data.shop_id as string,
                seller_name: data.seller_name as string,
                version: data.version as string,
              },
            })
            router.replace("/campaign")
          }
        })
        .catch((error) => {
          if (error.response.data.message === "Already Registered") {
            notifyError("Already Registered")
          }
        })
        .finally(() => setIsLoading(false))
    },
    [router, setUserInfo, userInfo]
  )

  useEffect(() => {
    if (workspaceId && code && isLoadUserTiktokInfo) {
      setIsLoading(true)
      processTiktokShopAuthWithCode(workspaceId, code).then()
    }
  }, [code])

  return (
    <>
      <div className="layout">
        <div>
          <div className={"connect-tts"}>
            <h1>
              Get started in minutes,
              <br />
              Reach creators instantly.
            </h1>
            <div>
              <div>
                <div>
                  <div>
                    <h5>
                      <LabelButton size={"sm"}>0</LabelButton>
                      <span>Connect TikTok Shop</span>
                    </h5>
                    <p>
                      Securely connect your shop to sync products
                      <br />
                      and automate outreach.
                    </p>
                  </div>
                  <div>
                    <Button
                      onClick={processTiktokAuth}
                      isLoading={isLoading}
                      disabled={isLoading}
                    >
                      Connect
                    </Button>
                  </div>
                </div>
                <div>
                  <h5>
                    <LabelButton size={"sm"}>1</LabelButton>
                    <span>Select campaign goal</span>
                  </h5>
                </div>
                <div>
                  <h5>
                    <LabelButton size={"sm"}>2</LabelButton>
                    <span>Pick products</span>
                  </h5>
                </div>
                <div>
                  <h5>
                    <LabelButton size={"sm"}>3</LabelButton>
                    <span>Select creator list</span>
                  </h5>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <style jsx>{`
        .layout {
          padding: ${schemes.spacing["6xl"]} 0;
          width: 100%;
          height: 100vh;
          display: flex;
          justify-content: center;
          align-items: center;
          > div {
            padding: ${schemes.spacing["3xl"]};
            width: 40rem;
            > .connect-tts {
              display: flex;
              flex-direction: column;
              gap: ${schemes.spacing["3xl"]};
              align-items: center;
              > h1 {
                text-align: center;
                font-size: ${fonts.heading.h2.fontSize};
                font-weight: ${fonts.heading.h2.fontWeight};
                line-height: ${fonts.heading.h2.lineHeight};
                margin: 0;
              }
              > div {
                width: 100%;
                height: 12.8125rem;
                > div {
                  position: relative;
                  > div {
                    border-radius: ${schemes.radius.sm};
                    background: ${colors.bg.dark.surfaceBright};
                    > div:first-child {
                      display: flex;
                      flex-direction: column;
                      gap: ${schemes.spacing.sm};
                    }
                    h5 {
                      margin: 0;
                      display: flex;
                      align-items: center;
                      justify-content: start;
                      gap: ${schemes.spacing.md};
                      > span {
                        font-size: ${fonts.body.body.fontSize};
                        font-weight: ${fonts.body.body.fontWeight};
                        line-height: ${fonts.body.body.lineHeight};
                      }
                    }
                    p {
                      margin: 0;
                      color: ${colors.schems.dark.onDisabled};
                      font-size: ${fonts.body.large.fontSize};
                      font-weight: ${fonts.body.large.fontWeight};
                      line-height: ${fonts.body.large.lineHeight};
                    }
                    &:nth-child(1) {
                      position: relative;
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      padding: ${schemes.spacing.xl} ${schemes.spacing["2xl"]};
                      gap: ${schemes.spacing["2xl"]};
                      border: 1px solid ${colors.border.dark.primary};
                      box-shadow:
                        0 8px 12px 6px rgba(0, 0, 0, 0.15),
                        0 4px 4px 0 rgba(0, 0, 0, 0.3);
                      background:
                        url(${getAssetPath("connect-tts.png")}) no-repeat right
                          4.52313rem bottom -2.50281rem / 8.0653rem,
                        ${colors.bg.dark.surfaceBright};
                      z-index: 5;
                    }
                    &:nth-child(2),
                    &:nth-child(3),
                    &:nth-child(4) {
                      position: absolute;
                      left: 50%;
                      transform: translateX(-50%);
                    }
                    &:nth-child(2) {
                      padding: ${schemes.spacing.xl} ${schemes.spacing["2xl"]};
                      border: 0.936px solid ${colors.border.dark.bgLighten};
                      opacity: 0.48;
                      box-shadow:
                        0 6px 10px 4px rgba(0, 0, 0, 0.15),
                        0 2px 3px 0 rgba(0, 0, 0, 0.3);
                      width: 34rem;
                      z-index: 4;
                      bottom: -2.44rem;
                    }
                    &:nth-child(3) {
                      padding: ${schemes.spacing.md} ${schemes.spacing.xl};
                      border: 0.872px solid ${colors.border.dark.bgLighten};
                      opacity: 0.24;
                      box-shadow:
                        0 4px 8px 3px rgba(0, 0, 0, 0.15),
                        0 1px 3px 0 rgba(0, 0, 0, 0.3);
                      width: 31rem;
                      z-index: 3;
                      bottom: -4.25rem;
                    }
                    &:nth-child(4) {
                      padding: ${schemes.spacing.md} ${schemes.spacing.xl};
                      border: 0.809px solid ${colors.border.dark.bgLighten};
                      opacity: 0.12;
                      box-shadow:
                        0 2px 6px 2px rgba(0, 0, 0, 0.15),
                        0 1px 2px 0 rgba(0, 0, 0, 0.3);
                      width: 28rem;
                      z-index: 2;
                      bottom: -6rem;
                    }
                  }
                }
              }
            }
          }
        }
      `}</style>
    </>
  )
}
