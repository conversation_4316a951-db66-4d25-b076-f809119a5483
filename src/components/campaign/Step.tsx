import StepFlips from "@/components/campaign/StepFlips"
import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { useCallback, useEffect, useMemo, useState } from "react"
import SelectProduct from "@/components/campaign/steps/SelectProduct"
import Purpose from "@/components/campaign/steps/Purpose"
import SelectCreators from "@/components/campaign/steps/SelectCreators"
import Sequences from "@/components/campaign/steps/Sequences"
import SetDatetime from "@/components/campaign/steps/SetDatetime"
import { CampaignTypes } from "@/common/enums/campaignTypes"
import useCampaignStore from "@/store/useCampaignStore"
import { saveCampaign } from "@/service/campaign"
import { calcDateByTimezone, dateFormatYmd } from "@/common/utils/date"
import useWorkspace from "@/store/useWorkspace"
import { useRouter } from "next/navigation"
import useUserInfo from "@/store/useUserInfo"
import { UserType } from "@/common/types/userType"
import Link from "next/link"
import TargetCollabs from "@/components/campaign/steps/TargetCollabs"
import Button from "@/components/elements/Button"

export default function CampaignStep() {
  const {
    purpose,
    setPurpose,
    purposeName,
    setPurposeName,
    products,
    setProduct,
    updateProduct,
    removeProduct,
    isCollabs,
    setIsCollabs,
    collabs,
    setCollabs,
    creatorTags,
    addTag,
    removeTag,
    sequences,
    addSequence,
    setSequences,
    removeSequence,
    datetime,
    setDateTime,
    isSchedule,
    setIsSchedule,
    resetCampaignStore,
  } = useCampaignStore()
  const { userInfo, setUserInfo } = useUserInfo()
  const { currentWorkspace } = useWorkspace()
  const workspaceId = useMemo(() => currentWorkspace?.id, [currentWorkspace])
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [openStep, setOpenStep] = useState<number>(0)
  const [steps] = useState([
    {
      type: CampaignTypes.purpose,
      title: "Select campaign goal",
      doneLabel: "First outreach configured",
    },
    {
      type: CampaignTypes.products,
      title: "Pick products",
      doneLabel: 'Product selected: "{product.name}"',
    },
    {
      type: CampaignTypes.creators,
      title: "Select creator list",
      doneLabel: "Creator list selected",
    },
    {
      type: CampaignTypes.target,
      title: "Invite target collaboration",
      doneLabel: "Target collaboration",
    },
    {
      type: CampaignTypes.messages,
      title: "Write messages",
      doneLabel: "Messages configured",
    },
    {
      type: CampaignTypes.datetime,
      title: "Schedule campaign",
      doneLabel: null,
    },
  ])

  useEffect(() => {
    if (datetime === null) resetCampaignStore()
  }, [datetime])

  const doneCheck = useCallback(
    (type: string) => {
      let fillMessages = true
      let isCollabsDone = true

      switch (type) {
        case CampaignTypes.purpose:
          return purpose?.length ?? false
        case CampaignTypes.products:
          return products?.length ?? false
        case CampaignTypes.creators:
          return creatorTags?.length ?? false
        case CampaignTypes.target:
          if (isCollabs) {
            isCollabsDone =
              collabs?.name &&
              collabs?.endTime &&
              collabs?.email &&
              collabs?.message?.length > 0
            products.map((product: any) => {
              if (!product?.commission) isCollabsDone = false
            })
          } else if (openStep < 3) isCollabsDone = false
          return isCollabsDone
        case CampaignTypes.messages:
          if (isCollabs) {
            sequences?.map((message) => {
              if (message.message.length === 0) fillMessages = false
            })
          } else {
            if (sequences?.length > 0) {
              sequences?.map((message) => {
                if (message.message.length === 0) fillMessages = false
              })
            } else {
              fillMessages = false
            }
          }
          return fillMessages
        case CampaignTypes.datetime:
          return false
      }
    },
    [
      collabs?.email,
      collabs?.endDate,
      collabs?.message?.length,
      collabs?.name,
      creatorTags?.length,
      isCollabs,
      openStep,
      products,
      purpose?.length,
      sequences,
    ]
  )

  const parseLabel = useCallback(
    (type: string) => {
      const doneLabel = steps.find((step) => step.type === type)?.doneLabel
      switch (type) {
        case CampaignTypes.purpose:
          return purpose ? doneLabel?.replace("{purpose}", purpose) : ""
        case CampaignTypes.products:
          return (
            doneLabel?.replace(
              "{product.name}",
              `${products?.[0]?.name.length > 20 ? `${products?.[0]?.name.substring(0, 40)}...` : products?.[0]?.name}${products.length > 1 ? ` and ${products.length - 1} more` : ""}`
            ) ?? ""
          )
        default:
          return doneLabel
      }
    },
    [products, purpose, steps]
  )

  const stepBodyHtml = useCallback(
    (step: string, index: number, disabled: boolean) => {
      switch (step) {
        case CampaignTypes.purpose:
          return (
            <Purpose
              purpose={purpose}
              setPurpose={setPurpose}
              setPurposeName={setPurposeName}
              next={() => setOpenStep(index + 1)}
              nextDisabled={disabled}
            />
          )
        case CampaignTypes.products:
          return (
            <SelectProduct
              products={products}
              setProduct={setProduct}
              removeProduct={removeProduct}
              prev={() => setOpenStep(index - 1)}
              next={() => setOpenStep(index + 1)}
              nextDisabled={disabled}
            />
          )
        case CampaignTypes.target:
          return (
            <TargetCollabs
              collabs={collabs}
              setCollabs={setCollabs}
              isCollabs={isCollabs}
              setIsCollabs={setIsCollabs}
              products={products}
              updateProduct={updateProduct}
              prev={() => setOpenStep(index - 1)}
              next={() => setOpenStep(index + 1)}
              nextDisabled={disabled}
            />
          )
        case CampaignTypes.creators:
          return (
            <SelectCreators
              creatorTags={creatorTags}
              addTag={addTag}
              removeTag={removeTag}
              prev={() => setOpenStep(index - 1)}
              next={() => setOpenStep(index + 1)}
              nextDisabled={disabled}
            />
          )
        case CampaignTypes.messages:
          return (
            <Sequences
              sequences={sequences}
              addSequence={addSequence}
              setSequences={setSequences}
              removeSequences={removeSequence}
              isCollabs={isCollabs}
              prev={() => setOpenStep(index - 1)}
              next={() => setOpenStep(index + 1)}
              nextDisabled={disabled}
            />
          )
        case CampaignTypes.datetime:
          return (
            <SetDatetime
              schedule={datetime}
              setSchedule={setDateTime}
              isSchedule={isSchedule}
              setIsSchedule={setIsSchedule}
              prev={() => setOpenStep(index - 1)}
              done={() => parseToSaveCampaign()}
              isLoading={isLoading}
            />
          )
      }
    },
    [
      purpose,
      setPurpose,
      setPurposeName,
      products,
      setProduct,
      removeProduct,
      collabs,
      setCollabs,
      isCollabs,
      creatorTags,
      addTag,
      removeTag,
      sequences,
      addSequence,
      setSequences,
      removeSequence,
      datetime,
      setDateTime,
      isSchedule,
      setIsSchedule,
      isLoading,
    ]
  )

  const parseToSaveCampaign = useCallback(async () => {
    if (workspaceId) {
      setIsLoading(true)
      const now = new Date()
      const data = {
        name: `[${dateFormatYmd(new Date())}] ${purposeName}`,
        type: purpose,
        tagIds: creatorTags,
        sequences: sequences.map((sequence) => ({
          type: sequence.type,
          message: sequence.message,
          sendDelayDay: sequence.days,
        })),
        timezone: datetime?.timezoneArray?.dstOffsetStr,
        timezoneName: datetime?.timezoneArray?.name,
        startDate: isSchedule
          ? datetime.startDate
          : dateFormatYmd(
              calcDateByTimezone({ offset: datetime?.timezoneArray?.dstOffset })
            ),
        endDate: isSchedule
          ? datetime.endDate
          : dateFormatYmd(
              calcDateByTimezone({
                date: new Date(now.setFullYear(now.getFullYear() + 2)),
                offset: datetime?.timezoneArray?.dstOffset,
              })
            ),
        startTime: datetime.startTime,
        endTime: datetime.endTime,
        weekdays: datetime.weeks,
        useCustomSchedule: isSchedule,
        targetCollaboration: isCollabs
          ? {
              name: collabs.name,
              contactEmail: collabs.email,
              message: collabs.message,
              endTime: collabs.endTime,
              hasFreeSample: collabs.hasFreeSample,
              isSampleApprovalExempt: collabs.isSampleApprovalExempt,
            }
          : null,
        products: products.map((product: any) => ({
          tiktokProductId: product.tiktok_product_id,
          productName: product.name,
          collaborationRate: product.commission,
          price: product.price,
        })),
      }

      saveCampaign(workspaceId, data)
        .then((res) => {
          setUserInfo({ ...userInfo, has_created_campaign: true } as UserType)
          resetCampaignStore()
          router.push("/campaign")
        })
        .catch(console.error)
        .finally(() => setIsLoading(false))
    }
  }, [
    collabs,
    creatorTags,
    datetime,
    isCollabs,
    isSchedule,
    products,
    purpose,
    purposeName,
    resetCampaignStore,
    router,
    sequences,
    setUserInfo,
    userInfo,
    workspaceId,
  ])

  return (
    <>
      <div className={"layout"}>
        <div>
          {userInfo?.has_created_campaign && (
            <Link href={"/campaign"}>
              <Icon path={getAssetPath("line_arrow_left.svg")} />
            </Link>
          )}
        </div>
        <div>
          <div>
            <h1>Start a new campaign</h1>
            <p>Easily reach and manage multiple creators.</p>
          </div>
          <div>
            <StepFlips
              number={0}
              title={"Connect TikTok Shop"}
              doneTitle={"Connect TikTok Shop"}
              isDone={true}
              inputDisabled={true}
            ></StepFlips>
            {steps.map((step, index) => {
              const done = doneCheck(step.type)
              return (
                <StepFlips
                  number={index + 1}
                  title={step.title}
                  doneTitle={parseLabel(step.type) ?? step.title}
                  checked={index === openStep}
                  key={step.title}
                  isDone={done}
                  inputDisabled={true}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setOpenStep(index)
                    }
                  }}
                >
                  {stepBodyHtml(step.type, index, !done)}
                </StepFlips>
              )
            })}
            {/*<div style={{ display: "flex", justifyContent: "flex-end" }}>*/}
            {/*  <Button*/}
            {/*    onClick={() => {*/}
            {/*      setOpenStep(0)*/}
            {/*      resetCampaignStore()*/}
            {/*    }}*/}
            {/*  >*/}
            {/*    Reset*/}
            {/*  </Button>*/}
            {/*</div>*/}
          </div>
        </div>
      </div>
      <style jsx>{`
        .layout {
          > div {
            width: 100%;
            height: calc(100vh - 3.5rem);
            overflow-y: scroll;
            -ms-overflow-style: none;
            &::-webkit-scrollbar {
              display: none;
            }
            &:first-child {
              z-index: 10;
              position: sticky;
              top: 0;
              height: 3.5rem;
              padding: 0 ${schemes.spacing["3xl"]};
              display: flex;
              align-items: center;
              background: ${colors.bg.dark.surfaceDim};
              border-bottom: ${schemes.borderWidth.sm} solid
                ${colors.border.dark.default};
              > button {
                background: none;
                border: none;
                margin: 0;
                padding: 0;
                font-size: 0;
              }
            }
            &:last-child {
              padding: ${schemes.spacing["6xl"]};
              > div {
                margin: 0 auto;
                width: 40rem;
                &:first-child {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  gap: ${schemes.spacing.md};
                  > h1 {
                    text-align: center;
                    font-size: ${fonts.heading.h2.fontSize};
                    font-weight: ${fonts.heading.h2.fontWeight};
                    line-height: ${fonts.heading.h2.lineHeight};
                    margin: 0;
                  }
                  > p {
                    font-size: ${fonts.body.large.fontSize};
                    font-weight: ${fonts.body.large.fontWeight};
                    line-height: ${fonts.body.large.lineHeight};
                    margin: 0;
                  }
                }
                &:last-child {
                  padding: 1.5rem;
                  display: flex;
                  flex-direction: column;
                  gap: ${schemes.spacing["2xl"]};
                }
              }
            }
          }
        }
      `}</style>
    </>
  )
}
