import React from "react"
import LabelButton from "@/components/elements/LabelButton"
import Icon from "@/components/elements/Icon"
import { getAssetPath } from "@/utils/util"
import { colors, fonts, schemes } from "@/utils/theme/style"

export default function StepFlips({
  children,
  number,
  title,
  doneTitle,
  isDone = false,
  inputDisabled = false,
  defaultChecked,
  checked,
  onChange,
}: {
  children?: React.ReactNode
  number: number
  title: string
  doneTitle: string
  isDone?: boolean
  inputDisabled?: boolean
  defaultChecked?: boolean
  checked?: boolean
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
}) {
  return (
    <>
      <div className="step-group">
        <input
          type="radio"
          name={"campaign"}
          id={`campaign${number}`}
          disabled={inputDisabled}
          defaultChecked={defaultChecked}
          checked={checked}
          onChange={onChange}
        />
        <div className={`block ${isDone ? "done" : ""}`}>
          <label htmlFor={`campaign${number}`}>
            <div>
              {isDone ? (
                <Icon
                  path={getAssetPath("check_circle.svg")}
                  color={colors.sys.dark.green.default}
                />
              ) : (
                <LabelButton size={"sm"}>{number}</LabelButton>
              )}
              <span className={"title"}>{title}</span>
              <span className={"done-title"}>{doneTitle}</span>
            </div>
          </label>
          <div className={"flip"}>{children}</div>
        </div>
      </div>
      <style jsx>{`
        .step-group {
          input[type="radio"] {
            display: none;
            &:checked + .block {
              max-height: 2000px;
              border: 1px solid ${colors.border.dark.primary};
              .flip {
                display: block;
              }

              > label > div > span {
                &.title {
                  display: block !important;
                }
                &.done-title {
                  display: none !important;
                }
              }
            }
          }
          .block {
            padding: ${schemes.spacing.xl} ${schemes.spacing["2xl"]};
            border-radius: ${schemes.radius.sm};
            border: 1px solid ${colors.border.dark.bgLighten};
            background: ${colors.bg.dark.surface};
            max-height: 3.0625rem;
            overflow: hidden;
            transition: all 0.5s ease-in-out;
            > label > div {
              display: flex;
              align-items: center;
              gap: ${schemes.spacing.md};
              > span {
                font-size: ${fonts.body.body.fontSize};
                font-weight: ${fonts.body.body.fontWeight};
                line-height: ${fonts.body.body.lineHeight};
                &.title {
                  display: block;
                }
                &.done-title {
                  display: none;
                }
              }
            }
            .flip {
              display: none;
              margin-top: ${schemes.spacing["2xl"]};
            }
            &.done {
              > label > div > span {
                &.title {
                  display: none;
                }
                &.done-title {
                  display: block;
                }
              }
            }
          }
        }
      `}</style>
    </>
  )
}
