import { colors, fonts, schemes } from "@/utils/theme/style"
import Button from "@/components/elements/Button"
import { getAssetPath } from "@/utils/util"
import Icon from "@/components/elements/Icon"
import LabelTag from "@/components/elements/LabelTag"
import Link from "next/link"
import { ColorType } from "@/common/types/colorType"
import React, { useEffect, useMemo, useState } from "react"
import { getCampaignList } from "@/service/campaign"
import useWorkspace from "@/store/useWorkspace"
import { useSearchParams } from "next/navigation"
import { campaignLabels } from "@/common/utils/campaign"
import Loading from "@/components/common/Loading"
import { dateFormatYmd } from "@/common/utils/date"

export default function CampaignList() {
  const [campaigns, setCampaigns] = useState([])
  const { currentWorkspace } = useWorkspace()
  const workspaceId = useMemo(() => currentWorkspace?.id, [currentWorkspace])

  const searchParams = useSearchParams()
  const status = searchParams.get("status")

  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    setIsLoading(true)
    getCampaignList(workspaceId!, status)
      .then((res) => {
        setCampaigns(res.campaigns)
      })
      .catch(console.error)
      .finally(() => setIsLoading(false))
  }, [workspaceId, status])

  return (
    <>
      <div className={"layout"}>
        <div className={"tabs"}>
          <div>
            <div>
              <Link href={"/campaign"} className={!status ? "active" : ""}>
                All Campaign
              </Link>
              {campaignLabels.map((label) => (
                <Link
                  key={label.name}
                  href={`/campaign?status=${label.name}`}
                  className={status === label.name ? "active" : ""}
                >
                  {label.name}
                </Link>
              ))}
            </div>
            <hr />
            <div>
              <Link
                href={"/campaign/create"}
                style={{ textDecoration: "none" }}
              >
                <Button size={"sm"} icon={{ path: getAssetPath("add.svg") }}>
                  Create
                </Button>
              </Link>
            </div>
          </div>
        </div>
        <div className={"list"}>
          <div>
            {!isLoading &&
              campaigns?.map((campaign: any) => (
                <Link
                  href={`/campaign/${campaign.id}`}
                  key={campaign.id}
                  style={{ textDecoration: "none" }}
                >
                  <div className={"list-item"}>
                    <div>
                      <div>
                        <LabelTag
                          type={"status"}
                          color={
                            campaignLabels.find(
                              (label) => label.name === campaign.status
                            )?.color as ColorType
                          }
                        >
                          {campaign.status}
                        </LabelTag>
                      </div>
                      <h4>{campaign.name}</h4>
                      <p></p>
                    </div>
                    <div>
                      <div>
                        <Icon
                          path={getAssetPath("calendar_false.svg")}
                          size={"1rem"}
                        />
                      </div>
                      <span>
                        {campaign.start_date && campaign.end_date
                          ? `${campaign.start_date} ~ ${campaign.end_date}`
                          : dateFormatYmd(new Date(campaign.created_at))}
                      </span>
                    </div>
                    <div>
                      <div>
                        <h5>Creators</h5>
                        <p>{campaign.creator_count}</p>
                      </div>
                      <div>
                        <h5>Message</h5>
                        <p>{campaign.message_count}</p>
                      </div>
                      <div>
                        <h5>Replied</h5>
                        <p>{campaign.reply_count}</p>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
          </div>
          {isLoading && (
            <div className={"loading"}>
              <Loading />
            </div>
          )}
        </div>
      </div>
      <style jsx>{`
        .layout {
          min-width: 1328px;
          height: 100vh;
          display: flex;
          flex-direction: column;
          overflow-y: scroll;
          -ms-overflow-style: none;
          &::-webkit-scrollbar {
            display: none;
          }

          > div {
            width: 100%;
            > div {
              width: 1280px;
              margin: 0 auto;
            }
            &.tabs {
              display: block;
              height: 3.5rem;
              padding: 0 ${schemes.spacing["3xl"]};
              border-bottom: ${schemes.borderWidth.sm} solid
                ${colors.border.dark.default};

              > div {
                display: flex;
                align-items: center;
                gap: ${schemes.spacing["3xl"]};

                > div:first-of-type {
                  flex: 1;
                  display: flex;
                  align-items: center;
                  gap: ${schemes.spacing["3xl"]};
                  a {
                    position: relative;
                    color: ${colors.schems.dark.onDisabled};

                    font-size: ${fonts.body.bodyBold.fontSize};
                    font-weight: ${fonts.body.bodyBold.fontWeight};
                    line-height: 3.5rem;
                    text-decoration: none;
                    cursor: pointer;
                    &:hover,
                    &.active {
                      color: ${colors.schems.dark.onBase};
                      &:after {
                        position: absolute;
                        content: "";
                        height: 2px;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: ${colors.schems.dark.primary};
                      }
                    }
                  }
                }
                > hr {
                  width: 0;
                  height: ${schemes.spacing["3xl"]};
                  border: 1px solid ${colors.border.dark.default};
                }
              }
            }
            &.list {
              flex: 1;
              padding: ${schemes.spacing["3xl"]};
              min-height: calc(100vh - 3.5rem);
              > div:not(.loading) {
                display: grid;
                grid-gap: ${schemes.spacing["2xl"]};
                grid-template-columns: repeat(4, 1fr);
                div.list-item {
                  white-space: nowrap;
                  padding: ${schemes.spacing["2xl"]};
                  display: flex;
                  flex-direction: column;
                  gap: ${schemes.spacing["2xl"]};
                  overflow-x: auto;
                  border-radius: ${schemes.radius.md};
                  background: ${colors.bg.dark.surface};
                  color: ${colors.schems.dark.onBase};
                  > div {
                    &:first-of-type {
                      display: flex;
                      flex-direction: column;
                      align-items: start;
                      gap: ${schemes.spacing.md};
                      > h4 {
                        margin: 0;
                        font-size: ${fonts.heading.h4.fontSize};
                        font-weight: ${fonts.heading.h4.fontWeight};
                        line-height: ${fonts.heading.h4.lineHeight};
                      }
                      > p {
                        width: 100%;
                        margin: 0;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        font-size: ${fonts.body.body.fontSize};
                        font-weight: ${fonts.body.body.fontWeight};
                        line-height: ${fonts.body.body.lineHeight};
                      }
                    }
                    &:nth-of-type(2) {
                      display: flex;
                      align-items: center;
                      gap: ${schemes.spacing.sm};
                      > div {
                        font-size: 0;
                      }
                      > span {
                        color: ${colors.schems.dark.onDisabled};
                        font-size: ${fonts.body.body.fontSize};
                        font-weight: ${fonts.body.body.fontWeight};
                        line-height: ${fonts.body.body.lineHeight};
                      }
                    }
                    &:last-of-type {
                      display: grid;
                      grid-gap: ${schemes.spacing.md};
                      grid-template-columns: repeat(3, 1fr);
                      > div {
                        padding: ${schemes.spacing.md};
                        border-radius: ${schemes.radius.sm};
                        background: ${colors.bg.dark.surfaceBright};
                        text-align: center;
                        > h5 {
                          margin: 0;
                          color: ${colors.schems.dark.onDisabled};
                          font-size: ${fonts.body.label.fontSize};
                          font-weight: ${fonts.body.label.fontWeight};
                          line-height: ${fonts.body.label.lineHeight};
                        }
                        > p {
                          margin: 0;
                          font-size: ${fonts.body.body.fontSize};
                          font-weight: ${fonts.body.body.fontWeight};
                          line-height: ${fonts.body.body.lineHeight};
                        }
                      }
                    }
                  }
                }
              }
              > .loading {
                height: calc(100vh - 6.5rem);
              }
            }
          }
        }
      `}</style>
    </>
  )
}
