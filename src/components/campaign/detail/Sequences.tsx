import React, { useEffect, useMemo, useState } from "react"
import { colors, fonts, schemes } from "@/utils/theme/style"
import Button from "@/components/elements/Button"
import {
  CampaignMessageContentType,
  CampaignMessageType,
} from "@/common/enums/campaignTypes"
import { updateCampaignMessages } from "@/service/campaign"
import { notifyError, notifySuc<PERSON> } from "@/components/modal/Toasts"
import Sequences from "@/components/campaign/steps/Sequences"

export default function CampaignDetailSequences({
  campaignId,
  campaign,
  setCampaign,
}: {
  campaignId: number
  campaign: any
  setCampaign: (campaign: any) => void
}) {
  const [isLoading, setIsLoading] = useState(false)
  const isCollabs = !!campaign?.campaign_target_collaboration

  const defaultSequence = Object.freeze({
    type: CampaignMessageType.message,
    days: 0,
    message: "",
  })
  const defaultCampaignSequence = useMemo(() => {
    return campaign.campaign_messages
      .filter(
        (campaign: any) =>
          campaign.content_type === CampaignMessageContentType.Text
      )
      .map((message: any) => {
        return {
          id: message.id,
          sort: message.sort,
          status: message.status,
          type: message.type,
          days: message.send_delay_day,
          message: message.content,
        }
      })
  }, [campaign.campaign_messages])
  const [sequences, setSequences] = useState(defaultCampaignSequence)

  const addSequenceHandler = () => {
    setSequences((prev: any) => [...prev, { ...defaultSequence }])
  }

  const resetSequenceHandler = () => {
    setSequences(defaultCampaignSequence)
  }

  const removeSequenceHandler = async (index: number) => {
    setSequences((prev: any) => prev.filter((_: any, i: number) => i !== index))
  }

  const checkFillSequences = () => {
    let fillSequences = false
    sequences.map((data: any) => {
      if (data.message.length === 0) {
        fillSequences = true
      }
    })
    return fillSequences
  }

  const saveSequenceHandler = async () => {
    setIsLoading(true)
    const data = {
      messages: sequences.map((seq: any, i: number) => {
        return seq.id
          ? {
              id: seq.id,
              sort: i,
              type: seq.type,
              content: seq.message,
              send_delay_day: seq.days,
            }
          : {
              sort: i,
              type: seq.type,
              content: seq.message,
              send_delay_day: seq.days,
            }
      }),
    }
    updateCampaignMessages(campaignId, data)
      .then((res) => {
        setCampaign(res)
        notifySuccess("Successfully Sequence saved")
      })
      .catch((e) => {
        console.log(e.message)
        notifyError(e.message)
      })
      .finally(() => setIsLoading(false))
  }

  useEffect(() => {
    setSequences(defaultCampaignSequence)
  }, [defaultCampaignSequence])

  return (
    <>
      <div className={"layout"}>
        <div>
          <div className={"sequences"}>
            <div>
              <Sequences
                sequences={sequences}
                addSequence={addSequenceHandler}
                setSequences={setSequences}
                removeSequences={removeSequenceHandler}
                isCollabs={isCollabs}
              />
            </div>
          </div>
        </div>
        <div>
          <div className={"buttons"}>
            <div></div>
            <div>
              <Button style={"text"} onClick={resetSequenceHandler}>
                Reset
              </Button>
              <Button
                onClick={saveSequenceHandler}
                disabled={
                  checkFillSequences() ||
                  isLoading ||
                  JSON.stringify(sequences) ===
                    JSON.stringify(defaultCampaignSequence)
                }
                isLoading={isLoading}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      </div>
      <style jsx>{`
        .layout {
          width: 100%;
          height: calc(100vh - 3.5rem);
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          > div:first-of-type {
            flex: 1;
            width: 100%;
            padding: ${schemes.spacing["5xl"]};
            overflow-y: auto;
            > .sequences {
              display: flex;
              flex-direction: column;
              gap: ${schemes.spacing["2xl"]};
              width: 40rem;
              padding: ${schemes.spacing["3xl"]};
              margin: 0 auto;
              > div:first-child {
                width: 100%;
                padding: ${schemes.spacing.xl} ${schemes.spacing["2xl"]};
                border-radius: ${schemes.radius.sm};
                background: ${colors.bg.dark.surface};
                padding: ${schemes.spacing["3xl"]};

                display: flex;
                flex-direction: column;
                gap: ${schemes.spacing["2xl"]};
                > div {
                  display: flex;
                  flex-direction: column;
                  gap: ${schemes.spacing["2xl"]};
                  padding: ${schemes.spacing["3xl"]};
                  border-radius: ${schemes.radius.sm};
                  background: ${colors.bg.dark.surface};
                  > * {
                    width: 100%;
                  }
                  > span {
                    font-size: ${fonts.body.large.fontSize};
                    font-weight: ${fonts.body.large.fontWeight};
                    line-height: ${fonts.body.large.lineHeight};
                  }
                  > div:first-of-type {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    height: 40px;
                    > div {
                      display: flex;
                      gap: ${schemes.spacing.md};
                      align-items: center;
                      > div {
                        width: 7.5rem;
                        div {
                          width: 100%;
                          button {
                            width: 100%;
                            justify-content: space-between;
                          }
                        }
                      }
                    }
                  }
                  > hr {
                    height: 0;
                    margin: 0;
                    border: none;
                    border-top: 1px solid ${colors.border.dark.bgLighten};
                  }
                  > div:last-of-type {
                    > textarea {
                      width: 100%;
                      background: ${colors.interactive.dark.hover};
                      border-radius: ${schemes.radius.sm};
                      border: 1px solid ${colors.border.dark.bgLighten};
                      padding: ${schemes.spacing.xl};
                      height: 15rem;

                      color: ${colors.schems.dark.onBase};
                      font-family: ${fonts.heading.fontFamily};
                      font-size: ${fonts.body.large.fontSize};
                      font-weight: ${fonts.body.large.fontWeight};
                      line-height: ${fonts.body.large.lineHeight};
                      resize: none;

                      &:focus-visible {
                        outline: none;
                        border-color: ${colors.border.dark.primary};
                      }
                    }
                  }
                }
              }
              > .add-message {
                display: flex;
                justify-content: center;
              }
            }
          }
          > div:last-of-type {
            width: 100%;
            height: 5rem;
            background: ${colors.schems.dark.base};
            > .buttons {
              display: flex;
              justify-content: space-between;
              padding: ${schemes.spacing["2xl"]} ${schemes.spacing["3xl"]};
              width: 40rem;
              margin: 0 auto;
              > div {
                display: flex;
                gap: ${schemes.spacing.md};
                &:first-child {
                  justify-content: start;
                }
                &:last-child {
                  justify-content: end;
                }
              }
            }
          }
        }
      `}</style>
    </>
  )
}
