import Input from "@/components/elements/Input3"
import { useCallback, useMemo, useState } from "react"
import { colors, fonts, schemes } from "@/utils/theme/style"
import Button from "@/components/elements/Button"
import SetDatetime from "@/components/campaign/steps/SetDatetime"
import { deleteCampaign, updateCampaign } from "@/service/campaign"
import DeleteCampaignModal from "@/components/modal/DeleteCampaignModal"
import { useRouter } from "next/navigation"
import { notifyError, notifySuccess } from "@/components/modal/Toasts"
import { calcDateByTimezone, dateFormatYmd } from "@/common/utils/date"
import { deepCopy } from "@/common/utils/common"

export default function CampaignDetailSetting({
  campaignId,
  campaign,
  setCampaign,
}: {
  campaignId: number
  campaign: any
  setCampaign: (campaign: any) => void
}) {
  const router = useRouter()
  const [name, setName] = useState(campaign.name)
  const [isSchedule, setIsSchedule] = useState(campaign.use_custom_schedule)
  const defaultCampaignSchedule: {
    startDate: string
    endDate: string
    startTime: string
    endTime: string
    weeks: number
    timezone: string
    timezoneName: string
    timezoneArray: any
  } = useMemo(
    () => ({
      startDate: campaign.start_date || "",
      endDate: campaign.end_date || "",
      startTime: campaign.start_time,
      endTime: campaign.end_time,
      weeks: campaign.week_days,
      timezone: campaign.timezone,
      timezoneName: campaign.timezone_name,
      timezoneArray: null,
    }),
    [campaign]
  )
  const [schedule, setSchedule] = useState<any>(defaultCampaignSchedule)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isDeleteLoading, setIsDeleteLoading] = useState(false)
  const [isNameLoading, setIsNameLoading] = useState(false)
  const [isScheduleLoading, setIsScheduleLoading] = useState(false)

  const deleteCampaignHandler = async () => {
    setIsDeleteLoading(true)
    deleteCampaign(campaignId)
      .then(() => {
        setIsModalOpen(false)
        router.push("/campaign")
      })
      .catch((e) => {
        console.error(e.message)
        notifyError(e.message)
      })
      .finally(() => setIsDeleteLoading(false))
  }

  const saveCampaignNameHandler = async () => {
    setIsNameLoading(true)
    const data = { name }
    updateCampaign(campaignId, data)
      .then((res) => {
        setCampaign(res)
        resetNameHandler()
        notifySuccess("Campaign saved successfully")
      })
      .catch((e) => {
        console.error(e.message)
        notifyError(e.message)
      })
      .finally(() => setIsNameLoading(false))
  }

  const checkFillSchedule = useMemo(() => {
    const copySchedule = deepCopy(schedule)
    delete copySchedule.timezoneArray
    const copyDefaultSchedule = deepCopy(schedule)
    delete copyDefaultSchedule.timezoneArray

    return !(
      schedule &&
      ((isSchedule &&
        schedule.startDate?.length > 0 &&
        schedule.endDate?.length > 0) ||
        !isSchedule) &&
      schedule.startTime &&
      schedule.endTime &&
      schedule.timezoneArray &&
      schedule.weeks > 0
    )
    // && JSON.stringify(copySchedule) === JSON.stringify(copyDefaultSchedule)
  }, [isSchedule, schedule])

  const saveCampaignScheduleHandler = async () => {
    setIsScheduleLoading(true)
    const now = new Date()
    const data = {
      timezone: schedule?.timezoneArray?.dstOffsetStr || schedule.timezone,
      timezoneName: schedule?.timezoneArray?.name || schedule.timezoneName,
      startDate: isSchedule
        ? schedule.startDate
        : dateFormatYmd(
            calcDateByTimezone({ offset: schedule?.timezoneArray?.dstOffset })
          ),
      endDate: isSchedule
        ? schedule.endDate
        : dateFormatYmd(
            calcDateByTimezone({
              date: new Date(now.setFullYear(now.getFullYear() + 2)),
              offset: schedule?.timezoneArray?.dstOffset,
            })
          ),
      startTime: schedule.startTime,
      endTime: schedule.endTime,
      weekdays: schedule.weeks,
      useCustomSchedule: isSchedule,
    }
    updateCampaign(campaignId, data)
      .then((res) => {
        setCampaign(res)
        resetScheduleHandler()
        notifySuccess("Campaign saved successfully")
      })
      .catch((e) => {
        console.error(e.message)
        notifyError(e.message)
      })
      .finally(() => setIsScheduleLoading(false))
  }

  const resetNameHandler = useCallback(() => {
    setName(campaign.name)
  }, [campaign.name])

  const resetScheduleHandler = useCallback(() => {
    setIsSchedule(campaign.use_custom_schedule)
    setSchedule(defaultCampaignSchedule)
  }, [campaign.use_custom_schedule, defaultCampaignSchedule])

  return (
    <>
      <div className={"layout"}>
        <div>
          <div className={"title"}>
            <h4>Campaign name</h4>
            <div>
              <Input
                type={"text"}
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder={"Campaign name"}
                clear={setName}
              />
            </div>
            <div className={"buttons"}>
              <Button style={"text"} onClick={resetNameHandler}>
                Reset
              </Button>
              <Button
                isLoading={isNameLoading}
                disabled={
                  isNameLoading || name === campaign.name || name === ""
                }
                onClick={saveCampaignNameHandler}
              >
                Save
              </Button>
            </div>
          </div>
          <div className={"schedule"}>
            <h4>Schedule Campaign</h4>
            {schedule && (
              <SetDatetime
                schedule={schedule}
                setSchedule={setSchedule}
                isSchedule={isSchedule}
                setIsSchedule={setIsSchedule}
                isEdit={true}
              />
            )}

            <div className={"buttons"}>
              <Button style={"text"} onClick={resetScheduleHandler}>
                Reset
              </Button>
              <Button
                isLoading={isScheduleLoading}
                disabled={isScheduleLoading || checkFillSchedule}
                onClick={saveCampaignScheduleHandler}
              >
                Save
              </Button>
            </div>
          </div>
          <div>
            <Button
              style={"secondary"}
              color={colors.sys.dark.red.default}
              onClick={() => setIsModalOpen(true)}
            >
              Delete Campaign
            </Button>
          </div>
        </div>
      </div>
      <DeleteCampaignModal
        isOpen={isModalOpen}
        setIsOpen={setIsModalOpen}
        loading={isDeleteLoading}
        action={deleteCampaignHandler}
        campaignName={name}
      />
      <style jsx>{`
        .layout {
          width: 100%;
          padding: ${schemes.spacing["5xl"]};
          > div {
            width: 40rem;
            padding: ${schemes.spacing["3xl"]};
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: ${schemes.spacing["2xl"]};
            > div:not(:last-child) {
              width: 100%;
              padding: ${schemes.spacing.xl} ${schemes.spacing["2xl"]};
              border-radius: ${schemes.radius.sm};
              background: ${colors.bg.dark.surface};

              display: flex;
              flex-direction: column;
              gap: ${schemes.spacing["2xl"]};
              > * {
                width: 100%;
                margin: 0;
                &h4,
                &.product-list > ul > li,
                &.product-list > ul > li > span,
                &.product-list > p {
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;

                  font-size: ${fonts.body.bodyBold.fontSize};
                  font-weight: ${fonts.body.bodyBold.fontWeight};
                  line-height: ${fonts.body.bodyBold.lineHeight};
                }
                &.buttons {
                  display: flex;
                  align-items: center;
                  justify-content: end;
                  gap: ${schemes.spacing.md};
                }
                &.product-list {
                  border-radius: ${schemes.radius.sm};
                  border: 1px solid ${colors.border.dark.bgLighten};
                  box-shadow:
                    0 1px 3px 1px rgba(0, 0, 0, 0.15),
                    0 1px 2px 0 rgba(0, 0, 0, 0.3);
                  padding: ${schemes.spacing.md} ${schemes.spacing["2xl"]};
                  > ul {
                    margin: 0;
                    padding-left: 0;
                    > li {
                      &::marker {
                        display: inline-block;
                        color: ${colors.schems.dark.onBase};
                        font-size: ${fonts.body.bodyBold.fontSize};
                        font-weight: ${fonts.body.bodyBold.fontWeight};
                        line-height: 2.5rem;
                      }
                      > span {
                        display: inline-block;
                        line-height: 2.5rem;
                        &:first-child {
                          width: 1.5rem;
                          text-align: center;
                        }
                      }
                    }
                  }
                  > p {
                    margin: 0;
                    line-height: 2.5rem;
                    text-align: center;
                  }
                }
              }
            }
          }
        }
      `}</style>
    </>
  )
}
