import Button from "@/components/elements/Button"
import React, { useEffect, useMemo, useState } from "react"
import { colors, fonts, schemes } from "@/utils/theme/style"
import { CampaignStatus } from "@/common/enums/campaignTypes"
import SelectProduct from "@/components/campaign/steps/SelectProduct"
import Information from "@/components/common/Information"
import TargetCollabs from "@/components/campaign/steps/TargetCollabs"
import { notifyError, notifySuccess } from "@/components/modal/Toasts"
import { updateCampaignTargetCollabs } from "@/service/campaign"

export default function CampaignDetailTarget({
  campaignId,
  campaign,
  setCampaign,
}: {
  campaignId: number
  campaign: any
  setCampaign: (campaign: any) => void
}) {
  const defaultCampaignProducts = useMemo(
    () =>
      campaign?.campaign_products.map((product: any) => ({
        tiktok_product_id: product.tiktok_product_id,
        name: product.name,
        commission: product.commission_rate,
        price: product.price,
      })),
    [campaign?.campaign_products]
  )
  const defaultCampaignTargetCollabs = useMemo(
    () => ({
      name: campaign?.campaign_target_collaboration?.name,
      email: campaign?.campaign_target_collaboration?.contact,
      message: campaign?.campaign_target_collaboration?.message,
      endTime: campaign?.campaign_target_collaboration?.end_time,
      hasFreeSample: campaign?.campaign_target_collaboration?.has_free_sample,
      isSampleApprovalExempt:
        campaign?.campaign_target_collaboration?.is_sample_approval_exempt,
    }),
    [campaign?.campaign_target_collaboration]
  )
  const [products, setProducts] = useState(defaultCampaignProducts)
  const [collabs, setCollabs] = useState(defaultCampaignTargetCollabs)
  const [isCollabs, setIsCollabs] = useState<boolean>(
    !!campaign?.campaign_target_collaboration
  )
  const [isLoading, setIsLoading] = useState(false)

  const updateProduct = (value: any, index: number) => {
    setProducts((prev: any) =>
      prev.map((product: any, idx: number) => {
        return index === idx ? value : product
      })
    )
  }

  const updateCollabs = (value: any) => {
    setCollabs((prev: any) => ({ ...prev, ...value }))
  }

  const saveHandler = () => {
    setIsLoading(true)
    const data = {
      targetCollaboration: isCollabs
        ? {
            name: collabs.name,
            contactEmail: collabs.email,
            message: collabs.message,
            endTime: collabs.endTime,
            hasFreeSample: collabs.hasFreeSample,
            isSampleApprovalExempt: collabs.isSampleApprovalExempt,
          }
        : null,
      products: products.map((product: any) => ({
        tiktokProductId: product.tiktok_product_id,
        productName: product.name,
        collaborationRate: product.commission,
        price: product.price,
      })),
    }

    updateCampaignTargetCollabs(campaignId, data)
      .then((res) => {
        setCampaign(res)
        notifySuccess("Target saved successfully")
      })
      .catch((e) => {
        console.error(e.message)
        notifyError(e.message)
      })
      .finally(() => setIsLoading(false))
  }

  const resetHandler = () => {
    setProducts(defaultCampaignProducts)
    setCollabs(defaultCampaignTargetCollabs)
    setIsCollabs(!!campaign?.campaign_target_collaboration)
  }

  const changeFillCheck = useMemo(() => {
    if (
      JSON.stringify(products.map((product: any) => product.name)) ===
        JSON.stringify(
          defaultCampaignProducts.map((product: any) => product.name)
        ) &&
      JSON.stringify(defaultCampaignTargetCollabs) ===
        JSON.stringify(collabs) &&
      isCollabs === !!campaign?.campaign_target_collaboration
    ) {
      return false
    } else {
      return (
        (isCollabs &&
          (JSON.stringify(defaultCampaignTargetCollabs) !==
            JSON.stringify(collabs) ||
            JSON.stringify(products.map((product: any) => product.name)) !==
              JSON.stringify(
                defaultCampaignProducts.map((product: any) => product.name)
              )) &&
          collabs?.name?.length > 0 &&
          collabs?.endTime?.length > 0 &&
          collabs?.email?.length > 0 &&
          collabs?.message?.length > 0 &&
          products.length ===
            products.filter((product: any) => product?.commission !== undefined)
              .length) ||
        !isCollabs
      )
    }
  }, [
    campaign?.campaign_target_collaboration,
    collabs,
    defaultCampaignProducts,
    defaultCampaignTargetCollabs,
    isCollabs,
    products,
  ])

  useEffect(() => {
    setProducts(defaultCampaignProducts)
    setCollabs(defaultCampaignTargetCollabs)
    setIsCollabs(!!campaign?.campaign_target_collaboration)
  }, [
    campaign?.campaign_target_collaboration,
    defaultCampaignProducts,
    defaultCampaignTargetCollabs,
  ])

  return (
    <>
      <div className={"layout"}>
        <div>
          <div>
            {campaign.status === CampaignStatus.pending ||
            campaign.status === CampaignStatus.ready ? (
              <Information type={"warning"}>
                You can&#39;t change the product after sending messages.
              </Information>
            ) : (
              <Information>
                You can&#39;t change the product after sending messages.
              </Information>
            )}
            <div className={"products"}>
              <h4>Select Product</h4>
              {campaign.status === CampaignStatus.pending ||
              campaign.status === CampaignStatus.ready ? (
                <>
                  <SelectProduct
                    products={products}
                    setProduct={(product) =>
                      setProducts((prev: any) => [...prev, product])
                    }
                    removeProduct={(product) =>
                      setProducts((prev: any) =>
                        prev.filter((item: any) => item.name !== product.name)
                      )
                    }
                  />
                </>
              ) : (
                <>
                  <div className={"product-list"}>
                    {products.length > 0 ? (
                      <ul>
                        {campaign?.campaign_products?.map(
                          (product: any, index: number) => (
                            <li key={index}>
                              <span>•</span>
                              <span>{product.name}</span>
                            </li>
                          )
                        )}
                      </ul>
                    ) : (
                      <p>No products found.</p>
                    )}
                  </div>
                </>
              )}
            </div>
            <div className={"target-collabs"}>
              <TargetCollabs
                collabs={collabs}
                setCollabs={updateCollabs}
                isCollabs={isCollabs}
                setIsCollabs={setIsCollabs}
                products={products}
                updateProduct={updateProduct}
                bgColor={colors.bg.dark.surface}
                allDisabled={
                  campaign.status !== CampaignStatus.pending &&
                  campaign.status !== CampaignStatus.ready
                }
              />
            </div>
          </div>
        </div>
        {(campaign.status === CampaignStatus.pending ||
          campaign.status === CampaignStatus.ready) && (
          <div>
            <div className={"buttons"}>
              <div></div>
              <div>
                <Button style={"text"} onClick={resetHandler}>
                  Reset
                </Button>
                <Button
                  onClick={saveHandler}
                  disabled={isLoading || !changeFillCheck}
                  isLoading={isLoading}
                >
                  Save
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
      <style jsx>{`
        .layout {
          width: 100%;
          height: calc(100vh - 3.5rem);
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          > div:first-of-type {
            flex: 1;
            width: 100%;
            padding: ${schemes.spacing["5xl"]};
            overflow-y: auto;

            > div {
              display: flex;
              flex-direction: column;
              gap: ${schemes.spacing["2xl"]};
              width: 40rem;
              padding: ${schemes.spacing["3xl"]};
              margin: 0 auto;

              > div {
                width: 100%;

                &.products {
                  padding: ${schemes.spacing["5xl"]} ${schemes.spacing["2xl"]};
                  border-radius: ${schemes.radius.sm};
                  background: ${colors.bg.dark.surface};

                  > .product-list {
                    margin-top: ${schemes.spacing["2xl"]};
                    border-radius: ${schemes.radius.sm};
                    border: 1px solid ${colors.border.dark.bgLighten};
                    box-shadow:
                      0 1px 3px 1px rgba(0, 0, 0, 0.15),
                      0 1px 2px 0 rgba(0, 0, 0, 0.3);

                    > ul {
                      margin: 0;
                      padding: ${schemes.spacing.md} ${schemes.spacing["2xl"]};
                      list-style: none;

                      > li {
                        width: 100%;

                        display: flex;
                        align-items: center;
                        line-height: 2.5rem;

                        > span:first-of-type {
                          display: inline-block;
                          width: 1.5rem;
                          text-align: center;
                        }

                        > span:last-of-type {
                          text-overflow: ellipsis;
                          overflow: hidden;
                          white-space: nowrap;
                        }
                      }
                    }
                  }
                }

                > * {
                  width: 100%;
                  margin: 0;

                  &h4 {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;

                    font-size: ${fonts.body.bodyBold.fontSize};
                    font-weight: ${fonts.body.bodyBold.fontWeight};
                    line-height: ${fonts.body.bodyBold.lineHeight};
                  }
                }
              }
            }
          }

          > div:last-of-type {
            width: 100%;
            height: 5rem;
            background: ${colors.schems.dark.base};

            > .buttons {
              display: flex;
              justify-content: space-between;
              padding: ${schemes.spacing["2xl"]} ${schemes.spacing["3xl"]};
              width: 40rem;
              margin: 0 auto;

              > div {
                display: flex;
                gap: ${schemes.spacing.md};

                &:first-child {
                  justify-content: start;
                }

                &:last-child {
                  justify-content: end;
                }
              }
            }
          }
        }
      `}</style>
    </>
  )
}
