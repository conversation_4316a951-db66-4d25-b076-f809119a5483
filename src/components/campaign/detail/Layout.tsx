import Icon from "@/components/elements/Icon"
import { ReactNode, useState } from "react"
import Link from "next/link"
import { getAssetPath } from "@/utils/util"
import Button from "@/components/elements/Button"
import { colors, fonts, schemes } from "@/utils/theme/style"
import LabelTag from "@/components/elements/LabelTag"
import { campaignLabels } from "@/common/utils/campaign"
import { ColorType } from "@/common/types/colorType"
import { CampaignStatus } from "@/common/enums/campaignTypes"
import { changeCampaignStatus } from "@/service/campaign"
import { notifyError, notifySuccess } from "@/components/modal/Toasts"

export default function Layout({
  children,
  campaignId,
  campaign,
  setCampaign,
  tab,
}: {
  children: ReactNode
  campaignId: number
  campaign: any
  setCampaign: (campaign: any) => void
  tab: string
}) {
  const [isStatusLoading, setIsStatusLoading] = useState(false)

  const changeCampaignStatusHandler = async (status: string) => {
    setIsStatusLoading(true)
    changeCampaignStatus(campaignId, status)
      .then(() => {
        setCampaign((prev: any) => ({ ...prev, status: status }))
        notifySuccess("Campaign status changed successfully.")
      })
      .catch((e) => {
        console.error(e.message)
        notifyError(e.message)
      })
      .finally(() => setIsStatusLoading(false))
  }

  return (
    <>
      <div className={"layout"}>
        <div className={"nav"}>
          <div>
            <div>
              <Link href="/campaign">
                <Icon path={getAssetPath("line_arrow_left.svg")} />
              </Link>
            </div>
            <div className={"title"}>
              <span>{campaign?.name ?? "Campaign Name"}</span>
              <Link href={`/campaign/${campaignId}?tab=setting`}>
                <Icon
                  path={getAssetPath("edit.svg")}
                  size={"1rem"}
                  color={colors.schems.dark.onBase}
                />
              </Link>
            </div>
            <hr />
            <div className={"tabs"}>
              <Link
                href={`/campaign/${campaignId}`}
                className={tab === "creators" ? "active" : ""}
              >
                Creators
              </Link>
              <Link
                href={`/campaign/${campaignId}?tab=target`}
                className={tab === "target" ? "active" : ""}
              >
                Target
              </Link>
              <Link
                href={`/campaign/${campaignId}?tab=sequence`}
                className={tab === "sequence" ? "active" : ""}
              >
                Sequences
              </Link>
              <Link
                href={`/campaign/${campaignId}?tab=setting`}
                className={tab === "setting" ? "active" : ""}
              >
                Setting
              </Link>
            </div>
            <hr />
            <div>
              <LabelTag
                type={"status"}
                color={
                  campaignLabels.find((label) => label.name === campaign.status)
                    ?.color as ColorType
                }
              >
                {campaign.status}
              </LabelTag>
            </div>
            <div>
              {campaign.status !== CampaignStatus.completed &&
                (campaign.status === CampaignStatus.pending ||
                  campaign.status === CampaignStatus.ready ||
                  campaign.status === CampaignStatus.paused) && (
                  <Button
                    icon={{ path: getAssetPath("play_arrow_true.svg") }}
                    size={"sm"}
                    isLoading={isStatusLoading}
                    disabled={
                      isStatusLoading ||
                      campaign.status === CampaignStatus.pending
                    }
                    onClick={() =>
                      changeCampaignStatusHandler(CampaignStatus.active)
                    }
                  >
                    Start Campaign
                  </Button>
                )}
              {campaign.status !== CampaignStatus.completed &&
                campaign.status === CampaignStatus.active && (
                  <Button
                    icon={{ path: getAssetPath("pause.svg") }}
                    size={"sm"}
                    style={"secondary"}
                    isLoading={isStatusLoading}
                    disabled={isStatusLoading}
                    onClick={() =>
                      changeCampaignStatusHandler(CampaignStatus.paused)
                    }
                  >
                    Pause Campaign
                  </Button>
                )}
            </div>
          </div>
        </div>
        <div>{children}</div>
      </div>
      <style jsx>{`
        .layout {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          > .nav {
            height: 3.5rem;
            padding: 0 ${schemes.spacing["3xl"]} 0 0;
            border-bottom: ${schemes.borderWidth.sm} solid
              ${colors.border.dark.default};
            > div {
              display: flex;
              align-items: center;
              gap: ${schemes.spacing["3xl"]};

              > .title {
                display: flex;
                align-items: center;
                gap: ${schemes.spacing.md};
                flex-shrink: 0;
                a {
                  font-size: 0;
                }
              }
              > hr {
                width: 0;
                height: ${schemes.spacing["3xl"]};
                border: 1px solid ${colors.border.dark.default};
              }
              > .tabs {
                flex: 1;
                display: flex;
                align-items: center;
                gap: ${schemes.spacing["3xl"]};
                a {
                  position: relative;
                  color: ${colors.schems.dark.onDisabled};

                  font-size: ${fonts.body.bodyBold.fontSize};
                  font-weight: ${fonts.body.bodyBold.fontWeight};
                  line-height: 3.5rem;
                  text-decoration: none;
                  cursor: pointer;
                  &:hover,
                  &.active {
                    color: ${colors.schems.dark.onBase};
                    &:after {
                      position: absolute;
                      content: "";
                      height: 2px;
                      left: 0;
                      right: 0;
                      bottom: 0;
                      background: ${colors.schems.dark.primary};
                    }
                  }
                }
              }
              > div:last-of-type {
                flex-shrink: 0;
              }
            }
          }
          > div:last-of-type {
            flex: 1;
          }
        }
      `}</style>
    </>
  )
}
