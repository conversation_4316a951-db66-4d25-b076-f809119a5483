import React, { use<PERSON><PERSON>back, useEffect, use<PERSON>em<PERSON>, useState } from "react"
import { deleteCampaignCreators, getCampaignCreators } from "@/service/campaign"
import { useSearchParams } from "next/navigation"
import Checkbox from "@/components/elements/Checkbox2"
import { Property } from "csstype"
import Icon from "@/components/elements/Icon"
import { getAssetPath, getNumberToDollar } from "@/utils/util"
import { colors, fonts, schemes } from "@/utils/theme/style"
import ColumnCreator from "@/components/table/columns/Creator"
import Pagination from "@/components/table/Pagination"
import { campaignColumns } from "@/common/utils/table"
import Link from "next/link"
import Button from "@/components/elements/Button"
import { notifyError, notifySuccess } from "@/components/modal/Toasts"
import NotFound from "@/components/common/NotFound"
import Loading from "@/components/common/Loading"
import LabelTag from "@/components/elements/LabelTag"
import { ColorType } from "@/common/types/colorType"
import {
  campaign<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/common/enums/campaignTypes"

export default function CampaignDetailCreators({
  campaignId,
  campaign,
}: {
  campaignId: number
  campaign: any
}) {
  const searchParams = useSearchParams()
  const page = searchParams.get("page") ? Number(searchParams.get("page")) : 1
  const filter = searchParams.get("filter") || undefined
  const isCollabs = !!campaign?.campaign_target_collaboration
  const [creators, setCreators] = useState([])
  const [totalCount, setTotalCount] = useState<number>(0)
  const [perPage, setPerPage] = useState<number>(50)
  const [loading, setLoading] = useState(false)
  const [uniqueIds, setUniqueIds] = useState<string[]>([])
  const [checkAll, setCheckAll] = useState(false)
  const [isDeletedLoading, setIsDeletedLoading] = useState(false)

  const selectUniqueIds = useMemo(() => new Set(uniqueIds), [uniqueIds])
  const statusLabel = [
    {
      text: "Waiting",
      color: "gray",
    },
    {
      text: "Sent",
      color: "blue",
    },
    {
      text: "Remind",
      color: "yellow",
    },
    {
      text: "Accept",
      color: "cyan",
    },
    {
      text: "Replied",
      color: "purple",
    },
    {
      text: "Upload",
      color: "green",
    },
    {
      text: "Non Affiliate",
      color: "red",
    },
    {
      text: "Conflict",
      color: "red",
    },
  ]

  const filters = useMemo(() => {
    return isCollabs
      ? [
          CampaignFilterKey.Waiting,
          CampaignFilterKey.Sent,
          CampaignFilterKey.Accept,
          CampaignFilterKey.Replied,
          CampaignFilterKey.Conflict,
          CampaignFilterKey.Failed,
        ]
      : [
          CampaignFilterKey.Waiting,
          CampaignFilterKey.Sent,
          CampaignFilterKey.Replied,
          CampaignFilterKey.Failed,
        ]
  }, [isCollabs])

  const columns = useMemo(() => {
    const messageLength =
      campaign.campaign_messages.length - (isCollabs ? 1 : 0)
    let filterGroups: string[] = []
    switch (messageLength) {
      case 0:
        filterGroups = ["First", "Second", "Third"]
        break
      case 1:
        filterGroups = ["Second", "Third"]
        break
      case 2:
        filterGroups = ["Third"]
        break
      case 3:
        filterGroups = []
        break
    }

    if (!isCollabs) {
      filterGroups = [...filterGroups, "Accept", "Target"]
    }

    return campaignColumns.filter(
      (column) => !filterGroups.includes(column.name)
    )
  }, [campaign.campaign_messages.length, isCollabs])

  const getCampaignCreatorsHandler = useCallback(
    async (campaignId: number, filter?: string) => {
      setLoading(true)
      getCampaignCreators(campaignId, page, perPage, filter)
        .then((data) => {
          setCreators(data.creators)
          setTotalCount(data.totalCount)
        })
        .catch(console.error)
        .finally(() => setLoading(false))
    },
    [page, perPage]
  )

  const checkboxAction = useCallback(
    (uniqueId: string) => {
      const uniqueIdsSet = new Set(uniqueIds)
      if (uniqueIdsSet.has(uniqueId)) {
        uniqueIdsSet.delete(uniqueId)
      } else {
        uniqueIdsSet.add(uniqueId)
      }
      setUniqueIds([...uniqueIdsSet])
      setCheckAll(uniqueIdsSet.size === creators.length)
    },
    [creators.length, uniqueIds]
  )

  const parseDataMultilineLayout = useCallback(
    ({
      value,
      upDown = "",
      label = "",
    }: {
      value: string
      upDown?: string
      label?: string
    }) => {
      return (
        <div className={`labels ${label === "" ? "bottom" : ""}`}>
          <label>{label}</label>
          <span>{value}</span>
          <small
            className={
              upDown !== "-" && upDown.startsWith("-")
                ? "blue"
                : upDown !== "-" && Number(upDown) > 0
                  ? "red"
                  : "gray"
            }
          >
            {upDown && (
              <>
                {upDown !== "-" && Number(upDown) > 0 ? `+${upDown}` : upDown}
                {upDown !== "-" ? "%" : ""}
              </>
            )}
          </small>
        </div>
      )
    },
    []
  )

  const getKeyByValue = useCallback(
    (
      creator: any,
      value: {
        name: string
        data: string
        subData?: string
        sort: boolean
        align: string
        label?: string
        style?: "dollar" | "percent" | undefined
        maxWidth?: string
      }
    ) => {
      switch (value.name) {
        case "Creator":
          return (
            <div>
              <ColumnCreator creator={creator} />
            </div>
          )
        case "Status": {
          let labelGroup = null
          if (creator.is_collaboration_accepted) {
            labelGroup = statusLabel.find((label) => label.text === "Accept")!
          } else {
            if (
              creator.creator_check_status ===
              campaignCreatorCheckStatus.NOT_AFFILIATE_USER
            ) {
              labelGroup = statusLabel.find(
                (label) => label.text === "Non Affiliate"
              )!
            } else if (
              creator.creator_check_status ===
              campaignCreatorCheckStatus.TARGET_COLLABORATION_CONFLICT
            ) {
              labelGroup = statusLabel.find(
                (label) => label.text === "Conflict"
              )!
            } else {
              if (creator.replied) {
                labelGroup = statusLabel.find(
                  (label) => label.text === "Replied"
                )!
              } else {
                if (creator.messages.length === 1) {
                  labelGroup = statusLabel.find(
                    (label) => label.text === "Sent"
                  )!
                } else if (creator.messages.length > 1) {
                  labelGroup = statusLabel.find(
                    (label) => label.text === "Remind"
                  )!
                } else {
                  labelGroup = statusLabel.find(
                    (label) => label.text === "Waiting"
                  )!
                }
              }
            }
          }

          return (
            <LabelTag type={"status"} color={labelGroup.color as ColorType}>
              {labelGroup.text}
            </LabelTag>
          )
        }
        case "Target":
          if (isCollabs && creator.messages[0])
            return (
              <Icon
                path={getAssetPath("check_circle.svg")}
                color={colors.schems.dark.primary}
              />
            )
          else return <Icon path={getAssetPath("radio_false.svg")} />
        case "First":
          if (
            (isCollabs && creator.messages[1]) ||
            (!isCollabs && creator.messages[0])
          )
            return (
              <Icon
                path={getAssetPath("check_circle.svg")}
                color={colors.schems.dark.primary}
              />
            )
          else return <Icon path={getAssetPath("radio_false.svg")} />

        case "Second":
          if (
            (isCollabs && creator.messages[2]) ||
            (!isCollabs && creator.messages[1])
          )
            return (
              <Icon
                path={getAssetPath("check_circle.svg")}
                color={colors.schems.dark.primary}
              />
            )
          else return <Icon path={getAssetPath("radio_false.svg")} />
        case "Third":
          if (
            (isCollabs && creator.messages[3]) ||
            (!isCollabs && creator.messages[2])
          )
            return (
              <Icon
                path={getAssetPath("check_circle.svg")}
                color={colors.schems.dark.primary}
              />
            )
          else return <Icon path={getAssetPath("radio_false.svg")} />
        case "Accept":
          if (isCollabs && creator.is_collaboration_accepted)
            return (
              <Icon
                path={getAssetPath("check_circle.svg")}
                color={colors.schems.dark.primary}
              />
            )
          else return <Icon path={getAssetPath("radio_false.svg")} />
        case "Replied":
          if (creator.replied)
            return (
              <Icon
                path={getAssetPath("check_circle.svg")}
                color={colors.schems.dark.primary}
              />
            )
          else return <Icon path={getAssetPath("radio_false.svg")} />
        case "GMV":
        case "GPM":
        case "Video GPM":
        case "Live GMV":
          return parseDataMultilineLayout({
            value:
              value.style === "dollar"
                ? getNumberToDollar(creator[value.data])
                : `${creator[value.data].toLocaleString()}${value.style === "percent" ? "%" : ""}`,
            upDown: (value.subData && creator[value.subData]) ?? "",
            label: value.label ?? "",
          })
        default:
          return !creator[value.data]
            ? "-"
            : value.style === "dollar"
              ? getNumberToDollar(creator[value.data] || 0)
              : `${typeof creator[value.data] === "number" ? creator[value.data]?.toLocaleString() || 0 : creator[value.data] || 0}${value.style === "percent" ? "%" : ""}`
      }
    },
    []
  )

  const actionCheckAll = (checked: boolean) => {
    if (checked) {
      setUniqueIds(
        creators
          .filter((creator: any) => !creator.is_waiting_analysis)
          .map((creator: any) => creator.unique_id)
      )
    } else {
      setUniqueIds([])
    }
    setCheckAll(checked)
  }

  const deleteCreatorHandler = async () => {
    setIsDeletedLoading(true)
    const creatorsCount = uniqueIds.length
    deleteCampaignCreators(campaignId, uniqueIds)
      .then(async () => {
        notifySuccess(`Successfully ${creatorsCount} creators deleted`)
        setUniqueIds([])
        await getCampaignCreatorsHandler(campaignId)
      })
      .catch((e) => {
        console.log(e.message)
        notifyError(e.message)
      })
      .finally(() => setIsDeletedLoading(false))
  }

  useEffect(() => {
    getCampaignCreatorsHandler(campaignId, filter).then()
  }, [campaignId, getCampaignCreatorsHandler, filter, page, perPage])

  return (
    <>
      <div className={"layout"}>
        <div className={"head"}>
          {selectUniqueIds.size > 0 && (
            <div className={"selected"}>
              <span>{selectUniqueIds.size} Selected</span>
              <Button
                style={"secondary"}
                size={"sm"}
                onClick={deleteCreatorHandler}
                isLoading={isDeletedLoading}
                disabled={isDeletedLoading}
              >
                Delete
              </Button>
            </div>
          )}
          <div className={"filters"}>
            <Link
              href={`/campaign/${campaignId}`}
              className={!filter ? "active" : ""}
            >
              <span>All</span>
              <span className={"badge"}>{campaign.creator_count}</span>
            </Link>
            <hr />
            {filters.map((value, index) => {
              return (
                <Link
                  href={`/campaign/${campaignId}?filter=${value}`}
                  className={value === filter ? "active" : ""}
                  key={index}
                >
                  <span>{value}</span>
                  <span className={"badge"}>
                    {campaign?.[`${value?.toLowerCase()}_count`]}
                  </span>
                </Link>
              )
            })}
          </div>
        </div>
        <div className={"table"}>
          <div className="creator-table">
            <table>
              <thead>
                <tr>
                  <th className={"checkbox"}>
                    <Checkbox
                      color={
                        checkAll || selectUniqueIds.size > 0 ? "blue" : "gray"
                      }
                      onChange={(e) => {
                        actionCheckAll(e.target.checked)
                      }}
                      isChecked={checkAll}
                      indeterminate={selectUniqueIds.size > 0}
                    />
                  </th>
                  {columns.map((value, index) => (
                    <th
                      key={index}
                      data-sort={value.sort}
                      data-column={value.data}
                      style={{
                        textAlign: value.align as
                          | Property.TextAlign
                          | undefined,
                        borderRight:
                          value?.border === "right"
                            ? `1px solid ${colors.border.dark.default}`
                            : "none",
                        borderLeft:
                          value?.border === "left"
                            ? `1px solid ${colors.border.dark.default}`
                            : "none",
                        paddingLeft:
                          value?.align === "center" ? "1rem" : "1.5rem",
                      }}
                      className={`${index === 0 ? `profile` : ""} ${value.sort ? "sortable" : ""}`}
                    >
                      {value.name}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {creators.length > 0 &&
                  !loading &&
                  creators.map((creator: any) => (
                    <tr
                      key={creator.unique_id}
                      className={`${[campaignCreatorCheckStatus.NOT_AFFILIATE_USER, campaignCreatorCheckStatus.TARGET_COLLABORATION_CONFLICT].includes(creator.creator_check_status) ? "disable" : ""}`}
                    >
                      <td
                        className={"checkbox"}
                        onClick={(e) => e.stopPropagation()}
                      >
                        {creator.is_waiting_analysis ? (
                          <Icon
                            path={getAssetPath("loading.svg")}
                            color={colors.schems.dark.primary}
                            size={"1.25rem"}
                            isSpin={true}
                          />
                        ) : (
                          <Checkbox
                            color={
                              selectUniqueIds?.has(creator.unique_id)
                                ? "blue"
                                : "gray"
                            }
                            onChange={() => {
                              checkboxAction(creator.unique_id)
                            }}
                            isChecked={
                              selectUniqueIds?.has(creator.unique_id) || false
                            }
                          />
                        )}
                      </td>
                      {columns.map((value, index) => (
                        <td
                          key={index}
                          style={{
                            textAlign: `${value.align}` as
                              | Property.TextAlign
                              | undefined,
                            borderRight:
                              value?.border === "right"
                                ? `1px solid ${colors.border.dark.default}`
                                : "none",
                            borderLeft:
                              value?.border === "left"
                                ? `1px solid ${colors.border.dark.default}`
                                : "none",
                            paddingLeft:
                              value?.align === "center" ? "1rem" : "1.5rem",
                          }}
                          className={`${index === 0 ? `profile ` : ""}`}
                        >
                          {creator && getKeyByValue(creator, value)}
                        </td>
                      ))}
                    </tr>
                  ))}
              </tbody>
            </table>
            {!creators.length && !loading && (
              <div className={"not-found"}>
                <NotFound />
              </div>
            )}
            {loading && (
              <div className={"loading"}>
                <Loading />
              </div>
            )}
          </div>
        </div>
        <div className={"foot"}>
          <Pagination
            currentPage={page}
            totalCount={totalCount}
            perPage={perPage}
            setPerPage={setPerPage}
          />
        </div>
      </div>
      <style jsx>{`
        .layout {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: calc(100vh - 3.5rem);
        }
        .head {
          width: 100%;
          display: flex;
          justify-content: start;
          border-bottom: ${schemes.borderWidth.sm} solid
            ${colors.border.dark.default};
          > div {
            height: 3.5rem;
            &.selected {
              padding: 0 ${schemes.spacing["3xl"]};
              display: flex;
              align-items: center;
              gap: ${schemes.spacing.md};
              align-self: stretch;
              border-right: 1px solid ${colors.border.dark.default};
              background: ${colors.bg.dark.surface};
              > span {
                color: ${colors.schems.dark.onDisabled};
                font-size: ${fonts.body.body.fontSize};
                font-weight: ${fonts.body.body.fontWeight};
                line-height: ${fonts.body.body.lineHeight};
              }
            }
            &.filters {
              padding: 0 ${schemes.spacing["3xl"]};
              display: flex;
              align-items: center;
              gap: ${schemes.spacing.md};
              > hr {
                width: 0;
                height: ${schemes.spacing["3xl"]};
                border: 1px solid ${colors.border.dark.default};
              }
              a {
                text-decoration: none;
                height: 2rem;
                padding: ${schemes.spacing.md};
                display: flex;
                align-items: center;
                gap: ${schemes.spacing.md};
                border-radius: ${schemes.radius.sm};
                border: ${schemes.borderWidth.sm} solid
                  ${colors.border.dark.bgLighten};
                > span {
                  padding: 0 ${schemes.spacing.sm};
                  color: ${colors.schems.dark.onBase};
                  &.badge {
                    min-width: 1.5rem;
                    text-align: center;
                    font-size: ${fonts.body.bodyBold.fontSize};
                    font-weight: ${fonts.body.bodyBold.fontWeight};
                    line-height: 1.5rem;
                    border-radius: ${schemes.radius.rounded};
                    background: ${colors.schems.dark.secondary};
                  }
                }
                &.active {
                  background: ${colors.schems.dark.secondary};
                  > span {
                    color: ${colors.schems.dark.onSecondary};
                  }
                }
              }
            }
          }
        }
        .table {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          max-height: calc(100vh - 10.5rem);
          > .creator-table {
            width: 100%;
            height: 100%;
            white-space: nowrap;
            overflow: scroll;
            -ms-overflow-style: none;
            &::-webkit-scrollbar {
              display: none;
            }
            & > table {
              border-collapse: separate;
              border-spacing: 0;
              min-width: 100%;
              table-layout: fixed;
              background: ${colors.schems.dark.base};
              td,
              th {
                padding: 0 1rem 0 1.5rem;
                border-bottom: ${schemes.borderWidth.sm} solid
                  ${colors.border.dark.default};
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                &.checkbox {
                  width: 4rem;
                  position: sticky;
                  left: 0;
                  background: ${colors.schems.dark.base};
                  z-index: 10;
                }

                &.profile {
                  width: 15rem;
                  min-width: 15rem;
                  max-width: 15rem;
                  position: sticky;
                  left: 4rem;
                  background: ${colors.schems.dark.base};
                  border-right: ${schemes.borderWidth.sm} solid
                    ${colors.border.dark.default};
                  z-index: 10;
                }

                &.save-button {
                  position: sticky;
                  right: 0;
                  padding: 0 0 0 2.5rem;
                  min-width: 134px;
                  overflow: visible;
                }
              }
              & thead {
                z-index: 11;
                position: sticky;
                left: 0;
                right: 0;
                top: 0;
                table-layout: fixed;
                background: black;
                & th {
                  height: 2.5rem;
                  color: ${colors.schems.dark.onDisabled};
                  font-size: ${fonts.body.body.fontSize};
                  font-weight: ${fonts.body.body.fontWeight};
                  line-height: ${fonts.body.body.lineHeight};
                  position: relative;

                  &.sortable {
                    cursor: pointer;
                    padding: 0 2rem 0 1.5rem;
                    &.selected {
                      background-color: ${colors.bg.dark.surface};
                    }
                    &.profile::after {
                      right: 8px;
                    }
                    &.asc::after {
                      border-top: none;
                      border-bottom: 5px solid ${colors.schems.dark.disabled};
                      border-left: 5px solid transparent;
                      border-right: 5px solid transparent;
                      background: none;
                      width: 0;
                      height: 0;
                    }

                    &.desc::after {
                      border-bottom: none;
                      border-top: 5px solid ${colors.schems.dark.disabled};
                      border-left: 5px solid transparent;
                      border-right: 5px solid transparent;
                      background: none;
                      width: 0;
                      height: 0;
                    }
                    &::after {
                      content: "";
                      position: absolute;
                      right: 10px;
                      top: 50%;
                      transform: translateY(-50%);
                      width: 1rem;
                      height: 1rem;
                      background-position: center;
                      background-repeat: no-repeat;
                      background-size: contain;
                      background-image: url(${getAssetPath("unfold_more.svg")});
                      transition: all 0.2s ease;
                    }
                  }
                }
              }
              & tbody {
                & tr {
                  &.disable {
                    > td {
                      background: #2c2c2c;
                      color: ${colors.schems.dark.onDisabled};
                      h4 {
                        color: ${colors.schems.dark.onDisabled};
                      }
                      img {
                        filter: grayscale(1);
                      }
                      &:not(.checkbox) {
                        i {
                          display: none;
                        }
                      }
                    }
                  }
                  &:hover:not(.disable) {
                    background: ${colors.bg.dark.surface};
                    & td {
                      &.save-button {
                        z-index: 30;
                      }
                      &.checkbox,
                      &.profile {
                        background: ${colors.bg.dark.surface};
                      }
                      & .button {
                        opacity: 1;
                      }
                    }
                  }
                  > td {
                    height: 6rem;
                    color: ${colors.schems.dark.onPrimary};
                    font-size: ${fonts.body.body.fontSize};
                    font-weight: ${fonts.body.body.fontWeight};
                    line-height: ${fonts.body.body.lineHeight};
                    padding: ${schemes.spacing.md} ${schemes.spacing["2xl"]}
                      ${schemes.spacing.md} ${schemes.spacing["3xl"]};
                    > .button {
                      position: relative;
                      background: ${colors.bg.dark.surface};
                      opacity: 0;
                      transition: opacity 0.3s ease;
                      padding-right: 10px;
                      height: 6rem;
                      display: flex;
                      justify-content: right;
                      align-items: center;
                      gap: ${schemes.spacing.md};
                      &:after {
                        position: absolute;
                        content: "";
                        left: -2.5rem;
                        width: 2.5rem;
                        top: 0;
                        bottom: 0;

                        background: linear-gradient(
                          270deg,
                          ${colors.bg.dark.surface} 0%,
                          rgba(24, 24, 27, 0) 100%
                        );
                      }
                    }

                    &:has(div.contents),
                    &:has(div.products) {
                      overflow: visible;
                    }
                    &:has(div.custom-tag) {
                      padding: 0;
                      overflow: visible;
                    }
                    & > .custom-tag {
                      max-width: 25rem;
                      height: 100%;
                      border-right: ${schemes.borderWidth.sm} solid
                        ${colors.border.dark.default};
                    }
                    & .labels {
                      height: 100%;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      & > * {
                        text-align: right;
                      }
                      & label,
                      & small {
                        color: ${colors.schems.dark.disabled};
                        font-size: ${fonts.body.label.fontSize};
                        font-weight: ${fonts.body.label.fontWeight};
                        line-height: ${fonts.body.label.lineHeight};
                        height: 21px;
                      }
                      & span {
                        font-size: ${fonts.body.body.fontSize};
                        font-weight: ${fonts.body.body.fontWeight};
                        line-height: ${fonts.body.body.lineHeight};
                      }
                      & small {
                        &.red {
                          color: ${colors.sys.dark.red.default};
                        }
                        &.blue {
                          color: ${colors.sys.dark.blue.default};
                        }
                      }
                    }
                    & .tags {
                      display: flex;
                      gap: ${schemes.spacing.md};

                      overflow-x: auto;
                      -ms-overflow-style: none;
                      &::-webkit-scrollbar {
                        display: none;
                      }
                    }
                  }
                }
              }
            }
            & > .not-found,
            & > .loading {
              width: 100%;
              height: calc(100vh - (3.5rem * 3 + 2.5rem));
            }
          }
        }
        .foot {
          flex-shrink: 0;
          border-top: ${schemes.borderWidth.md} solid
            ${colors.border.dark.default};
          background: ${colors.schems.dark.base};
          height: 3.5rem;
        }
      `}</style>
    </>
  )
}
