import { ReactNode } from "react"
import { fonts, schemes } from "@/utils/theme/style"

export default function LabelBox({
  label,
  labelFor,
  children,
}: {
  label: string
  labelFor?: string
  children: ReactNode
}) {
  return (
    <>
      <div>
        <label htmlFor={labelFor}>{label}</label>
        {children}
      </div>
      <style jsx>{`
        label {
          display: block;
          margin-bottom: ${schemes.spacing.sm};
          font-size: ${fonts.body.label.fontSize};
          font-weight: ${fonts.body.label.fontWeight};
          line-height: ${fonts.body.label.lineHeight};
        }
      `}</style>
    </>
  )
}
