import React, { use<PERSON><PERSON>back, useEffect, useState } from "react"
import PhoneInput from "react-phone-number-input"
import "react-phone-number-input/style.css"
import { colors, fonts, schemes } from "@/utils/theme/style"
import Input from "@/components/elements/Input"
import Button from "@/components/elements/Button"
import { getAssetPath } from "@/utils/util"
import Icon from "@/components/elements/Icon"

export const Profile = ({
  setStep,
  fullName,
  setFullName,
  jobTitle,
  setJobTitle,
  phoneNumber,
  setPhoneNumber,
}: {
  setStep: (step: number) => void
  fullName: string
  setFullName: (name: string) => void
  jobTitle: string
  setJobTitle: (job: string) => void
  phoneNumber: string
  setPhoneNumber: (phone: string) => void
}) => {
  const [disabled, setDisabled] = useState<boolean>(true)
  const [fullNameHelper, setFullNameHelper] = useState<string | null>(null)
  const [jobTitleHelper, setJobTitleHelper] = useState<string | null>(null)
  const [phoneNumberHelper, setPhoneNumberHelper] = useState<string | null>(
    null
  )
  const nameFormat = /[^ 가-힣A-Za-z]/
  const jobFormat = /^[0-9 ]+$/

  useEffect(() => {
    if (
      fullName &&
      !nameFormat.test(fullName) &&
      jobTitle &&
      !jobFormat.test(jobTitle) &&
      phoneNumber?.length
    ) {
      setDisabled(false)
    } else {
      setDisabled(true)
    }
  }, [fullName, jobTitle, phoneNumber])

  const nextStep = () => {
    setStep(1)
  }

  const fullNameFocusEvent = useCallback(() => {
    setFullNameHelper(null)
  }, [])

  const fullNameBlurEvent = useCallback(() => {
    if (!fullName.length) {
      setFullNameHelper("Please enter your full name.")
    } else if (fullName.length < 3) {
      setFullNameHelper("Name must be at least 2 characters.")
    } else if (nameFormat.test(fullName)) {
      setFullNameHelper("Name cannot include numbers or special characters.")
    } else {
      setFullNameHelper(null)
    }
  }, [fullName])

  const jobTitleFocusEvent = useCallback(() => {
    setJobTitleHelper(null)
  }, [])

  const jobTitleBlurEvent = useCallback(() => {
    if (!jobTitle.length) {
      setJobTitleHelper("Please enter your job title.")
    } else if (jobTitle.length < 3) {
      setJobTitleHelper("Job title must be at least 2 characters.")
    } else if (jobFormat.test(jobTitle)) {
      setJobTitleHelper("Job title cannot be only numbers.")
    } else {
      setJobTitleHelper(null)
    }
  }, [jobTitle])

  const phoneNumberFocusEvent = useCallback(() => {
    setPhoneNumberHelper(null)
  }, [])

  const phoneNumberBlurEvent = useCallback(() => {
    if (!phoneNumber?.length) {
      console.log("Please enter your phone number.")
      setPhoneNumberHelper("Please enter your phone number.")
    } else {
      setPhoneNumberHelper(null)
    }
  }, [phoneNumber])

  return (
    <>
      <div className="profile">
        <div>
          <h2>
            Tell us about
            <br />
            your profile
          </h2>
        </div>
        <div>
          <div>
            <Input
              type={"text"}
              value={fullName}
              placeholderText={"Mark"}
              status={fullNameHelper ? "error" : "default"}
              onChange={setFullName}
              label={{ text: "Full name" }}
              helper={fullNameHelper ? { text: fullNameHelper } : undefined}
              onFocus={fullNameFocusEvent}
              onBlur={fullNameBlurEvent}
            />
          </div>
          <div>
            <Input
              type={"text"}
              value={jobTitle}
              placeholderText={"e.g Director of Marketing"}
              status={jobTitleHelper ? "error" : "default"}
              onChange={setJobTitle}
              label={{ text: "Job title" }}
              helper={jobTitleHelper ? { text: jobTitleHelper } : undefined}
              onFocus={jobTitleFocusEvent}
              onBlur={jobTitleBlurEvent}
            />
          </div>
          <div
            className={`phone-number-input ${phoneNumberHelper ? "error" : ""}`}
          >
            <label>Phone number</label>
            <PhoneInput
              defaultCountry="US"
              id="phoneNumber"
              placeholder="Enter phone number"
              value={phoneNumber}
              // @ts-expect-error: Not an error
              onChange={setPhoneNumber}
              onFocus={phoneNumberFocusEvent}
              onBlur={phoneNumberBlurEvent}
            />
            {phoneNumberHelper && (
              <div className={"helper"}>
                <Icon
                  path={getAssetPath("cancel.svg")}
                  color={colors.sys.dark.red.default}
                  size={"1rem"}
                />
                <p>{phoneNumberHelper}</p>
              </div>
            )}
          </div>
        </div>
        <div>
          <Button
            type="button"
            style={"secondary"}
            onClick={nextStep}
            full={true}
            disabled={disabled}
          >
            Continue
          </Button>
        </div>
      </div>
      <style jsx>{`
        div.profile {
          display: flex;
          flex-direction: column;
          gap: ${schemes.spacing["8xl"]};
          & > div:nth-of-type(2) {
            display: flex;
            flex-direction: column;
            gap: ${schemes.spacing["2xl"]};
          }
        }
        h2 {
          font-size: ${fonts.heading.h2.fontSize};
          font-weight: ${fonts.heading.h2.fontWeight};
          line-height: ${fonts.heading.h2.lineHeight};
        }
        .phone-number-input {
          label {
            display: block;
            margin-bottom: ${schemes.spacing.sm};
            font-size: ${fonts.body.label.fontSize};
            font-weight: ${fonts.body.label.fontWeight};
            line-height: ${fonts.body.label.lineHeight};
          }
          input {
            outline: none;
            border: 1px solid ${colors.border.dark.default};
            border-radius: ${schemes.radius.sm};
            box-shadow: none;
            background: ${colors.interactive.dark.hover};
            caret-color: ${colors.border.dark.primary};

            color: ${colors.schems.dark.onBase};
            font-weight: ${fonts.body.body.fontWeight};
            line-height: ${fonts.body.body.lineHeight};

            &:-webkit-autofill::placeholder,
            &::placeholder {
              overflow: hidden;
              color: ${colors.schems.dark.onDisabled};
              text-overflow: ellipsis;
              font-size: ${fonts.body.body.fontSize};
              font-weight: ${fonts.body.body.fontWeight};
              line-height: ${fonts.body.body.lineHeight};
            }

            &:focus-visible {
              border-color: ${colors.border.dark.primary};
            }

            &:-webkit-autofill,
            &:-webkit-autofill:hover,
            &:-webkit-autofill:focus-visible {
              -webkit-text-fill-color: ${colors.schems.dark.onBase};
              -webkit-box-shadow: 0 0 0 0 ${colors.interactive.dark.hover} inset;
              transition: background-color 5000s ease-in-out 0s;
            }
            /* Chrome, Safari, Edge, Opera */
            &::-webkit-outer-spin-button,
            &::-webkit-inner-spin-button {
              -webkit-appearance: none;
              margin: 0;
            }

            /* Firefox  */
            &[type="number"] {
              -moz-appearance: textfield;
            }

            &:disabled {
              background: ${colors.schems.dark.disabled};
              color: ${colors.schems.dark.onDisabled};
            }
          }
          .helper {
            margin-top: ${schemes.spacing.sm};
            display: flex;
            justify-content: left;
            align-items: center;
            gap: ${schemes.spacing.sm};
            p {
              margin: 0;
              color: ${colors.sys.dark.red.default};
              font-size: ${fonts.body.label.fontSize};
              font-weight: ${fonts.body.label.fontWeight};
              line-height: ${fonts.body.label.lineHeight};
            }
          }
        }
      `}</style>
      <style jsx global={true}>{`
        .PhoneInput {
          & .PhoneInputCountry {
            height: 2.5rem;
            padding: 0 ${schemes.spacing.xl};
            border-radius: ${schemes.radius.sm};
            border: 1px solid ${colors.border.dark.default};
            background: ${colors.bg.dark.surface};
            & .PhoneInputCountrySelectArrow {
              margin-left: ${schemes.spacing["2xl"]};
            }
          }
          & > input {
            height: 2.5rem;
            padding: 0 ${schemes.spacing.xl};
            margin-left: ${schemes.spacing["2xl"]};
            border-radius: ${schemes.radius.sm};
            border: 1px solid ${colors.border.dark.default};
            background: ${colors.bg.dark.surface};
            &:-webkit-autofill,
            &:-webkit-autofill:hover,
            &:-webkit-autofill:focus-visible {
              -webkit-text-fill-color: ${colors.schems.dark.onBase};
              -webkit-box-shadow: 0 0 0 0 ${colors.interactive.dark.hover} inset;
              transition: background-color 5000s ease-in-out 0s;
            }
          }
        }
        .phone-number-input.error {
          .PhoneInput > input {
            border: 1px solid ${colors.sys.dark.red.default} !important;
          }
        }
      `}</style>
    </>
  )
}
