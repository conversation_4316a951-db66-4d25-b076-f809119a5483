import React from "react"
import { schemes } from "@/utils/theme/style"

export default function OnboardingFrame({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className={"content"}>
      <div>
        <div>
          <div></div>
        </div>
        <div>{children}</div>
      </div>
      <style jsx>{`
        .content {
          height: 100vh;
          & > div {
            display: flex;
            height: 100%;
            gap: 0.625rem;
            & > div {
              width: 50%;
              padding: ${schemes.spacing["3xl"]};
              & > div {
                width: 100%;
                height: 100%;
                background: url("/images/allsale/onboarding.png") center/contain
                  no-repeat;
              }
            }
          }
        }
      `}</style>
    </div>
  )
}
