import React, { useCallback, useEffect, useState } from "react"
import Button from "@/components/elements/Button"
import { fonts, schemes } from "@/utils/theme/style"
import Input from "@/components/elements/Input"
import InputButtons from "@/components/elements/InputButtons"
import SelectBox from "@/components/elements/SelectBox"

const companySizes = ["1-5", "6-10", "11-20", "21-50", "51-100", "101+"]

export const Business = ({
  companyName,
  setCompanyName,
  industry,
  setIndustry,
  companySize,
  setCompanySize,
  onboardingSubmit,
  isSubmit,
}: {
  companyName: string
  setCompanyName: (name: string) => void
  industry: string
  setIndustry: (industry: string) => void
  companySize: string
  setCompanySize: (size: string) => void
  onboardingSubmit: () => void
  isSubmit: boolean
}) => {
  const [disabled, setDisabled] = useState<boolean>(true)
  const [companyNameHelper, setCompanyNameHelper] = useState<string | null>(
    null
  )
  const companyNameFormat = /[^ 가-힣A-Za-z]/

  useEffect(() => {
    if (
      companyName &&
      !companyNameFormat.test(companyName) &&
      industry &&
      companySize
    ) {
      setDisabled(false)
    } else {
      setDisabled(true)
    }
  }, [companyName, industry, companySize])

  const changeCompanySize = (data: any) => {
    if (data.checked) {
      setCompanySize(data.data)
    }
  }

  const companyNameFocusEvent = useCallback(() => {
    setCompanyNameHelper(null)
  }, [])

  const companyNameBlurEvent = useCallback(() => {
    if (!companyName.length) {
      setCompanyNameHelper("Please enter your company name.")
    } else if (companyName.length < 3) {
      setCompanyNameHelper("Company name must be at least 2 characters.")
    } else if (companyNameFormat.test(companyName)) {
      setCompanyNameHelper(
        "Company name cannot include numbers or special characters."
      )
    } else {
      setCompanyNameHelper(null)
    }
  }, [companyName])

  return (
    <>
      <div className="business">
        <div>
          <h2>
            Tell us about <br /> your company.
          </h2>
        </div>
        <div>
          <div>
            <Input
              type={"text"}
              value={companyName}
              placeholderText={"Your Company"}
              status={companyNameHelper ? "error" : "default"}
              isRequired={true}
              onChange={setCompanyName}
              label={{ text: "Company name" }}
              helper={
                companyNameHelper ? { text: companyNameHelper } : undefined
              }
              onFocus={companyNameFocusEvent}
              onBlur={companyNameBlurEvent}
            />
          </div>
          <div>
            <SelectBox
              data={[
                "Beauty & Personal Care",
                "Marketing & Advertising",
                "Fashion & Apparel",
                "Health & Wellness",
                "E-commerce & Retail",
                "SaaS & Tech",
                "Other",
              ]}
              isFull={true}
              action={setIndustry}
              defaultValue={industry}
              label={"Industry"}
            />
          </div>
          <div>
            <label htmlFor="id">Company size</label>
            <div>
              <InputButtons
                data={companySizes}
                action={changeCompanySize}
                checkValues={companySize}
              />
            </div>
          </div>
        </div>

        <div>
          <Button
            full={true}
            onClick={onboardingSubmit}
            isLoading={isSubmit}
            disabled={isSubmit || disabled}
          >
            Continue
          </Button>
        </div>
      </div>
      <style jsx>{`
        div.business {
          display: flex;
          flex-direction: column;
          gap: ${schemes.spacing["8xl"]};
          & > div:nth-of-type(2) {
            display: flex;
            flex-direction: column;
            gap: ${schemes.spacing["2xl"]};
          }
        }
        h2 {
          font-size: ${fonts.heading.h2.fontSize};
          font-weight: ${fonts.heading.h2.fontWeight};
          line-height: ${fonts.heading.h2.lineHeight};
        }
        label {
          display: block;
          margin-bottom: ${schemes.spacing.sm};
          font-size: ${fonts.body.label.fontSize};
          font-weight: ${fonts.body.label.fontWeight};
          line-height: ${fonts.body.label.lineHeight};
        }
      `}</style>
    </>
  )
}
