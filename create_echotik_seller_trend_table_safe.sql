-- EchoTik Seller Trend API 데이터 저장용 테이블 생성 (안전한 버전)
-- brand 스키마에 echotik_seller_trend_daily 테이블 생성

CREATE TABLE IF NOT EXISTS brand."echotik-seller-trend-daily" (
    id BIGSERIAL PRIMARY KEY,
    
    -- API 기본 정보
    dt DATE NOT NULL,  -- API에서 "dt" 필드명 사용
    seller_id TEXT NOT NULL,
    user_id TEXT,  -- 새로 추가된 필드
    
    -- 총 판매 데이터 (누적)
    total_sale_cnt INTEGER DEFAULT 0,
    total_sale_gmv_amt DECIMAL(15,2) DEFAULT 0,
    
    -- 일별 판매 데이터
    total_sale_1d_cnt INTEGER DEFAULT 0,
    total_sale_gmv_1d_amt DECIMAL(15,2) DEFAULT 0,
    
    -- 비디오/라이브 데이터  
    total_video_cnt INTEGER DEFAULT 0,
    total_live_cnt INTEGER DEFAULT 0,
    
    -- 상품 데이터
    total_product_cnt INTEGER DEFAULT 0,
    total_crawl_product_cnt INTEGER DEFAULT 0,
    
    -- 인플루언서 데이터 (비디오/라이브별 세분화)
    total_video_ifl_cnt INTEGER DEFAULT 0,
    total_live_ifl_cnt INTEGER DEFAULT 0,
    
    -- 메타데이터
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 중복 방지를 위한 UNIQUE 제약
    UNIQUE(seller_id, dt)
);

-- 인덱스 생성 (빠른 조회를 위해)
CREATE INDEX IF NOT EXISTS idx_echotik_seller_trend_seller_date 
ON brand."echotik-seller-trend-daily"(seller_id, dt);

CREATE INDEX IF NOT EXISTS idx_echotik_seller_trend_date 
ON brand."echotik-seller-trend-daily"(dt);

CREATE INDEX IF NOT EXISTS idx_echotik_seller_trend_user_id 
ON brand."echotik-seller-trend-daily"(user_id);

-- updated_at 자동 업데이트 트리거 함수 (없다면 생성)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- updated_at 트리거 설정 (DROP 없이 직접 생성)
CREATE TRIGGER update_echotik_seller_trend_updated_at
    BEFORE UPDATE ON brand."echotik-seller-trend-daily"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 테이블에 대한 설명 추가
COMMENT ON TABLE brand."echotik-seller-trend-daily" IS 'EchoTik Seller Trend API에서 수집한 일별 판매자 트렌드 데이터 (완전한 API 응답 구조)';

-- 컬럼 설명
COMMENT ON COLUMN brand."echotik-seller-trend-daily".dt IS '데이터 날짜 (API의 dt 필드)';
COMMENT ON COLUMN brand."echotik-seller-trend-daily".seller_id IS 'EchoTik 판매자 ID';
COMMENT ON COLUMN brand."echotik-seller-trend-daily".user_id IS 'TikTok 사용자 ID';

-- 누적 판매 데이터
COMMENT ON COLUMN brand."echotik-seller-trend-daily".total_sale_cnt IS '총 판매 건수 (누적)';
COMMENT ON COLUMN brand."echotik-seller-trend-daily".total_sale_gmv_amt IS '총 판매 GMV (누적)';

-- 일별 판매 데이터
COMMENT ON COLUMN brand."echotik-seller-trend-daily".total_sale_1d_cnt IS '일별 판매 건수';
COMMENT ON COLUMN brand."echotik-seller-trend-daily".total_sale_gmv_1d_amt IS '일별 판매 GMV';

-- 콘텐츠 데이터
COMMENT ON COLUMN brand."echotik-seller-trend-daily".total_video_cnt IS '총 비디오 수';
COMMENT ON COLUMN brand."echotik-seller-trend-daily".total_live_cnt IS '총 라이브 수';

-- 상품 데이터
COMMENT ON COLUMN brand."echotik-seller-trend-daily".total_product_cnt IS '총 상품 수 (활성)';
COMMENT ON COLUMN brand."echotik-seller-trend-daily".total_crawl_product_cnt IS '총 크롤링된 상품 수';

-- 인플루언서 데이터
COMMENT ON COLUMN brand."echotik-seller-trend-daily".total_video_ifl_cnt IS '비디오 관련 인플루언서 수';
COMMENT ON COLUMN brand."echotik-seller-trend-daily".total_live_ifl_cnt IS '라이브 관련 인플루언서 수';

-- 메타데이터
COMMENT ON COLUMN brand."echotik-seller-trend-daily".created_at IS '레코드 생성 시간';
COMMENT ON COLUMN brand."echotik-seller-trend-daily".updated_at IS '레코드 업데이트 시간'; 