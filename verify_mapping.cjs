const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://jqfijmjvfgzgghvjbwxc.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpxZmlqbWp2Zmd6Z2dodmpid3hjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDI1NjUyMDIsImV4cCI6MjAxODE0MTIwMn0.JItd7grxUM0vo_h-UWc6Z7KuGusTbFK0qQPEYQjrUaU'
);

async function verifyJuneMapping() {
  try {
    console.log('🔍 6월 데이터 매핑 검증 시작');
    
    const { data, error } = await supabase
      .schema('brand')
      .from('competitor-dashboard-daily')
      .select('*')
      .gte('date', '2025-06-01')
      .lt('date', '2025-07-01')
      .order('date', { ascending: true });
    
    if (error) {
      console.error('❌ Supabase Error:', error);
      return;
    }
    
    console.log('📊 6월 총 데이터 건수:', data.length);
    
    // 6월 전체 월별 GMV 집계
    console.log('\n📈 실제 Supabase 6월 GMV 집계:');
    const monthlyGMV = {};
    data.forEach(item => {
      if (!monthlyGMV[item.brand_name]) {
        monthlyGMV[item.brand_name] = 0;
      }
      monthlyGMV[item.brand_name] += item.gmv;
    });
    
    Object.keys(monthlyGMV).forEach(brand => {
      console.log(`${brand}: $${Math.ceil(monthlyGMV[brand]).toLocaleString()}`);
    });
    
    // 현재 화면에 표시된 데이터와 비교
    console.log('\n🎯 화면 표시 데이터와 비교:');
    const screenData = {
      'Skin1004 US': 25554,
      'medicube US Store': 2393138,
      'COSRX US': 66958,
      'Dr.Melaxin Global': 1001825,
      'Anua Store US': 1259482,
      'Beauty of Joseon US': 325640
    };
    
    console.log('\n화면 데이터:');
    Object.keys(screenData).forEach(brand => {
      console.log(`${brand}: $${screenData[brand].toLocaleString()}`);
    });
    
    console.log('\n📋 매핑 검증 결과:');
    Object.keys(screenData).forEach(brand => {
      const supabaseValue = Math.ceil(monthlyGMV[brand] || 0);
      const screenValue = screenData[brand];
      const match = supabaseValue === screenValue;
      console.log(`${brand}: ${match ? '✅' : '❌'} (Supabase: $${supabaseValue.toLocaleString()}, 화면: $${screenValue.toLocaleString()})`);
    });
    
    // 일자별 샘플 데이터 (첫 3일)
    console.log('\n📅 6월 첫 3일 일자별 샘플:');
    const dailyData = {};
    data.forEach(item => {
      if (!dailyData[item.date]) {
        dailyData[item.date] = [];
      }
      dailyData[item.date].push(item);
    });
    
    Object.keys(dailyData).sort().slice(0, 3).forEach(date => {
      console.log(`\n📅 ${date}:`);
      dailyData[date].forEach(item => {
        console.log(`  ${item.brand_name}: GMV=$${item.gmv.toLocaleString()}, Sales=${item.sales_count}, Videos=${item.video_count}`);
      });
    });
    
  } catch (err) {
    console.error('❌ 오류 발생:', err);
  }
}

verifyJuneMapping(); 