-- echotik_api_config 테이블의 정확한 컬럼명 확인

-- 1. 테이블 구조 상세 확인
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'echotik_api_config'
ORDER BY ordinal_position;

-- 2. 실제 데이터 확인 (모든 컬럼)
SELECT * FROM public.echotik_api_config LIMIT 1;

-- 3. 테이블의 모든 컬럼명만 추출
SELECT column_name
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'echotik_api_config'
ORDER BY ordinal_position;

-- 4. 테이블 정의 확인 (PostgreSQL 시스템 카탈로그 사용)
SELECT 
    a.attname AS column_name,
    t.typname AS data_type,
    a.attnotnull AS not_null,
    a.attnum AS position
FROM pg_attribute a
JOIN pg_type t ON a.atttypid = t.oid
JOIN pg_class c ON a.attrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'public'
AND c.relname = 'echotik_api_config'
AND a.attnum > 0
AND NOT a.attisdropped
ORDER BY a.attnum; 