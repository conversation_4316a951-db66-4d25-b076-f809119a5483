-- =======================================
-- HTTP Extension 설정 및 EchoTik API 테스트
-- =======================================

-- 1. HTTP extension 확인
SELECT '🔍 HTTP Extension 상태 확인:' as 확인;
SELECT name, installed_version, default_version, comment
FROM pg_available_extensions 
WHERE name = 'http';

-- 2. HTTP extension 설치 (필요시)
-- CREATE EXTENSION IF NOT EXISTS http;

-- 3. 설치된 extension 목록 확인
SELECT '📋 설치된 Extensions:' as 확인;
SELECT extname, extversion 
FROM pg_extension 
WHERE extname IN ('http', 'pg_cron');

-- 4. Basic Auth 테스트 함수
CREATE OR REPLACE FUNCTION test_basic_auth()
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    auth_header TEXT;
    username TEXT := 'mark_kim';
    password TEXT := '76a669aeff3d11efad3b5254ac16e20b';
BEGIN
    -- Basic Auth 헤더 생성 테스트
    auth_header := 'Basic ' || encode(
        convert_to(username || ':' || password, 'UTF8'),
        'base64'
    );
    
    RETURN auth_header;
END;
$$;

-- 5. Basic Auth 헤더 테스트
SELECT '🔐 Basic Auth 헤더 테스트:' as 테스트;
SELECT test_basic_auth() as auth_header;

-- 6. HTTP 요청 테스트 (간단한 GET 요청)
SELECT '🌐 HTTP 요청 테스트:' as 테스트;
-- SELECT status, content FROM http_get('https://httpbin.org/get');

-- 7. EchoTik API 연결 테스트 (Seller List)
CREATE OR REPLACE FUNCTION test_echotik_seller_list()
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    auth_header TEXT;
    response RECORD;
    api_url TEXT;
BEGIN
    -- Basic Auth 헤더 생성
    auth_header := 'Basic ' || encode(
        convert_to('mark_kim:76a669aeff3d11efad3b5254ac16e20b', 'UTF8'),
        'base64'
    );
    
    -- API URL
    api_url := 'https://open.echotik.live/api/v2/seller/list?keyword=Skin1004&region=US&page_num=1&page_size=1';
    
    -- HTTP 요청
    SELECT status, content INTO response
    FROM http((
        'GET',
        api_url,
        ARRAY[http_header('Authorization', auth_header)],
        NULL,
        NULL
    )::http_request);
    
    -- 응답 확인
    IF response.status = 200 THEN
        RETURN response.content::jsonb;
    ELSE
        RETURN jsonb_build_object(
            'error', 'HTTP request failed',
            'status', response.status,
            'content', response.content
        );
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('error', SQLERRM);
END;
$$;

-- 8. EchoTik Seller List API 테스트 실행
SELECT '🧪 EchoTik Seller List API 테스트:' as 테스트;
-- SELECT test_echotik_seller_list() as api_response;

-- 9. EchoTik Seller Trend API 테스트
CREATE OR REPLACE FUNCTION test_echotik_seller_trend()
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    auth_header TEXT;
    response RECORD;
    api_url TEXT;
    test_seller_id TEXT := '7495275617887947202'; -- Skin1004 US (예시)
    test_date TEXT := (CURRENT_DATE - INTERVAL '3 days')::TEXT;
BEGIN
    -- Basic Auth 헤더 생성
    auth_header := 'Basic ' || encode(
        convert_to('mark_kim:76a669aeff3d11efad3b5254ac16e20b', 'UTF8'),
        'base64'
    );
    
    -- API URL
    api_url := 'https://open.echotik.live/api/v2/seller/trend' ||
               '?seller_id=' || test_seller_id ||
               '&start_date=' || test_date ||
               '&end_date=' || test_date ||
               '&page_num=1&page_size=1';
    
    -- HTTP 요청
    SELECT status, content INTO response
    FROM http((
        'GET',
        api_url,
        ARRAY[http_header('Authorization', auth_header)],
        NULL,
        NULL
    )::http_request);
    
    -- 응답 확인
    IF response.status = 200 THEN
        RETURN response.content::jsonb;
    ELSE
        RETURN jsonb_build_object(
            'error', 'HTTP request failed',
            'status', response.status,
            'content', response.content
        );
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('error', SQLERRM);
END;
$$;

-- 10. EchoTik Seller Trend API 테스트 실행
SELECT '🧪 EchoTik Seller Trend API 테스트:' as 테스트;
-- SELECT test_echotik_seller_trend() as api_response;

-- 11. 시스템 준비 상태 확인
CREATE OR REPLACE FUNCTION check_system_readiness()
RETURNS TABLE(
    component TEXT,
    status TEXT,
    details TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- HTTP Extension 확인
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'http') THEN
        RETURN QUERY SELECT 'HTTP Extension'::TEXT, '✅ 설치됨'::TEXT, 'API 호출 가능'::TEXT;
    ELSE
        RETURN QUERY SELECT 'HTTP Extension'::TEXT, '❌ 미설치'::TEXT, 'CREATE EXTENSION http; 실행 필요'::TEXT;
    END IF;
    
    -- pg_cron Extension 확인
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') THEN
        RETURN QUERY SELECT 'pg_cron Extension'::TEXT, '✅ 설치됨'::TEXT, 'Cron Job 설정 가능'::TEXT;
    ELSE
        RETURN QUERY SELECT 'pg_cron Extension'::TEXT, '❌ 미설치'::TEXT, 'CREATE EXTENSION pg_cron; 실행 필요'::TEXT;
    END IF;
    
    -- 테이블 확인
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'brand' AND table_name = 'echotik-seller-trend-daily') THEN
        RETURN QUERY SELECT 'Main Table'::TEXT, '✅ 생성됨'::TEXT, 'brand."echotik-seller-trend-daily" 존재'::TEXT;
    ELSE
        RETURN QUERY SELECT 'Main Table'::TEXT, '❌ 미생성'::TEXT, 'create_echotik_seller_trend_table.sql 실행 필요'::TEXT;
    END IF;
    
    -- 설정 테이블 확인
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'brand' AND table_name = 'echotik_seller_config') THEN
        RETURN QUERY SELECT 'Config Table'::TEXT, '✅ 생성됨'::TEXT, 'brand.echotik_seller_config 존재'::TEXT;
    ELSE
        RETURN QUERY SELECT 'Config Table'::TEXT, '❌ 미생성'::TEXT, 'setup_echotik_seller_trend_system.sql 실행 필요'::TEXT;
    END IF;
    
    -- API 설정 확인
    IF EXISTS (SELECT 1 FROM brand.echotik_api_config WHERE api_username = 'mark_kim') THEN
        RETURN QUERY SELECT 'API Config'::TEXT, '✅ 설정됨'::TEXT, 'Basic Auth 정보 저장됨'::TEXT;
    ELSE
        RETURN QUERY SELECT 'API Config'::TEXT, '❌ 미설정'::TEXT, 'API 인증 정보 설정 필요'::TEXT;
    END IF;
END;
$$;

-- 12. 시스템 준비 상태 확인 실행
SELECT '🔍 시스템 준비 상태 확인:' as 확인;
SELECT * FROM check_system_readiness();

-- 13. 다음 단계 안내
SELECT '📋 다음 실행 순서:' as 안내;
SELECT '1. HTTP Extension 설치: CREATE EXTENSION IF NOT EXISTS http;' as 단계1;
SELECT '2. 메인 테이블 생성: create_echotik_seller_trend_table.sql 실행' as 단계2;
SELECT '3. 시스템 설정: setup_echotik_seller_trend_system.sql 실행' as 단계3;
SELECT '4. API 테스트: SELECT test_echotik_seller_list();' as 단계4;
SELECT '5. 초기 설정 실행: SELECT brand.setup_echotik_seller_trend_system();' as 단계5;
SELECT '6. Cron Job 설정: setup_echotik_cron_job.sql 실행' as 단계6;

-- 14. 테스트 함수 정리
DROP FUNCTION IF EXISTS test_basic_auth();
DROP FUNCTION IF EXISTS test_echotik_seller_list();
DROP FUNCTION IF EXISTS test_echotik_seller_trend();
DROP FUNCTION IF EXISTS check_system_readiness(); 