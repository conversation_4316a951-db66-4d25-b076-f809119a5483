{"name": "echotik-seller-trend-collector", "version": "1.0.0", "description": "AWS Lambda function to collect EchoTik Seller Trend data and save to Supabase", "main": "lambda_echotik_collector.js", "scripts": {"test": "node lambda_echotik_collector.js", "deploy": "zip -r lambda-deployment.zip lambda_echotik_collector.js package.json node_modules/"}, "dependencies": {"@supabase/supabase-js": "^2.39.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["aws-lambda", "echotik", "supabase", "data-collection", "cron"], "author": "<PERSON>", "license": "MIT"}