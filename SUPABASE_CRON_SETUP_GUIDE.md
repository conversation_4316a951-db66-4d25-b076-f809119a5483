# Supabase Cron Jobs 설정 가이드

매일 01:00 AM (KST)에 EchoTik API에서 K-뷰티 브랜드의 일일 데이터를 자동으로 수집하는 Cron Job을 설정하는 방법입니다.

## 📋 사전 준비사항

1. **Supabase 프로젝트** (Pro 플랜 이상 필요)
2. **EchoTik API 키**
3. **테이블 생성 완료** (`competitor-dashboard-daily`)

## 🚀 1단계: Supabase SQL Editor에서 기본 설정

### 1.1 Extensions 활성화
```sql
-- HTTP 요청을 위한 extension 활성화
CREATE EXTENSION IF NOT EXISTS http;

-- Cron Jobs는 Supabase에서 기본 활성화됨
-- CREATE EXTENSION IF NOT EXISTS pg_cron;
```

### 1.2 기본 함수 및 테이블 생성
`supabase_cron_setup.sql` 파일의 내용을 Supabase SQL Editor에서 실행합니다.

## 🔧 2단계: 실제 API 연동 설정

### 2.1 HTTP Extension 및 API 함수 생성
`supabase_cron_echotik_api.sql` 파일의 내용을 실행합니다.

### 2.2 EchoTik API Basic Auth 설정
```sql
-- Basic Auth 정보 업데이트 (이미 설정됨)
UPDATE echotik_api_config 
SET api_key = 'mark_kim:76a669aeff3d11efad3b5254ac16e20b'
WHERE id = 1;

-- 설정 확인
SELECT 
    id,
    LEFT(api_key, 20) || '...' as api_key_preview,
    base_url
FROM echotik_api_config;
```

## ⏰ 3단계: Cron Job 스케줄 확인

### 3.1 시간대 설정 확인
- **KST 01:00 AM = UTC 16:00 (전날)**
- Cron 표현식: `0 16 * * *`

### 3.2 Cron Job 상태 확인
```sql
-- 현재 등록된 Cron Jobs 확인
SELECT * FROM check_cron_jobs();

-- 또는 직접 확인
SELECT * FROM cron.job;
```

## 🧪 4단계: 테스트 실행

### 4.1 수동 테스트
```sql
-- 함수 수동 실행 테스트
SELECT test_daily_fetch_v2();
```

### 4.2 API 연결 테스트
```sql
-- Basic Auth 헤더 생성 테스트
SELECT 'Basic ' || encode('mark_kim:76a669aeff3d11efad3b5254ac16e20b'::bytea, 'base64') as basic_auth_header;

-- 특정 브랜드 API 호출 테스트 (Skin1004)
SELECT call_echotik_seller_trend_api('7495275617887947202', '2025-01-20');

-- 전체 테스트 스크립트 실행
-- test_echotik_api.sql 파일 내용을 실행하여 종합 테스트
```

## 📊 5단계: 모니터링 및 로그 확인

### 5.1 Cron Job 실행 로그 확인
```sql
-- 최근 실행 결과 확인
SELECT * FROM check_recent_cron_results(7);

-- 상세 실행 로그 확인
SELECT * FROM check_cron_logs();
```

### 5.2 데이터 업데이트 확인
```sql
-- 최근 업데이트된 데이터 확인
SELECT 
    date,
    brand_name,
    gmv,
    sales_count,
    updated_at
FROM "competitor-dashboard-daily"
WHERE date >= CURRENT_DATE - INTERVAL '7 days'
ORDER BY date DESC, brand_name;
```

## 🔄 6단계: Cron Job 관리

### 6.1 Cron Job 중지
```sql
SELECT cron.unschedule('daily-competitor-data-fetch-v2');
```

### 6.2 Cron Job 재시작
```sql
SELECT cron.schedule(
    'daily-competitor-data-fetch-v2',
    '0 16 * * *',
    'SELECT fetch_daily_competitor_data_v2();'
);
```

### 6.3 스케줄 변경 (예: 매일 02:00 AM KST)
```sql
-- 기존 Job 제거
SELECT cron.unschedule('daily-competitor-data-fetch-v2');

-- 새 시간으로 설정 (17:00 UTC = 02:00 KST)
SELECT cron.schedule(
    'daily-competitor-data-fetch-v2',
    '0 17 * * *',
    'SELECT fetch_daily_competitor_data_v2();'
);
```

## 🚨 7단계: 오류 처리 및 알림

### 7.1 오류 발생 시 확인사항
1. **Basic Auth 인증**: username=mark_kim, password=76a669aeff3d11efad3b5254ac16e20b가 올바른지 확인
2. **네트워크 연결**: Supabase에서 외부 API 호출 가능한지 확인
3. **Rate Limiting**: API 호출 제한에 걸리지 않았는지 확인
4. **데이터 형식**: API 응답 형식이 변경되지 않았는지 확인
5. **Base64 인코딩**: Basic Auth 헤더가 올바르게 생성되는지 확인

### 7.2 알림 설정 (선택사항)
```sql
-- 오류 발생 시 이메일 알림 (Supabase 기능 활용)
CREATE OR REPLACE FUNCTION send_error_notification(error_msg TEXT)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- Supabase Edge Functions를 통한 알림 발송
    -- 또는 webhook을 통한 Slack/Discord 알림
    RAISE NOTICE 'Error notification: %', error_msg;
END;
$$;
```

## 📈 8단계: 성능 최적화

### 8.1 배치 처리 최적화
- API 호출 간 1초 대기 (Rate limiting 방지)
- 병렬 처리 대신 순차 처리로 안정성 확보

### 8.2 데이터 정리
```sql
-- 오래된 로그 데이터 정리 (월 1회)
DELETE FROM cron_job_logs 
WHERE created_at < CURRENT_DATE - INTERVAL '90 days';
```

## 🎯 완료 체크리스트

- [ ] Extensions 활성화 (http, pg_cron)
- [ ] 기본 함수 및 테이블 생성
- [ ] EchoTik API 키 설정
- [ ] Cron Job 스케줄 등록
- [ ] 수동 테스트 실행 성공
- [ ] 로그 모니터링 설정
- [ ] 오류 처리 메커니즘 구현

## 📞 문제 해결

### 자주 발생하는 문제들:

1. **HTTP Extension 오류**
   ```sql
   -- Extension 재설치
   DROP EXTENSION IF EXISTS http;
   CREATE EXTENSION http;
   ```

2. **Basic Auth 오류**
   ```sql
   -- Basic Auth 설정 확인
   SELECT api_key FROM echotik_api_config;
   
   -- Base64 인코딩 테스트
   SELECT 'Basic ' || encode('mark_kim:76a669aeff3d11efad3b5254ac16e20b'::bytea, 'base64') as auth_header;
   ```

3. **시간대 문제**
   ```sql
   -- 현재 시간대 확인
   SELECT NOW(), CURRENT_TIMESTAMP AT TIME ZONE 'UTC';
   ```

## 🔗 관련 링크

- [Supabase Cron Jobs 문서](https://supabase.com/docs/guides/database/extensions/pg_cron)
- [PostgreSQL HTTP Extension](https://github.com/pramsey/pgsql-http)
- [EchoTik API 문서](https://api.echotik.com/docs)

---

이 설정을 통해 매일 자동으로 최신 경쟁사 데이터를 수집하여 대시보드에 실시간으로 반영할 수 있습니다. 