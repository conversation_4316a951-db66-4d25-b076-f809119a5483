const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

// Supabase 설정
const supabaseUrl = 'https://tmqhrmpzfgjwqdyiormw.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRtcWhybXB6Zmdqd3FkeWlvcm13Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNzExNDEsImV4cCI6MjA2NTc0NzE0MX0.zNCWt3PbLBqB3esC70HI3PbadAVMBKPBC_5AkxkDP3Q'

const supabase = createClient(supabaseUrl, supabaseKey)

// 브랜드 이름 매핑
const brandMapping = {
  'skin1004': 'Skin1004 US',
  'medicube': 'medicube US Store', 
  'cosrx': 'COSRX US',
  'drmelaxin': 'Dr.Melaxin Global',
  'anua': 'Anua Store US',
  'beautyofjoseon': 'Beauty of Joseon US'
}

// 월별 이름 매핑
const getMonthLabel = (monthStr) => {
  const monthNames = {
    '2024-12': 'Dec 2024',
    '2025-01': 'Jan 2025',
    '2025-02': 'Feb 2025', 
    '2025-03': 'Mar 2025',
    '2025-04': 'Apr 2025',
    '2025-05': 'May 2025',
    '2025-06': 'Jun 2025 (ing)'
  }
  return monthNames[monthStr] || monthStr
}

// 장기 트렌드 파일에서 데이터 추출
function extractLongTermData(filename) {
  try {
    const content = fs.readFileSync(filename, 'utf8')
    const lines = content.trim().split('\n')
    let allData = []
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          const parsed = JSON.parse(line)
          if (parsed.data && Array.isArray(parsed.data)) {
            allData.push(...parsed.data)
          }
        } catch (e) {
          // Skip invalid JSON lines
        }
      }
    }
    
    return allData
  } catch (error) {
    console.error(`Error reading ${filename}:`, error.message)
    return []
  }
}

// 월별 데이터 집계 함수
function createMonthlyData() {
  const monthlyData = {}
  
  // 모든 브랜드의 데이터에서 월별로 집계
  Object.entries(brandMapping).forEach(([key, displayName]) => {
    const filename = `${key}_longterm_trend.json`
    console.log(`Processing ${filename}...`)
    
    const data = extractLongTermData(filename)
    console.log(`  Found ${data.length} data points for ${displayName}`)
    
    data.forEach(item => {
      const monthKey = item.dt.substring(0, 7) // YYYY-MM 형식
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {}
      }
      
      if (!monthlyData[monthKey][displayName]) {
        monthlyData[monthKey][displayName] = {
          totalSales: 0,
          totalGMV: 0,
          totalVideos: 0,
          totalLives: 0,
          avgProducts: 0,
          dataPoints: 0
        }
      }
      
      // 월별 데이터 누적
      monthlyData[monthKey][displayName].totalSales += item.total_sale_1d_cnt || 0
      monthlyData[monthKey][displayName].totalGMV += item.total_sale_gmv_1d_amt || 0
      monthlyData[monthKey][displayName].totalVideos += item.total_video_cnt || 0
      monthlyData[monthKey][displayName].totalLives += item.total_live_cnt || 0
      monthlyData[monthKey][displayName].avgProducts += item.total_product_cnt || 0
      monthlyData[monthKey][displayName].dataPoints += 1
    })
  })
  
  // 월별 평균 계산
  Object.keys(monthlyData).forEach(month => {
    Object.keys(monthlyData[month]).forEach(brand => {
      const data = monthlyData[month][brand]
      data.avgProducts = Math.round(data.avgProducts / data.dataPoints)
      data.avgVideos = Math.round(data.totalVideos / data.dataPoints)
      data.avgLives = Math.round(data.totalLives / data.dataPoints)
    })
  })
  
  return monthlyData
}

// Supabase 테이블 생성 (SQL)
async function createTable() {
  console.log('Creating competitor-dashboard table...')
  
  const { data, error } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS "competitor-dashboard" (
        id SERIAL PRIMARY KEY,
        metric_type VARCHAR(50) NOT NULL,
        date_period VARCHAR(20) NOT NULL,
        month_key VARCHAR(10) NOT NULL,
        brand_name VARCHAR(100) NOT NULL,
        value BIGINT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      
      CREATE INDEX IF NOT EXISTS idx_competitor_dashboard_metric ON "competitor-dashboard"(metric_type);
      CREATE INDEX IF NOT EXISTS idx_competitor_dashboard_date ON "competitor-dashboard"(date_period);
      CREATE INDEX IF NOT EXISTS idx_competitor_dashboard_brand ON "competitor-dashboard"(brand_name);
    `
  })
  
  if (error) {
    console.error('Error creating table:', error)
    return false
  }
  
  console.log('Table created successfully!')
  return true
}

// 데이터를 Supabase에 업로드
async function uploadData() {
  console.log('\n월별 데이터 집계 중...')
  const monthlyData = createMonthlyData()
  
  console.log('\nSupabase에 데이터 업로드 중...')
  
  const sortedMonths = Object.keys(monthlyData).sort()
  const metrics = ['GMV', 'Item solds', 'Creators', 'Video', 'Live', 'Views']
  
  // 기존 데이터 삭제
  console.log('기존 데이터 삭제 중...')
  const { error: deleteError } = await supabase
    .from('competitor-dashboard')
    .delete()
    .neq('id', 0) // 모든 데이터 삭제
  
  if (deleteError) {
    console.error('Error deleting existing data:', deleteError)
  }
  
  const uploadPromises = []
  
  for (const metric of metrics) {
    for (const month of sortedMonths) {
      const monthLabel = getMonthLabel(month)
      
      for (const [key, brandName] of Object.entries(brandMapping)) {
        const data = monthlyData[month][brandName]
        let value = 0
        
        switch (metric) {
          case 'GMV':
            value = data ? Math.round(data.totalGMV) : 0
            break
          case 'Item solds':
            value = data ? data.totalSales : 0
            break
          case 'Creators':
            value = data ? Math.round(data.avgVideos / 10) : 0
            break
          case 'Video':
            value = data ? data.avgVideos : 0
            break
          case 'Live':
            value = data ? data.avgLives : 0
            break
          case 'Views':
            value = data ? data.avgVideos * 1000 : 0
            break
        }
        
        uploadPromises.push({
          metric_type: metric,
          date_period: monthLabel,
          month_key: month,
          brand_name: brandName,
          value: value
        })
      }
    }
  }
  
  // 배치로 업로드 (Supabase는 한 번에 1000개까지 가능)
  const batchSize = 100
  for (let i = 0; i < uploadPromises.length; i += batchSize) {
    const batch = uploadPromises.slice(i, i + batchSize)
      
    const { data, error } = await supabase
        .from('competitor-dashboard')
        .insert(batch)
      
      if (error) {
      console.error(`Error uploading batch ${i / batchSize + 1}:`, error)
      } else {
      console.log(`Uploaded batch ${i / batchSize + 1}/${Math.ceil(uploadPromises.length / batchSize)}`)
    }
  }
  
  console.log(`\n총 ${uploadPromises.length}개 레코드 업로드 완료!`)
      }

// 업로드된 데이터 확인
async function verifyData() {
  console.log('\n업로드된 데이터 확인 중...')
  
  const { data, error } = await supabase
    .from('competitor-dashboard')
    .select('metric_type, count(*)')
    .group('metric_type')
  
  if (error) {
    console.error('Error verifying data:', error)
    return
  }
  
  console.log('메트릭별 데이터 개수:')
  data.forEach(row => {
    console.log(`  ${row.metric_type}: ${row.count}개`)
  })
  
  // 샘플 데이터 확인
  const { data: sampleData, error: sampleError } = await supabase
    .from('competitor-dashboard')
    .select('*')
    .eq('metric_type', 'GMV')
    .eq('brand_name', 'medicube US Store')
    .limit(3)
  
  if (!sampleError && sampleData.length > 0) {
    console.log('\n샘플 데이터 (medicube GMV):')
    sampleData.forEach(row => {
      console.log(`  ${row.date_period}: $${row.value.toLocaleString()}`)
    })
  }
}

// 메인 실행 함수
async function main() {
  try {
    console.log('🚀 Supabase competitor-dashboard 업로드 시작\n')
    
    // 테이블 생성은 수동으로 해야 할 수 있음 (권한에 따라)
    // await createTable()
    
    await uploadData()
    await verifyData()
    
    console.log('\n✅ 업로드 완료!')
    console.log('이제 competitor dashboard에서 이 데이터를 사용할 수 있습니다.')
    
  } catch (error) {
    console.error('❌ 업로드 중 오류 발생:', error)
  }
}

// 스크립트 실행
main() 