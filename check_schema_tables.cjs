const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://jqfijmjvfgzgghvjbwxc.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpxZmlqbWp2Zmd6Z2dodmpid3hjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDI1NjUyMDIsImV4cCI6MjAxODE0MTIwMn0.JItd7grxUM0vo_h-UWc6Z7KuGusTbFK0qQPEYQjrUaU'
);

async function checkSchemaTables() {
  try {
    console.log('🔍 brand 스키마 확인');
    
    // 기존 테이블 확인
    console.log('\n1️⃣ 기존 competitor-dashboard-daily 테이블 확인:');
    const { data: existingData, error: existingError } = await supabase
      .schema('brand')
      .from('competitor-dashboard-daily')
      .select('count')
      .limit(1);
    
    if (existingError) {
      console.log('   ❌ 기존 테이블 접근 실패:', existingError.message);
    } else {
      console.log('   ✅ 기존 테이블 접근 성공');
    }
    
    // 새 테이블 확인 (언더스코어 버전)
    console.log('\n2️⃣ competitor_seller_trend_daily 테이블 확인:');
    const { data: newData, error: newError } = await supabase
      .schema('brand')
      .from('competitor_seller_trend_daily')
      .select('count')
      .limit(1);
    
    if (newError) {
      console.log('   ❌ 새 테이블 접근 실패:', newError.message);
    } else {
      console.log('   ✅ 새 테이블 접근 성공');
    }
    
    // 하이픈 버전도 확인
    console.log('\n3️⃣ competitor-seller-trend-daily 테이블 확인:');
    const { data: hyphenData, error: hyphenError } = await supabase
      .schema('brand')
      .from('competitor-seller-trend-daily')
      .select('count')
      .limit(1);
    
    if (hyphenError) {
      console.log('   ❌ 하이픈 테이블 접근 실패:', hyphenError.message);
    } else {
      console.log('   ✅ 하이픈 테이블 접근 성공');
    }
    
    // public 스키마도 확인
    console.log('\n4️⃣ public 스키마 확인:');
    const { data: publicData, error: publicError } = await supabase
      .from('competitor_seller_trend_daily')
      .select('count')
      .limit(1);
    
    if (publicError) {
      console.log('   ❌ public 스키마 접근 실패:', publicError.message);
    } else {
      console.log('   ✅ public 스키마 접근 성공');
    }
    
  } catch (err) {
    console.error('❌ 스키마 확인 오류:', err);
  }
}

checkSchemaTables(); 