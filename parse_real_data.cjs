const fs = require('fs');

// 브랜드 이름 매핑 (JSON 파일명 -> 표시 이름)
const brandMapping = {
  'skin1004': 'Skin1004 US',
  'medicube': 'medicube US Store', 
  'cosrx': 'COSRX US',
  'drmelaxin': 'Dr.Melaxin Global',
  'anua': 'Anua Store US',
  'beautyofjoseon': 'Beauty of Joseon US'
};

// 브랜드별 색상 정의
const brandColors = {
  "Skin1004 US": "#4F89FF",
  "medicube US Store": "#FF4F4F",
  "Anua Store US": "#A84FFF", 
  "COSRX US": "#4FFF89",
  "Beauty of Joseon US": "#FFD94F",
  "Dr.Melaxin Global": "#FF4FA8"
};

// JSON 파일에서 데이터 추출 함수
function extractDataFromFile(filename) {
  try {
    const content = fs.readFileSync(filename, 'utf8');
    const lines = content.trim().split('\n');
    let allData = [];
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          const parsed = JSON.parse(line);
          if (parsed.data && Array.isArray(parsed.data)) {
            allData.push(...parsed.data);
          }
        } catch (e) {
          console.log(`Skipping invalid JSON line in ${filename}`);
        }
      }
    }
    
    return allData;
  } catch (error) {
    console.error(`Error reading ${filename}:`, error.message);
    return [];
  }
}

// 모든 브랜드 데이터 수집
const allBrandData = {};

for (const [key, displayName] of Object.entries(brandMapping)) {
  const filename = `${key}_full_trend.json`;
  console.log(`Processing ${filename}...`);
  
  const data = extractDataFromFile(filename);
  allBrandData[displayName] = data;
  console.log(`  Found ${data.length} data points for ${displayName}`);
}

// 날짜별로 데이터 정리
function createChartData() {
  const allDates = new Set();
  
  // 모든 날짜 수집
  Object.values(allBrandData).forEach(brandData => {
    brandData.forEach(item => allDates.add(item.dt));
  });
  
  const sortedDates = Array.from(allDates).sort();
  
  // 각 차트 탭별 데이터 생성
  const chartData = {
    GMV: [],
    "Item solds": [],
    Creators: [],
    Video: [],
    Live: [],
    Views: []
  };
  
  sortedDates.forEach(date => {
    // GMV 데이터 (일일 매출)
    const gmvPoint = { date };
    Object.entries(allBrandData).forEach(([brandName, data]) => {
      const dayData = data.find(item => item.dt === date);
      gmvPoint[brandName] = dayData ? dayData.total_sale_gmv_1d_amt : 0;
    });
    chartData.GMV.push(gmvPoint);
    
    // Item solds 데이터 (일일 판매량)
    const salesPoint = { date };
    Object.entries(allBrandData).forEach(([brandName, data]) => {
      const dayData = data.find(item => item.dt === date);
      salesPoint[brandName] = dayData ? dayData.total_sale_1d_cnt : 0;
    });
    chartData["Item solds"].push(salesPoint);
    
    // Creators 데이터 (인플루언서 수는 비디오 수의 1/10로 추정)
    const creatorsPoint = { date };
    Object.entries(allBrandData).forEach(([brandName, data]) => {
      const dayData = data.find(item => item.dt === date);
      creatorsPoint[brandName] = dayData ? Math.round(dayData.total_video_cnt / 10) : 0;
    });
    chartData.Creators.push(creatorsPoint);
    
    // Video 데이터 (총 비디오 수)
    const videoPoint = { date };
    Object.entries(allBrandData).forEach(([brandName, data]) => {
      const dayData = data.find(item => item.dt === date);
      videoPoint[brandName] = dayData ? dayData.total_video_cnt : 0;
    });
    chartData.Video.push(videoPoint);
    
    // Live 데이터 (총 라이브 수)
    const livePoint = { date };
    Object.entries(allBrandData).forEach(([brandName, data]) => {
      const dayData = data.find(item => item.dt === date);
      livePoint[brandName] = dayData ? dayData.total_live_cnt : 0;
    });
    chartData.Live.push(livePoint);
    
    // Views 데이터 (비디오 수 * 1000으로 추정)
    const viewsPoint = { date };
    Object.entries(allBrandData).forEach(([brandName, data]) => {
      const dayData = data.find(item => item.dt === date);
      viewsPoint[brandName] = dayData ? dayData.total_video_cnt * 1000 : 0;
    });
    chartData.Views.push(viewsPoint);
  });
  
  return chartData;
}

// 데이터 생성
const chartData = createChartData();

// TypeScript 파일 생성
const tsContent = `// Real EchoTik API data - Generated automatically
// Generated on: ${new Date().toISOString()}

// 브랜드별 색상 정의
export const brandColors = ${JSON.stringify(brandColors, null, 2)}

// EchoTik API에서 가져온 실제 트렌드 데이터
export const competitorData = ${JSON.stringify(chartData, null, 2)}
`;

// 파일 저장
fs.writeFileSync('src/utils/competitorRealData.ts', tsContent);

console.log('\n✅ Successfully generated src/utils/competitorRealData.ts');
console.log('\nData Summary:');
console.log(`- Period: ${chartData.GMV[0]?.date} to ${chartData.GMV[chartData.GMV.length - 1]?.date}`);
console.log(`- Total days: ${chartData.GMV.length}`);
console.log(`- Brands: ${Object.keys(brandColors).join(', ')}`);
console.log('\nNext steps:');
console.log('1. Update CompetitorChart.tsx to import from competitorRealData instead of competitorDummyData');
console.log('2. Test the dashboard to ensure data is displaying correctly'); 