# 📋 Supabase Schema Migration Guide: public → brand

기존 `public` 스키마의 데이터와 함수를 `brand` 스키마로 이동하는 가이드입니다.

## 🎯 마이그레이션 목표

- ✅ 기존 데이터 보존
- ✅ 함수들을 brand 스키마로 이동
- ✅ Cron Job 업데이트
- ✅ 코드에서 스키마 참조 변경

## 📝 마이그레이션 단계

### 1단계: 데이터 백업 및 확인

```sql
-- 현재 public 스키마 데이터 확인
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT brand_name) as unique_brands,
    MIN(date) as earliest_date,
    MAX(date) as latest_date
FROM public."competitor-dashboard-daily";

-- API 설정 확인
SELECT * FROM public.echotik_api_config;
```

### 2단계: 마이그레이션 실행

```bash
# Supabase SQL Editor에서 실행
migrate_to_brand_schema.sql
```

**주요 작업:**
- ✅ brand 스키마에 테이블 생성
- ✅ 기존 데이터 복사 (1,053개 레코드)
- ✅ API 설정 데이터 복사
- ✅ 함수들을 brand 스키마로 재생성
- ✅ 권한 설정

### 3단계: Cron Job 업데이트

```bash
# Supabase SQL Editor에서 실행
update_cron_job_brand_schema.sql
```

**변경 사항:**
- ❌ 기존: `SELECT public.fetch_daily_competitor_data_v2();`
- ✅ 새로운: `SELECT brand.fetch_daily_competitor_data_v2();`

### 4단계: 코드 업데이트

**CompetitorChart.tsx 변경:**
```typescript
// 변경 전
const { data, error } = await supabase
  .from('competitor-dashboard-daily')

// 변경 후  
const { data, error } = await supabase
  .schema('brand')
  .from('competitor-dashboard-daily')
```

**upload_daily_data.cjs 변경:**
```javascript
// 모든 Supabase 쿼리에 .schema('brand') 추가
const { data, error } = await supabase
  .schema('brand')
  .from('competitor-dashboard-daily')
```

### 5단계: 테스트 및 검증

```sql
-- brand 스키마 데이터 확인
SELECT 
    'brand schema' as schema_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT brand_name) as unique_brands,
    MIN(date) as earliest_date,
    MAX(date) as latest_date
FROM brand."competitor-dashboard-daily";

-- 함수 테스트
SELECT brand.test_daily_fetch_v2();

-- Cron Job 확인
SELECT * FROM cron.job WHERE jobname LIKE '%competitor%';
```

## 🔧 업데이트된 파일 목록

### SQL 스크립트
- ✅ `migrate_to_brand_schema.sql` - 메인 마이그레이션 스크립트
- ✅ `update_cron_job_brand_schema.sql` - Cron Job 업데이트

### 코드 파일
- ✅ `src/components/dashboard/CompetitorChart.tsx` - brand 스키마 사용
- ✅ `upload_daily_data.cjs` - brand 스키마 사용

## 🚀 실행 순서

1. **백업 확인**
   ```sql
   SELECT COUNT(*) FROM public."competitor-dashboard-daily";
   ```

2. **마이그레이션 실행**
   ```sql
   -- migrate_to_brand_schema.sql 전체 실행
   ```

3. **Cron Job 업데이트**
   ```sql
   -- update_cron_job_brand_schema.sql 실행
   ```

4. **코드 배포**
   ```bash
   git add .
   git commit -m "feat: brand 스키마로 마이그레이션"
   git push
   ```

5. **검증**
   ```sql
   SELECT brand.test_daily_fetch_v2();
   ```

## ⚠️ 주의사항

1. **백업**: 마이그레이션 전에 반드시 데이터 백업
2. **순서**: SQL 스크립트를 순서대로 실행
3. **검증**: 각 단계 후 데이터 무결성 확인
4. **정리**: 마이그레이션 성공 후 public 스키마 정리

## 🎉 마이그레이션 완료 후 상태

- ✅ **데이터**: brand."competitor-dashboard-daily" (1,053+ 레코드)
- ✅ **함수**: brand.fetch_daily_competitor_data_v2()
- ✅ **Cron Job**: brand 스키마 함수 호출
- ✅ **웹앱**: brand 스키마에서 데이터 로딩
- ✅ **자동화**: 매일 KST 01:00 자동 실행

## 🔍 트러블슈팅

### 권한 오류
```sql
GRANT USAGE ON SCHEMA brand TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA brand TO postgres, anon, authenticated, service_role;
```

### 함수 호출 오류
```sql
-- 함수 존재 확인
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'brand';
```

### 데이터 불일치
```sql
-- 데이터 비교
SELECT 'public' as schema, COUNT(*) FROM public."competitor-dashboard-daily"
UNION ALL
SELECT 'brand' as schema, COUNT(*) FROM brand."competitor-dashboard-daily";
``` 