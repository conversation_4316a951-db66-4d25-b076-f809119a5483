const { createClient } = require('@supabase/supabase-js');

// Supabase 설정
const supabaseUrl = 'https://tmqhrmpzfgjwqdyiormw.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRtcWhybXB6Zmdqd3FkeWlvcm13Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNzExNDEsImV4cCI6MjA2NTc0NzE0MX0.zNCWt3PbLBqB3esC70HI3PbadAVMBKPBC_5AkxkDP3Q';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  console.log('🔗 Supabase 연결 테스트...');
  
  try {
    // 간단한 쿼리로 연결 테스트
    const { data, error } = await supabase
      .from('auth.users')
      .select('count', { count: 'exact', head: true });
    
    if (error) {
      console.log('❌ 연결 실패:', error.message);
      
      // 테이블 생성 시도
      console.log('\n📋 테이블 생성 시도...');
      await createTable();
    } else {
      console.log('✅ Supabase 연결 성공!');
    }
  } catch (err) {
    console.error('❌ 연결 오류:', err.message);
  }
}

async function createTable() {
  console.log('📋 competitor-dashboard 테이블 생성 중...');
  
  // SQL 실행을 위한 RPC 호출
  const { data, error } = await supabase.rpc('exec', {
    sql: `
      CREATE TABLE IF NOT EXISTS "competitor-dashboard" (
        id SERIAL PRIMARY KEY,
        metric_type VARCHAR(50) NOT NULL,
        date_period VARCHAR(20) NOT NULL,
        month_key VARCHAR(10) NOT NULL,
        brand_name VARCHAR(100) NOT NULL,
        value BIGINT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });
  
  if (error) {
    console.log('❌ 테이블 생성 실패:', error.message);
    console.log('💡 Supabase 대시보드에서 수동으로 테이블을 생성해주세요.');
    console.log('📄 SQL 파일: create_table.sql');
  } else {
    console.log('✅ 테이블 생성 성공!');
  }
}

async function listTables() {
  console.log('\n📋 기존 테이블 목록 확인...');
  
  const { data, error } = await supabase
    .rpc('exec', {
      sql: `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name;
      `
    });
  
  if (error) {
    console.log('❌ 테이블 목록 조회 실패:', error.message);
  } else {
    console.log('📋 Public 스키마의 테이블들:');
    if (data && data.length > 0) {
      data.forEach(table => {
        console.log(`  - ${table.table_name}`);
      });
    } else {
      console.log('  (테이블 없음)');
    }
  }
}

// 테스트 실행
async function main() {
  await testConnection();
  await listTables();
  
  console.log('\n🎯 다음 단계:');
  console.log('1. Supabase 대시보드 접속: https://supabase.com/dashboard');
  console.log('2. SQL Editor에서 create_table.sql 실행');
  console.log('3. upload_to_supabase.cjs 실행');
}

main().catch(console.error); 