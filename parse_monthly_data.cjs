const fs = require('fs');

// 브랜드 이름 매핑
const brandMapping = {
  'skin1004': 'Skin1004 US',
  'medicube': 'medicube US Store', 
  'cosrx': 'COSRX US',
  'drmelaxin': 'Dr.Melaxin Global',
  'anua': 'Anua Store US',
  'beautyofjoseon': 'Beauty of Joseon US'
};

// 브랜드별 색상 정의
const brandColors = {
  "Skin1004 US": "#4F89FF",
  "medicube US Store": "#FF4F4F",
  "Anua Store US": "#A84FFF", 
  "COSRX US": "#4FFF89",
  "Beauty of Joseon US": "#FFD94F",
  "Dr.Melaxin Global": "#FF4FA8"
};

// 월별 이름 매핑 (6월은 ing 표시)
const getMonthLabel = (monthStr) => {
  const monthNames = {
    '2024-12': 'Dec 2024',
    '2025-01': 'Jan 2025',
    '2025-02': 'Feb 2025', 
    '2025-03': 'Mar 2025',
    '2025-04': 'Apr 2025',
    '2025-05': 'May 2025',
    '2025-06': 'Jun 2025 (ing)'
  };
  return monthNames[monthStr] || monthStr;
};

// 장기 트렌드 파일에서 데이터 추출
function extractLongTermData(filename) {
  try {
    const content = fs.readFileSync(filename, 'utf8');
    const lines = content.trim().split('\n');
    let allData = [];
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          const parsed = JSON.parse(line);
          if (parsed.data && Array.isArray(parsed.data)) {
            allData.push(...parsed.data);
          }
        } catch (e) {
          // Skip invalid JSON lines
        }
      }
    }
    
    return allData;
  } catch (error) {
    console.error(`Error reading ${filename}:`, error.message);
    return [];
  }
}

// 모든 브랜드 데이터 수집
const allBrandData = {};

for (const [key, displayName] of Object.entries(brandMapping)) {
  const filename = `${key}_longterm_trend.json`;
  console.log(`Processing ${filename}...`);
  
  const data = extractLongTermData(filename);
  allBrandData[displayName] = data;
  console.log(`  Found ${data.length} data points for ${displayName}`);
}

// 월별 데이터 집계 함수
function createMonthlyData() {
  const monthlyData = {};
  
  // 모든 브랜드의 데이터에서 월별로 집계
  Object.entries(allBrandData).forEach(([brandName, data]) => {
    data.forEach(item => {
      const monthKey = item.dt.substring(0, 7); // YYYY-MM 형식
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {};
      }
      
      if (!monthlyData[monthKey][brandName]) {
        monthlyData[monthKey][brandName] = {
          totalSales: 0,
          totalGMV: 0,
          totalVideos: 0,
          totalLives: 0,
          avgProducts: 0,
          dataPoints: 0
        };
      }
      
      // 월별 데이터 누적
      monthlyData[monthKey][brandName].totalSales += item.total_sale_1d_cnt || 0;
      monthlyData[monthKey][brandName].totalGMV += item.total_sale_gmv_1d_amt || 0;
      monthlyData[monthKey][brandName].totalVideos += item.total_video_cnt || 0;
      monthlyData[monthKey][brandName].totalLives += item.total_live_cnt || 0;
      monthlyData[monthKey][brandName].avgProducts += item.total_product_cnt || 0;
      monthlyData[monthKey][brandName].dataPoints += 1;
    });
  });
  
  // 월별 평균 계산
  Object.keys(monthlyData).forEach(month => {
    Object.keys(monthlyData[month]).forEach(brand => {
      const data = monthlyData[month][brand];
      data.avgProducts = Math.round(data.avgProducts / data.dataPoints);
      data.avgVideos = Math.round(data.totalVideos / data.dataPoints);
      data.avgLives = Math.round(data.totalLives / data.dataPoints);
    });
  });
  
  return monthlyData;
}

// 차트용 데이터 생성
function createChartData(monthlyData) {
  const sortedMonths = Object.keys(monthlyData).sort();
  
  const chartData = {
    GMV: [],
    "Item solds": [],
    Creators: [],
    Video: [],
    Live: [],
    Views: []
  };
  
  sortedMonths.forEach(month => {
    const monthLabel = getMonthLabel(month);
    
    // GMV 데이터 (월별 총 매출)
    const gmvPoint = { date: monthLabel, month: month };
    Object.keys(brandMapping).forEach(key => {
      const brandName = brandMapping[key];
      const data = monthlyData[month][brandName];
      gmvPoint[brandName] = data ? Math.round(data.totalGMV) : 0;
    });
    chartData.GMV.push(gmvPoint);
    
    // Item solds 데이터 (월별 총 판매량)
    const salesPoint = { date: monthLabel, month: month };
    Object.keys(brandMapping).forEach(key => {
      const brandName = brandMapping[key];
      const data = monthlyData[month][brandName];
      salesPoint[brandName] = data ? data.totalSales : 0;
    });
    chartData["Item solds"].push(salesPoint);
    
    // Creators 데이터 (평균 비디오 수 / 10으로 추정)
    const creatorsPoint = { date: monthLabel, month: month };
    Object.keys(brandMapping).forEach(key => {
      const brandName = brandMapping[key];
      const data = monthlyData[month][brandName];
      creatorsPoint[brandName] = data ? Math.round(data.avgVideos / 10) : 0;
    });
    chartData.Creators.push(creatorsPoint);
    
    // Video 데이터 (월 평균 비디오 수)
    const videoPoint = { date: monthLabel, month: month };
    Object.keys(brandMapping).forEach(key => {
      const brandName = brandMapping[key];
      const data = monthlyData[month][brandName];
      videoPoint[brandName] = data ? data.avgVideos : 0;
    });
    chartData.Video.push(videoPoint);
    
    // Live 데이터 (월 평균 라이브 수)
    const livePoint = { date: monthLabel, month: month };
    Object.keys(brandMapping).forEach(key => {
      const brandName = brandMapping[key];
      const data = monthlyData[month][brandName];
      livePoint[brandName] = data ? data.avgLives : 0;
    });
    chartData.Live.push(livePoint);
    
    // Views 데이터 (비디오 수 * 1000으로 추정)
    const viewsPoint = { date: monthLabel, month: month };
    Object.keys(brandMapping).forEach(key => {
      const brandName = brandMapping[key];
      const data = monthlyData[month][brandName];
      viewsPoint[brandName] = data ? data.avgVideos * 1000 : 0;
    });
    chartData.Views.push(viewsPoint);
  });
  
  return chartData;
}

// 데이터 생성
console.log('\n월별 데이터 집계 중...');
const monthlyData = createMonthlyData();
const chartData = createChartData(monthlyData);

console.log('\n월별 집계 결과:');
Object.keys(monthlyData).sort().forEach(month => {
  console.log(`${getMonthLabel(month)}: ${Object.keys(monthlyData[month]).length}개 브랜드`);
});

// TypeScript 파일 생성
const tsContent = `// Monthly EchoTik API data - Generated automatically
// Generated on: ${new Date().toISOString()}
// Data period: 2024-12-28 to 2025-06-22 (176 days)

// 브랜드별 색상 정의
export const brandColors = ${JSON.stringify(brandColors, null, 2)}

// 월별 트렌드 데이터 (6개월)
export const competitorData = ${JSON.stringify(chartData, null, 2)}

// 원본 월별 집계 데이터
export const monthlyAggregatedData = ${JSON.stringify(monthlyData, null, 2)}
`;

// 파일 저장
fs.writeFileSync('src/utils/competitorMonthlyData.ts', tsContent);

console.log('\n✅ Successfully generated src/utils/competitorMonthlyData.ts');
console.log('\nMonthly Data Summary:');
console.log(`- Period: ${chartData.GMV[0]?.date} to ${chartData.GMV[chartData.GMV.length - 1]?.date}`);
console.log(`- Total months: ${chartData.GMV.length}`);
console.log(`- Brands: ${Object.keys(brandColors).join(', ')}`);
console.log('\nNext steps:');
console.log('1. Update CompetitorChart.tsx to import from competitorMonthlyData');
console.log('2. Update the page header to show monthly view');
console.log('3. Test the dashboard with monthly data'); 