-- =======================================
-- EchoTik Seller Trend API Cron Job 설정
-- =======================================

-- 1. pg_cron extension 확인 및 설치
SELECT 'pg_cron extension 상태 확인:' as 확인;
SELECT * FROM pg_available_extensions WHERE name = 'pg_cron';

-- 2. pg_cron extension 설치 (필요시)
-- CREATE EXTENSION IF NOT EXISTS pg_cron;

-- 3. 기존 EchoTik 관련 Cron Job 제거 (있다면)
SELECT cron.unschedule('echotik-seller-trend-daily');

-- 4. 일별 Seller Trend 데이터 수집 Cron Job 설정
-- 매일 오전 9시 (UTC)에 실행
SELECT cron.schedule(
    'echotik-seller-trend-daily',  -- Job 이름
    '0 9 * * *',                   -- 매일 오전 9시 UTC (한국시간 오후 6시)
    'SELECT brand.daily_seller_trend_update();'  -- 실행할 함수
);

-- 5. <PERSON>ron Job 목록 확인
SELECT 
    jobid,
    schedule,
    command,
    nodename,
    nodeport,
    database,
    username,
    active,
    jobname
FROM cron.job 
WHERE jobname = 'echotik-seller-trend-daily';

-- 6. Cron Job 로그 테이블 조회 (실행 결과 확인용)
SELECT 
    runid,
    job_pid,
    database,
    username,
    command,
    status,
    return_message,
    start_time,
    end_time
FROM cron.job_run_details 
WHERE command LIKE '%daily_seller_trend_update%'
ORDER BY start_time DESC
LIMIT 10;

-- 7. 수동 테스트 (필요시)
-- SELECT brand.daily_seller_trend_update();

-- 8. 설정 완료 메시지
SELECT '✅ EchoTik Seller Trend Cron Job 설정 완료!' as 상태;
SELECT '📅 실행 스케줄: 매일 오전 9시 (UTC)' as 스케줄;
SELECT '🔍 로그 확인: SELECT * FROM cron.job_run_details WHERE command LIKE ''%daily_seller_trend_update%'' ORDER BY start_time DESC;' as 로그확인;

-- 9. 주요 관리 명령어 (참고용)
/*
-- Cron Job 일시 중지
UPDATE cron.job SET active = false WHERE jobname = 'echotik-seller-trend-daily';

-- Cron Job 재시작
UPDATE cron.job SET active = true WHERE jobname = 'echotik-seller-trend-daily';

-- Cron Job 완전 삭제
SELECT cron.unschedule('echotik-seller-trend-daily');

-- 스케줄 변경 (예: 매일 오후 2시로 변경)
SELECT cron.alter_job('echotik-seller-trend-daily', schedule => '0 14 * * *');

-- 수동 실행
SELECT brand.daily_seller_trend_update();
*/ 