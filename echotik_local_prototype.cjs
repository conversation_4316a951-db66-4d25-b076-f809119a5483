/**
 * 로컬 프로토타이핑: EchoTik Seller Trend 데이터 수집기
 * Lambda 배포 전 로컬에서 API 테스트 및 데이터 검증용
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase 클라이언트 설정
const supabase = createClient(
  'https://jqfijmjvfgzgghvjbwxc.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpxZmlqbWp2Zmd6Z2dodmpid3hjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDI1NjUyMDIsImV4cCI6MjAxODE0MTIwMn0.JItd7grxUM0vo_h-UWc6Z7KuGusTbFK0qQPEYQjrUaU'
);

// EchoTik API 설정
const ECHOTIK_CONFIG = {
  baseUrl: 'https://open.echotik.live/api/v2',
  username: 'mark_kim',
  password: '76a669aeff3d11efad3b5254ac16e20b'
};

// 테스트용 Seller ID (Anua만 먼저 테스트)
const TEST_SELLERS = [
  { seller_id: '7495467833010457057', brand_name: 'Anua Store US' }
];

/**
 * 날짜를 YYYY-MM-DD 형식으로 포맷
 */
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

/**
 * 테스트용 날짜 범위 (최근 7일)
 */
function getTestDateRange() {
  const today = new Date();
  const endDate = new Date(today);
  endDate.setDate(today.getDate() - 2); // 2일 전까지
  
  const startDate = new Date(today);
  startDate.setDate(today.getDate() - 9); // 9일 전부터 (7일간)
  
  return {
    start_date: formatDate(startDate),
    end_date: formatDate(endDate)
  };
}

/**
 * EchoTik API 테스트 호출
 */
async function testEchoTikAPI(sellerId, startDate, endDate) {
  const url = `${ECHOTIK_CONFIG.baseUrl}/seller/trend`;
  const params = new URLSearchParams({
    seller_id: sellerId,
    start_date: startDate,
    end_date: endDate,
    page_num: 1,
    page_size: 10
  });
  
  const auth = Buffer.from(`${ECHOTIK_CONFIG.username}:${ECHOTIK_CONFIG.password}`).toString('base64');
  
  console.log(`\n📡 EchoTik API 테스트 호출:`);
  console.log(`   URL: ${url}?${params}`);
  console.log(`   기간: ${startDate} ~ ${endDate}`);
  
  try {
    const response = await fetch(`${url}?${params}`, {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`   HTTP Status: ${response.status}`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    console.log(`   API Code: ${result.code}`);
    console.log(`   API Message: ${result.message}`);
    
    if (result.code !== 0) {
      throw new Error(`API Error: ${result.message || 'Unknown error'}`);
    }
    
    console.log(`   ✅ 성공! 데이터 ${result.data?.length || 0}건`);
    
    // 응답 데이터 구조 출력
    if (result.data && result.data.length > 0) {
      console.log(`\n📋 응답 데이터 구조 (첫 번째 레코드):`);
      const firstRecord = result.data[0];
      Object.keys(firstRecord).forEach(key => {
        console.log(`   ${key}: ${firstRecord[key]} (${typeof firstRecord[key]})`);
      });
      
      console.log(`\n📊 전체 데이터 미리보기:`);
      result.data.forEach((item, index) => {
        console.log(`   [${index + 1}] ${item.date}: 판매 ${item.total_sale_cnt}건, GMV $${item.total_sale_gmv_amt}, 비디오 ${item.total_video_cnt}개`);
      });
    }
    
    return result.data || [];
    
  } catch (err) {
    console.error(`   ❌ API 호출 실패:`, err.message);
    throw err;
  }
}

/**
 * Supabase 테이블 존재 확인
 */
async function checkTableExists() {
  try {
    console.log('\n🔍 Supabase 테이블 확인: brand.competitor_seller_trend_daily');
    
    const { data, error } = await supabase
      .schema('brand')
      .from('competitor_seller_trend_daily')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('   ❌ 테이블이 존재하지 않거나 접근 불가:', error.message);
      console.log('   💡 다음 SQL을 Supabase에서 실행해주세요:');
      console.log('   📄 create_echotik_seller_trend_table.sql');
      return false;
    }
    
    console.log('   ✅ 테이블 접근 가능');
    return true;
    
  } catch (err) {
    console.error('   ❌ 테이블 확인 오류:', err.message);
    return false;
  }
}

/**
 * 기존 데이터 확인
 */
async function checkExistingData(sellerId) {
  try {
    const { data, error } = await supabase
      .schema('brand')
      .from('competitor_seller_trend_daily')
      .select('date')
      .eq('seller_id', sellerId)
      .order('date', { ascending: false })
      .limit(5);
    
    if (error) {
      console.error(`   ❌ 기존 데이터 확인 실패:`, error.message);
      return null;
    }
    
    if (data && data.length > 0) {
      console.log(`   📅 기존 데이터 ${data.length}건 발견:`);
      data.forEach(item => console.log(`      - ${item.date}`));
      return data[0].date; // 최신 날짜
    } else {
      console.log(`   📭 기존 데이터 없음`);
      return null;
    }
    
  } catch (err) {
    console.error(`   ❌ 기존 데이터 확인 오류:`, err.message);
    return null;
  }
}

/**
 * 테스트 데이터 저장
 */
async function saveTestData(sellerId, brandName, apiData) {
  if (!apiData || apiData.length === 0) {
    console.log(`   ℹ️  저장할 데이터 없음`);
    return { success: 0, errors: 0 };
  }
  
  console.log(`\n💾 Supabase에 데이터 저장 시작 (${apiData.length}건)`);
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const item of apiData) {
    try {
      const recordData = {
        seller_id: sellerId,
        date: item.date,
        total_sale_cnt: item.total_sale_cnt || 0,
        total_sale_gmv_amt: item.total_sale_gmv_amt || 0,
        total_video_cnt: item.total_video_cnt || 0,
        total_live_cnt: item.total_live_cnt || 0,
        total_video_sale_cnt: item.total_video_sale_cnt || 0,
        total_video_sale_gmv_amt: item.total_video_sale_gmv_amt || 0,
        total_ifl_cnt: item.total_ifl_cnt || 0
      };
      
      console.log(`   저장 중: ${item.date} (판매 ${item.total_sale_cnt}건, GMV $${item.total_sale_gmv_amt})`);
      
      const { error } = await supabase
        .schema('brand')
        .from('competitor_seller_trend_daily')
        .upsert(recordData, {
          onConflict: 'seller_id,date'
        });
      
      if (error) {
        console.error(`   ❌ 저장 실패 (${item.date}):`, error.message);
        errorCount++;
      } else {
        console.log(`   ✅ 저장 성공: ${item.date}`);
        successCount++;
      }
      
    } catch (err) {
      console.error(`   ❌ 처리 실패 (${item.date}):`, err.message);
      errorCount++;
    }
  }
  
  console.log(`\n📊 저장 결과: 성공 ${successCount}건, 실패 ${errorCount}건`);
  return { success: successCount, errors: errorCount };
}

/**
 * 메인 프로토타이핑 함수
 */
async function runPrototype() {
  console.log('🚀 EchoTik API 로컬 프로토타이핑 시작');
  console.log('📅 실행 시간:', new Date().toLocaleString('ko-KR'));
  
  try {
    // 1. Supabase 테이블 확인
    const tableExists = await checkTableExists();
    if (!tableExists) {
      console.log('\n❌ 프로토타이핑 중단: 테이블을 먼저 생성해주세요');
      return;
    }
    
    // 2. 테스트 날짜 범위 설정
    const dateRange = getTestDateRange();
    console.log(`\n📅 테스트 날짜 범위: ${dateRange.start_date} ~ ${dateRange.end_date}`);
    
    // 3. 각 Seller에 대해 테스트
    for (const seller of TEST_SELLERS) {
      console.log(`\n🏢 ${seller.brand_name} (${seller.seller_id}) 테스트 시작`);
      
      // 3-1. 기존 데이터 확인
      await checkExistingData(seller.seller_id);
      
      // 3-2. EchoTik API 호출
      const apiData = await testEchoTikAPI(
        seller.seller_id, 
        dateRange.start_date, 
        dateRange.end_date
      );
      
      // 3-3. 데이터 저장
      const saveResult = await saveTestData(seller.seller_id, seller.brand_name, apiData);
      
      console.log(`✅ ${seller.brand_name} 테스트 완료`);
    }
    
    console.log('\n🎉 프로토타이핑 완료!');
    
  } catch (err) {
    console.error('\n❌ 프로토타이핑 실패:', err.message);
    console.error('상세 오류:', err);
  }
}

// 실행
if (require.main === module) {
  runPrototype();
} 