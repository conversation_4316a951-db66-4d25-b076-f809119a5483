-- competitor-dashboard-daily 테이블 생성 (일일 데이터)
-- 기존 월별 집계 테이블을 일일 데이터로 변경

-- 기존 테이블 백업 (옵션)
CREATE TABLE IF NOT EXISTS "competitor-dashboard-backup" AS 
SELECT * FROM "competitor-dashboard";

-- 새로운 일일 데이터 테이블 생성
DROP TABLE IF EXISTS "competitor-dashboard-daily";
CREATE TABLE "competitor-dashboard-daily" (
  id SERIAL PRIMARY KEY,
  date DATE NOT NULL,
  brand_name VARCHAR(100) NOT NULL,
  seller_id VARCHAR(50) NOT NULL,
  
  -- 일일 메트릭 (EchoTik API 필드 기반)
  gmv DECIMAL(12,2) NOT NULL DEFAULT 0,           -- total_sale_gmv_1d_amt
  sales_count INTEGER NOT NULL DEFAULT 0,         -- total_sale_1d_cnt
  video_count INTEGER NOT NULL DEFAULT 0,         -- total_video_cnt
  live_count INTEGER NOT NULL DEFAULT 0,          -- total_live_cnt
  product_count INTEGER NOT NULL DEFAULT 0,       -- total_product_cnt
  
  -- 누적 데이터 (참고용)
  total_gmv DECIMAL(15,2) DEFAULT 0,              -- total_sale_gmv_amt
  total_sales INTEGER DEFAULT 0,                  -- total_sale_cnt
  
  -- 메타데이터
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 인덱스 생성 (쿼리 성능 최적화)
CREATE INDEX idx_daily_date ON "competitor-dashboard-daily"(date);
CREATE INDEX idx_daily_brand ON "competitor-dashboard-daily"(brand_name);
CREATE INDEX idx_daily_seller ON "competitor-dashboard-daily"(seller_id);
CREATE INDEX idx_daily_date_brand ON "competitor-dashboard-daily"(date, brand_name);

-- 유니크 제약 조건 (같은 날짜, 같은 브랜드 중복 방지)
CREATE UNIQUE INDEX idx_daily_unique ON "competitor-dashboard-daily"(date, brand_name);

-- 테이블 설명
COMMENT ON TABLE "competitor-dashboard-daily" IS 'K-beauty 브랜드 일일 경쟁사 분석 데이터 - EchoTik API 기반';
COMMENT ON COLUMN "competitor-dashboard-daily".date IS '데이터 날짜 (YYYY-MM-DD)';
COMMENT ON COLUMN "competitor-dashboard-daily".brand_name IS '브랜드명 (예: Skin1004 US, medicube US Store)';
COMMENT ON COLUMN "competitor-dashboard-daily".seller_id IS 'EchoTik 셀러 ID';
COMMENT ON COLUMN "competitor-dashboard-daily".gmv IS '일일 GMV 매출액 (USD)';
COMMENT ON COLUMN "competitor-dashboard-daily".sales_count IS '일일 판매 수량';
COMMENT ON COLUMN "competitor-dashboard-daily".video_count IS '총 비디오 수';
COMMENT ON COLUMN "competitor-dashboard-daily".live_count IS '총 라이브 수';
COMMENT ON COLUMN "competitor-dashboard-daily".product_count IS '총 상품 수';

-- 데이터 검증용 뷰 생성 (월별 집계)
CREATE OR REPLACE VIEW "competitor-dashboard-monthly" AS
SELECT 
  DATE_TRUNC('month', date) as month,
  TO_CHAR(DATE_TRUNC('month', date), 'Mon YYYY') as month_label,
  brand_name,
  SUM(gmv) as total_gmv,
  SUM(sales_count) as total_sales,
  AVG(video_count) as avg_videos,
  AVG(live_count) as avg_lives,
  AVG(product_count) as avg_products,
  COUNT(*) as data_points
FROM "competitor-dashboard-daily"
GROUP BY DATE_TRUNC('month', date), brand_name
ORDER BY month, brand_name;

COMMENT ON VIEW "competitor-dashboard-monthly" IS '일일 데이터를 월별로 집계한 뷰'; 