-- =======================================
-- EchoTik Seller Trend API 시스템 설정
-- =======================================

-- 1. API 설정 테이블 생성
CREATE TABLE IF NOT EXISTS brand.echotik_seller_config (
    id SERIAL PRIMARY KEY,
    brand_name TEXT NOT NULL UNIQUE,
    seller_id TEXT,
    region TEXT DEFAULT 'US',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. 브랜드 정보 삽입 (seller_id는 나중에 API로 채움)
INSERT INTO brand.echotik_seller_config (brand_name, region)
VALUES 
    ('Skin1004 US', 'US'),
    ('medicube US Store', 'US'),
    ('Anua Store US', 'US'),
    ('COSRX US', 'US'),
    ('Beauty of Joseon US', 'US'),
    ('Dr.Melaxin', 'US')
ON CONFLICT (brand_name) DO NOTHING;

-- 3. API 인증 정보 저장 (기존 테이블 활용)
INSERT INTO brand.echotik_api_config (api_username, api_password, created_at)
VALUES ('mark_kim', '76a669aeff3d11efad3b5254ac16e20b', NOW())
ON CONFLICT DO NOTHING;

-- 4. Seller List API 호출 함수 (HTTP extension 필요)
CREATE OR REPLACE FUNCTION brand.call_echotik_seller_list_api(
    p_keyword TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    api_config RECORD;
    auth_header TEXT;
    response JSONB;
BEGIN
    -- API 설정 가져오기
    SELECT api_username, api_password INTO api_config
    FROM brand.echotik_api_config
    ORDER BY created_at DESC
    LIMIT 1;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('error', 'API configuration not found');
    END IF;
    
    -- Basic Auth 헤더 생성
    auth_header := 'Basic ' || encode(
        convert_to(api_config.api_username || ':' || api_config.api_password, 'UTF8'),
        'base64'
    );
    
    -- HTTP 요청 (http extension 사용)
    SELECT content::jsonb INTO response
    FROM http((
        'GET',
        'https://open.echotik.live/api/v2/seller/list?keyword=' || p_keyword || '&region=US&page_num=1&page_size=10',
        ARRAY[http_header('Authorization', auth_header)],
        NULL,
        NULL
    )::http_request);
    
    RETURN response;
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('error', SQLERRM);
END;
$$;

-- 5. Seller Trend API 호출 함수
CREATE OR REPLACE FUNCTION brand.call_echotik_seller_trend_api(
    p_seller_id TEXT,
    p_start_date TEXT,
    p_end_date TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    api_config RECORD;
    auth_header TEXT;
    response JSONB;
    api_url TEXT;
BEGIN
    -- API 설정 가져오기
    SELECT api_username, api_password INTO api_config
    FROM brand.echotik_api_config
    ORDER BY created_at DESC
    LIMIT 1;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('error', 'API configuration not found');
    END IF;
    
    -- Basic Auth 헤더 생성
    auth_header := 'Basic ' || encode(
        convert_to(api_config.api_username || ':' || api_config.api_password, 'UTF8'),
        'base64'
    );
    
    -- API URL 구성
    api_url := 'https://open.echotik.live/api/v2/seller/trend' ||
               '?seller_id=' || p_seller_id ||
               '&start_date=' || p_start_date ||
               '&end_date=' || p_end_date ||
               '&page_num=1&page_size=10';
    
    -- HTTP 요청
    SELECT content::jsonb INTO response
    FROM http((
        'GET',
        api_url,
        ARRAY[http_header('Authorization', auth_header)],
        NULL,
        NULL
    )::http_request);
    
    RETURN response;
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('error', SQLERRM);
END;
$$;

-- 6. Seller ID 자동 수집 함수
CREATE OR REPLACE FUNCTION brand.fetch_seller_ids()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    brand_config RECORD;
    api_response JSONB;
    seller_data JSONB;
    found_seller_id TEXT;
    success_count INTEGER := 0;
    error_count INTEGER := 0;
BEGIN
    RAISE NOTICE '🔍 Seller ID 수집 시작...';
    
    -- 각 브랜드별로 Seller ID 검색
    FOR brand_config IN 
        SELECT brand_name 
        FROM brand.echotik_seller_config 
        WHERE seller_id IS NULL
    LOOP
        BEGIN
            RAISE NOTICE '📊 브랜드 검색 중: %', brand_config.brand_name;
            
            -- Seller List API 호출
            api_response := brand.call_echotik_seller_list_api(brand_config.brand_name);
            
            -- 응답 확인
            IF api_response ? 'error' THEN
                RAISE EXCEPTION 'API Error: %', api_response->>'error';
            END IF;
            
            -- 첫 번째 검색 결과의 seller_id 사용
            IF api_response ? 'data' AND jsonb_array_length(api_response->'data') > 0 THEN
                seller_data := api_response->'data'->0;
                found_seller_id := seller_data->>'seller_id';
                
                -- Seller ID 업데이트
                UPDATE brand.echotik_seller_config 
                SET seller_id = found_seller_id,
                    updated_at = NOW()
                WHERE brand_name = brand_config.brand_name;
                
                RAISE NOTICE '✅ %: seller_id = %', brand_config.brand_name, found_seller_id;
                success_count := success_count + 1;
            ELSE
                RAISE EXCEPTION 'No seller found for brand: %', brand_config.brand_name;
            END IF;
            
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE '❌ 브랜드 % 처리 실패: %', brand_config.brand_name, SQLERRM;
                error_count := error_count + 1;
        END;
    END LOOP;
    
    RETURN format('Seller ID 수집 완료: 성공 %s개, 실패 %s개', success_count, error_count);
END;
$$;

-- 7. 일별 Seller Trend 데이터 수집 함수
CREATE OR REPLACE FUNCTION brand.fetch_seller_trend_data(
    p_start_date TEXT DEFAULT NULL,
    p_end_date TEXT DEFAULT NULL
)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    brand_config RECORD;
    api_response JSONB;
    trend_data JSONB;
    data_item JSONB;
    start_date_str TEXT;
    end_date_str TEXT;
    success_count INTEGER := 0;
    error_count INTEGER := 0;
    total_days INTEGER := 0;
BEGIN
    -- 기본 날짜 설정 (178일전부터 2일전까지)
    start_date_str := COALESCE(p_start_date, (CURRENT_DATE - INTERVAL '178 days')::TEXT);
    end_date_str := COALESCE(p_end_date, (CURRENT_DATE - INTERVAL '2 days')::TEXT);
    
    RAISE NOTICE '🚀 Seller Trend 데이터 수집 시작: % ~ %', start_date_str, end_date_str;
    
    -- 각 브랜드별 데이터 수집
    FOR brand_config IN 
        SELECT brand_name, seller_id 
        FROM brand.echotik_seller_config 
        WHERE seller_id IS NOT NULL
    LOOP
        BEGIN
            RAISE NOTICE '📊 브랜드 처리 중: % (seller_id: %)', brand_config.brand_name, brand_config.seller_id;
            
            -- Seller Trend API 호출
            api_response := brand.call_echotik_seller_trend_api(
                brand_config.seller_id, 
                start_date_str, 
                end_date_str
            );
            
            -- 응답 확인
            IF api_response ? 'error' THEN
                RAISE EXCEPTION 'API Error: %', api_response->>'error';
            END IF;
            
            -- 데이터 처리
            trend_data := api_response->'data';
            
            IF trend_data IS NULL OR jsonb_array_length(trend_data) = 0 THEN
                RAISE NOTICE 'ℹ️ %: 데이터 없음', brand_config.brand_name;
                CONTINUE;
            END IF;
            
            -- 각 일별 데이터 저장
            FOR data_item IN SELECT * FROM jsonb_array_elements(trend_data)
            LOOP
                INSERT INTO brand."echotik-seller-trend-daily" (
                    dt,
                    seller_id,
                    user_id,
                    total_sale_cnt,
                    total_sale_gmv_amt,
                    total_sale_1d_cnt,
                    total_sale_gmv_1d_amt,
                    total_video_cnt,
                    total_live_cnt,
                    total_product_cnt,
                    total_crawl_product_cnt,
                    total_video_ifl_cnt,
                    total_live_ifl_cnt,
                    created_at,
                    updated_at
                ) VALUES (
                    (data_item->>'dt')::DATE,
                    brand_config.seller_id,
                    data_item->>'user_id',
                    COALESCE((data_item->>'total_sale_cnt')::INTEGER, 0),
                    COALESCE((data_item->>'total_sale_gmv_amt')::DECIMAL, 0),
                    COALESCE((data_item->>'total_sale_1d_cnt')::INTEGER, 0),
                    COALESCE((data_item->>'total_sale_gmv_1d_amt')::DECIMAL, 0),
                    COALESCE((data_item->>'total_video_cnt')::INTEGER, 0),
                    COALESCE((data_item->>'total_live_cnt')::INTEGER, 0),
                    COALESCE((data_item->>'total_product_cnt')::INTEGER, 0),
                    COALESCE((data_item->>'total_crawl_product_cnt')::INTEGER, 0),
                    COALESCE((data_item->>'total_video_ifl_cnt')::INTEGER, 0),
                    COALESCE((data_item->>'total_live_ifl_cnt')::INTEGER, 0),
                    NOW(),
                    NOW()
                )
                ON CONFLICT (seller_id, dt) 
                DO UPDATE SET
                    user_id = EXCLUDED.user_id,
                    total_sale_cnt = EXCLUDED.total_sale_cnt,
                    total_sale_gmv_amt = EXCLUDED.total_sale_gmv_amt,
                    total_sale_1d_cnt = EXCLUDED.total_sale_1d_cnt,
                    total_sale_gmv_1d_amt = EXCLUDED.total_sale_gmv_1d_amt,
                    total_video_cnt = EXCLUDED.total_video_cnt,
                    total_live_cnt = EXCLUDED.total_live_cnt,
                    total_product_cnt = EXCLUDED.total_product_cnt,
                    total_crawl_product_cnt = EXCLUDED.total_crawl_product_cnt,
                    total_video_ifl_cnt = EXCLUDED.total_video_ifl_cnt,
                    total_live_ifl_cnt = EXCLUDED.total_live_ifl_cnt,
                    updated_at = NOW();
                
                total_days := total_days + 1;
            END LOOP;
            
            RAISE NOTICE '✅ %: %개 레코드 저장 완료', brand_config.brand_name, jsonb_array_length(trend_data);
            success_count := success_count + 1;
            
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE '❌ 브랜드 % 처리 실패: %', brand_config.brand_name, SQLERRM;
                error_count := error_count + 1;
        END;
    END LOOP;
    
    RETURN format('데이터 수집 완료: 성공 %s개 브랜드, 실패 %s개, 총 %s개 레코드', 
                  success_count, error_count, total_days);
END;
$$;

-- 8. 메인 실행 함수 (초기 설정용)
CREATE OR REPLACE FUNCTION brand.setup_echotik_seller_trend_system()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result_message TEXT;
BEGIN
    RAISE NOTICE '🚀 EchoTik Seller Trend 시스템 초기 설정 시작';
    
    -- 1단계: Seller ID 수집
    RAISE NOTICE '📋 1단계: Seller ID 수집';
    SELECT brand.fetch_seller_ids() INTO result_message;
    RAISE NOTICE '%', result_message;
    
    -- 2단계: 히스토리 데이터 수집 (178일전부터 2일전까지)
    RAISE NOTICE '📋 2단계: 히스토리 데이터 수집';
    SELECT brand.fetch_seller_trend_data() INTO result_message;
    RAISE NOTICE '%', result_message;
    
    RETURN '✅ EchoTik Seller Trend 시스템 초기 설정 완료';
END;
$$;

-- 9. 일별 업데이트 함수 (Cron Job용)
CREATE OR REPLACE FUNCTION brand.daily_seller_trend_update()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    yesterday_str TEXT;
    result_message TEXT;
BEGIN
    -- 어제 날짜 (2일전 데이터 수집)
    yesterday_str := (CURRENT_DATE - INTERVAL '2 days')::TEXT;
    
    RAISE NOTICE '📅 일별 Seller Trend 업데이트: %', yesterday_str;
    
    -- 어제 하루 데이터만 수집
    SELECT brand.fetch_seller_trend_data(yesterday_str, yesterday_str) INTO result_message;
    
    RETURN format('일별 업데이트 완료 (%s): %s', yesterday_str, result_message);
END;
$$;

-- 10. 권한 설정
GRANT EXECUTE ON FUNCTION brand.call_echotik_seller_list_api(TEXT) TO postgres;
GRANT EXECUTE ON FUNCTION brand.call_echotik_seller_trend_api(TEXT, TEXT, TEXT) TO postgres;
GRANT EXECUTE ON FUNCTION brand.fetch_seller_ids() TO postgres;
GRANT EXECUTE ON FUNCTION brand.fetch_seller_trend_data(TEXT, TEXT) TO postgres;
GRANT EXECUTE ON FUNCTION brand.setup_echotik_seller_trend_system() TO postgres;
GRANT EXECUTE ON FUNCTION brand.daily_seller_trend_update() TO postgres;

-- 11. 초기 설정 안내
SELECT '🎯 다음 단계:' as 안내;
SELECT '1. HTTP extension 확인: SELECT * FROM pg_available_extensions WHERE name = ''http'';' as 단계1;
SELECT '2. 초기 설정 실행: SELECT brand.setup_echotik_seller_trend_system();' as 단계2;
SELECT '3. Cron Job 설정: 매일 오전 9시에 SELECT brand.daily_seller_trend_update();' as 단계3; 