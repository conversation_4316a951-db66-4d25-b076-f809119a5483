const { createClient } = require('@supabase/supabase-js')

// Supabase 설정
const supabaseUrl = 'https://tmqhrmpzfgjwqdyiormw.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRtcWhybXB6Zmdqd3FkeWlvcm13Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNzExNDEsImV4cCI6MjA2NTc0NzE0MX0.zNCWt3PbLBqB3esC70HI3PbadAVMBKPBC_5AkxkDP3Q'

const supabase = createClient(supabaseUrl, supabaseKey)

async function verifyData() {
  console.log('📊 Supabase 데이터 확인 중...\n')
  
  try {
    // 총 레코드 수 확인
    const { count, error: countError } = await supabase
      .from('competitor-dashboard')
      .select('*', { count: 'exact', head: true })
    
    if (countError) {
      console.error('❌ 레코드 수 조회 오류:', countError)
      return
    }
    
    console.log(`📈 총 레코드 수: ${count}개`)
    
    // 메트릭별 레코드 수 확인
    const { data: metricData, error: metricError } = await supabase
      .from('competitor-dashboard')
      .select('metric_type')
    
    if (metricError) {
      console.error('❌ 메트릭 데이터 조회 오류:', metricError)
      return
    }
    
    const metricCounts = {}
    metricData.forEach(row => {
      metricCounts[row.metric_type] = (metricCounts[row.metric_type] || 0) + 1
    })
    
    console.log('\n📊 메트릭별 레코드 수:')
    Object.entries(metricCounts).forEach(([metric, count]) => {
      console.log(`  - ${metric}: ${count}개`)
    })
    
    // 브랜드별 레코드 수 확인
    const { data: brandData, error: brandError } = await supabase
      .from('competitor-dashboard')
      .select('brand_name')
    
    if (brandError) {
      console.error('❌ 브랜드 데이터 조회 오류:', brandError)
      return
    }
    
    const brandCounts = {}
    brandData.forEach(row => {
      brandCounts[row.brand_name] = (brandCounts[row.brand_name] || 0) + 1
    })
    
    console.log('\n🏪 브랜드별 레코드 수:')
    Object.entries(brandCounts).forEach(([brand, count]) => {
      console.log(`  - ${brand}: ${count}개`)
    })
    
    // 월별 레코드 수 확인
    const { data: monthData, error: monthError } = await supabase
      .from('competitor-dashboard')
      .select('date_period')
    
    if (monthError) {
      console.error('❌ 월별 데이터 조회 오류:', monthError)
      return
    }
    
    const monthCounts = {}
    monthData.forEach(row => {
      monthCounts[row.date_period] = (monthCounts[row.date_period] || 0) + 1
    })
    
    console.log('\n📅 월별 레코드 수:')
    Object.entries(monthCounts).forEach(([month, count]) => {
      console.log(`  - ${month}: ${count}개`)
    })
    
    // 샘플 데이터 확인
    const { data: sampleData, error: sampleError } = await supabase
      .from('competitor-dashboard')
      .select('*')
      .limit(5)
    
    if (sampleError) {
      console.error('❌ 샘플 데이터 조회 오류:', sampleError)
      return
    }
    
    console.log('\n📋 샘플 데이터 (첫 5개):')
    sampleData.forEach((row, index) => {
      console.log(`${index + 1}. ${row.brand_name} - ${row.metric_type} (${row.date_period}): ${row.value.toLocaleString()}`)
    })
    
    console.log('\n✅ 데이터 확인 완료!')
    
  } catch (error) {
    console.error('❌ 데이터 확인 중 오류:', error)
  }
}

verifyData() 