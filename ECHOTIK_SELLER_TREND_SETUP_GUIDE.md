# 📊 EchoTik Seller Trend API 시스템 설정 가이드

Supabase에서 EchoTik Seller Trend API를 사용하여 매일 자동으로 브랜드별 판매 데이터를 수집하는 시스템을 구축하는 가이드입니다.

## 🎯 **시스템 개요**

- **6개 K-Beauty 브랜드** 일별 트렌드 데이터 자동 수집
- **178일 히스토리 데이터** 초기 로드 (API 제한)
- **매일 오전 9시(UTC)** 자동 업데이트
- **PostgreSQL Cron Job** 기반 스케줄링

## 📋 **수집 브랜드**

1. **Skin1004 US**
2. **medicube US Store**  
3. **Anua Store US**
4. **COSRX US**
5. **Beauty of Joseon US**
6. **Dr.Melaxin**

## 🔧 **전체 설정 순서**

### **1단계: 필수 Extensions 설치**

```sql
-- HTTP extension (API 호출용)
CREATE EXTENSION IF NOT EXISTS http;

-- pg_cron extension (스케줄링용)  
CREATE EXTENSION IF NOT EXISTS pg_cron;
```

**확인:**
```sql
SELECT * FROM pg_extension WHERE extname IN ('http', 'pg_cron');
```

### **2단계: 메인 테이블 생성**

Supabase SQL Editor에서 실행:
```bash
create_echotik_seller_trend_table.sql
```

**생성되는 테이블:**
- `brand."echotik-seller-trend-daily"` - 일별 트렌드 데이터 저장

### **3단계: 시스템 설정 및 함수 생성**

```bash
setup_echotik_seller_trend_system.sql
```

**생성되는 구성요소:**
- `brand.echotik_seller_config` - 브랜드 설정 테이블
- API 호출 함수들
- 데이터 수집 함수들
- Basic Auth 정보 저장

### **4단계: HTTP Extension 테스트**

```bash
test_echotik_http_extension.sql
```

**테스트 항목:**
- Extension 설치 확인
- Basic Auth 헤더 생성
- API 연결 테스트
- 시스템 준비 상태 확인

### **5단계: 초기 데이터 수집**

```sql
-- 브랜드별 Seller ID 자동 수집 + 178일 히스토리 데이터 로드
SELECT brand.setup_echotik_seller_trend_system();
```

**처리 과정:**
1. 각 브랜드명으로 Seller List API 호출
2. 첫 번째 검색 결과의 seller_id 저장
3. 178일전부터 2일전까지의 트렌드 데이터 수집

### **6단계: Cron Job 설정**

```bash
setup_echotik_cron_job.sql
```

**설정 내용:**
- 매일 오전 9시 UTC (한국시간 오후 6시) 실행
- 2일전 데이터 자동 수집
- 로그 기록 및 에러 처리

## 📊 **데이터 구조**

### **테이블: `brand."echotik-seller-trend-daily"`**

| 필드명 | 타입 | 설명 |
|--------|------|------|
| `dt` | DATE | 데이터 날짜 |
| `seller_id` | TEXT | EchoTik 판매자 ID |
| `user_id` | TEXT | TikTok 사용자 ID |
| `total_sale_cnt` | INTEGER | 총 판매 건수 (누적) |
| `total_sale_gmv_amt` | DECIMAL | 총 판매 GMV (누적) |
| `total_sale_1d_cnt` | INTEGER | 일별 판매 건수 |
| `total_sale_gmv_1d_amt` | DECIMAL | 일별 판매 GMV |
| `total_video_cnt` | INTEGER | 총 비디오 수 |
| `total_live_cnt` | INTEGER | 총 라이브 수 |
| `total_product_cnt` | INTEGER | 총 상품 수 |
| `total_crawl_product_cnt` | INTEGER | 크롤링된 상품 수 |
| `total_video_ifl_cnt` | INTEGER | 비디오 관련 인플루언서 수 |
| `total_live_ifl_cnt` | INTEGER | 라이브 관련 인플루언서 수 |

## 🔐 **API 인증 정보**

```
Username: mark_kim
Password: 76a669aeff3d11efad3b5254ac16e20b
```

Basic Auth 방식으로 EchoTik API 인증을 처리합니다.

## 📅 **스케줄링 정보**

- **실행 시간**: 매일 오전 9시 UTC (한국시간 오후 6시)
- **데이터 범위**: 2일전 데이터 (API 딜레이 고려)
- **재시도 로직**: 실패시 로그 기록
- **중복 처리**: UPSERT로 안전한 데이터 갱신

## 🔍 **모니터링 & 관리**

### **시스템 상태 확인**
```sql
SELECT * FROM check_system_readiness();
```

### **Cron Job 로그 확인**
```sql
SELECT * FROM cron.job_run_details 
WHERE command LIKE '%daily_seller_trend_update%' 
ORDER BY start_time DESC;
```

### **최근 수집 데이터 확인**
```sql
SELECT 
    seller_id,
    COUNT(*) as total_records,
    MIN(dt) as earliest_date,
    MAX(dt) as latest_date
FROM brand."echotik-seller-trend-daily"
GROUP BY seller_id
ORDER BY latest_date DESC;
```

### **브랜드별 최신 데이터**
```sql
SELECT 
    config.brand_name,
    trend.dt,
    trend.total_sale_1d_cnt,
    trend.total_sale_gmv_1d_amt,
    trend.total_video_cnt,
    trend.total_live_cnt
FROM brand.echotik_seller_config config
LEFT JOIN brand."echotik-seller-trend-daily" trend 
    ON config.seller_id = trend.seller_id
WHERE trend.dt = (
    SELECT MAX(dt) 
    FROM brand."echotik-seller-trend-daily" t2 
    WHERE t2.seller_id = trend.seller_id
)
ORDER BY config.brand_name;
```

## 🚨 **문제 해결**

### **HTTP Extension 오류**
```sql
-- Extension이 설치되지 않은 경우
CREATE EXTENSION IF NOT EXISTS http;

-- 권한 문제인 경우 Supabase 대시보드에서 확인
```

### **API 호출 실패**
```sql
-- 인증 정보 확인
SELECT * FROM brand.echotik_api_config;

-- 수동 API 테스트
SELECT test_echotik_seller_list();
```

### **Cron Job 실행 안됨**
```sql
-- Cron Job 상태 확인
SELECT * FROM cron.job WHERE jobname = 'echotik-seller-trend-daily';

-- 수동 실행 테스트
SELECT brand.daily_seller_trend_update();
```

### **데이터 누락**
```sql
-- 특정 날짜 수동 수집
SELECT brand.fetch_seller_trend_data('2024-12-01', '2024-12-01');

-- 전체 재수집 (주의: 시간 소요)
SELECT brand.setup_echotik_seller_trend_system();
```

## 🔧 **관리 명령어**

### **Cron Job 관리**
```sql
-- 일시 중지
UPDATE cron.job SET active = false WHERE jobname = 'echotik-seller-trend-daily';

-- 재시작
UPDATE cron.job SET active = true WHERE jobname = 'echotik-seller-trend-daily';

-- 스케줄 변경 (예: 매일 오후 2시)
SELECT cron.alter_job('echotik-seller-trend-daily', schedule => '0 14 * * *');

-- 완전 삭제
SELECT cron.unschedule('echotik-seller-trend-daily');
```

### **데이터 관리**
```sql
-- 특정 기간 데이터 삭제
DELETE FROM brand."echotik-seller-trend-daily" 
WHERE dt BETWEEN '2024-01-01' AND '2024-01-31';

-- 브랜드별 데이터 삭제
DELETE FROM brand."echotik-seller-trend-daily" 
WHERE seller_id = '특정_SELLER_ID';

-- 테이블 초기화 (주의!)
TRUNCATE brand."echotik-seller-trend-daily";
```

## ✅ **설정 완료 체크리스트**

- [ ] HTTP Extension 설치됨
- [ ] pg_cron Extension 설치됨  
- [ ] 메인 테이블 생성됨
- [ ] 시스템 함수들 생성됨
- [ ] API 인증 정보 저장됨
- [ ] 브랜드 설정 완료됨
- [ ] Seller ID 자동 수집됨
- [ ] 히스토리 데이터 로드됨
- [ ] Cron Job 설정됨
- [ ] 첫 번째 자동 실행 확인됨

## 📞 **지원**

시스템 설정 중 문제가 발생하면:
1. 각 단계별 체크리스트 확인
2. 로그 파일 점검
3. 수동 테스트 함수 실행
4. API 연결 상태 확인

---

*🎯 이 시스템으로 6개 K-Beauty 브랜드의 TikTok Shop 성과를 실시간으로 모니터링할 수 있습니다!* 