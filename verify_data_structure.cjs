const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://jqfijmjvfgzgghvjbwxc.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpxZmlqbWp2Zmd6Z2dodmpid3hjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDI1NjUyMDIsImV4cCI6MjAxODE0MTIwMn0.JItd7grxUM0vo_h-UWc6Z7KuGusTbFK0qQPEYQjrUaU'
);

async function verifyDataStructure() {
  try {
    console.log('🔍 Video/Live 데이터 구조 확인');
    
    // Anua Store US 6월 첫 5일 데이터 확인
    const { data, error } = await supabase
      .schema('brand')
      .from('competitor-dashboard-daily')
      .select('*')
      .eq('brand_name', 'Anua Store US')
      .gte('date', '2025-06-01')
      .lte('date', '2025-06-05')
      .order('date', { ascending: true });
    
    if (error) {
      console.error('❌ Error:', error);
      return;
    }
    
    console.log('\n📊 Anua Store US 6월 첫 5일 데이터:');
    console.log('날짜\t\tGMV\t\tSales\t\tVideo\t\tLive');
    console.log('-'.repeat(80));
    
    data.forEach((item, index) => {
      console.log(`${item.date}\t$${item.gmv}\t${item.sales_count}\t\t${item.video_count}\t${item.live_count}`);
      
      if (index > 0) {
        const prevItem = data[index - 1];
        const videoDiff = item.video_count - prevItem.video_count;
        const liveDiff = item.live_count - prevItem.live_count;
        console.log(`  전날 대비:\t\t\t\t\t\t+${videoDiff}\t+${liveDiff}`);
      }
    });
    
    // 전체 6월 데이터로 차액 분석
    const { data: juneData, error: juneError } = await supabase
      .schema('brand')
      .from('competitor-dashboard-daily')
      .select('*')
      .eq('brand_name', 'Anua Store US')
      .gte('date', '2025-06-01')
      .lt('date', '2025-07-01')
      .order('date', { ascending: true });
    
    if (juneError) {
      console.error('❌ June Error:', juneError);
      return;
    }
    
    console.log('\n📈 6월 전체 Video/Live 증분 분석:');
    let totalVideoDiff = 0;
    let totalLiveDiff = 0;
    
    for (let i = 1; i < juneData.length; i++) {
      const videoDiff = juneData[i].video_count - juneData[i-1].video_count;
      const liveDiff = juneData[i].live_count - juneData[i-1].live_count;
      
      if (videoDiff >= 0) totalVideoDiff += videoDiff;
      if (liveDiff >= 0) totalLiveDiff += liveDiff;
    }
    
    console.log(`총 Video 증분: ${totalVideoDiff.toLocaleString()}`);
    console.log(`총 Live 증분: ${totalLiveDiff.toLocaleString()}`);
    console.log(`마지막 날 누적 Video: ${juneData[juneData.length-1].video_count.toLocaleString()}`);
    console.log(`마지막 날 누적 Live: ${juneData[juneData.length-1].live_count.toLocaleString()}`);
    
  } catch (err) {
    console.error('❌ 오류:', err);
  }
}

verifyDataStructure(); 