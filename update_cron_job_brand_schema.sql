-- <PERSON>ron Job 업데이트: brand 스키마 함수 사용

-- 1. 기존 Cron Job 삭제
SELECT cron.unschedule('daily_competitor_data_fetch');

-- 2. 새로운 Cron Job 생성 (brand 스키마 함수 사용)
SELECT cron.schedule(
    'daily_competitor_data_fetch_brand',
    '0 16 * * *',  -- 매일 16:00 UTC (다음날 01:00 KST)
    'SELECT brand.fetch_daily_competitor_data_v2();'
);

-- 3. Cron Job 상태 확인
SELECT 
    jobid,
    jobname,
    schedule,
    command,
    active
FROM cron.job 
WHERE jobname LIKE '%competitor%'
ORDER BY jobid;

-- 4. 테스트 실행
SELECT brand.test_daily_fetch_v2(); 