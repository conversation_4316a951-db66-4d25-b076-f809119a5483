import { makeSdTailwindConfig } from "sd-tailwindcss-transformer"
import StyleDictionary from "style-dictionary"

const sd = new StyleDictionary(
  makeSdTailwindConfig({
    type: "all",
    source: ["./tokens/light/*.json"],
    // source: ["./tokens/dark/*.json"],
    buildPath: "./tokens/light/", // 반환될 위치
    // buildPath: "./tokens/dark/", // 반환될 위치
  })
)

await sd.hasInitialized
await sd.cleanAllPlatforms()
await sd.buildAllPlatforms()
