-- EchoTik API Basic Auth 테스트 스크립트

-- 1. API 설정 확인
SELECT 'API Configuration Check' as test_name;
SELECT 
    id,
    LEFT(api_key, 20) || '...' as api_key_preview,
    base_url,
    created_at
FROM echotik_api_config;

-- 2. Basic Auth 헤더 생성 테스트
SELECT 'Basic Auth Header Test' as test_name;
SELECT 
    'Basic ' || encode('mark_kim:76a669aeff3d11efad3b5254ac16e20b'::bytea, 'base64') as basic_auth_header;

-- 3. 단일 브랜드 API 호출 테스트 (Skin1004)
SELECT 'Single Brand API Test - Skin1004' as test_name;
SELECT call_echotik_seller_trend_api('7495275617887947202', '2025-01-20');

-- 4. 여러 브랜드 테스트
SELECT 'Multiple Brands API Test' as test_name;

-- Skin1004 US
SELECT 
    'Skin1004 US' as brand_name,
    '7495275617887947202' as seller_id,
    call_echotik_seller_trend_api('7495275617887947202', '2025-01-20') as api_response;

-- medicube US Store  
SELECT 
    'medicube US Store' as brand_name,
    '7495514739648989419' as seller_id,
    call_echotik_seller_trend_api('7495514739648989419', '2025-01-20') as api_response;

-- 5. 전체 데이터 수집 함수 테스트
SELECT 'Full Data Collection Test' as test_name;
SELECT test_daily_fetch_v2();

-- 6. 수집된 데이터 확인
SELECT 'Collected Data Verification' as test_name;
SELECT 
    date,
    brand_name,
    gmv,
    sales_count,
    video_count,
    updated_at
FROM "competitor-dashboard-daily"
WHERE date >= CURRENT_DATE - INTERVAL '3 days'
ORDER BY date DESC, brand_name;

-- 7. 로그 확인
SELECT 'Cron Job Logs Check' as test_name;
SELECT * FROM cron_job_logs 
ORDER BY created_at DESC 
LIMIT 5; 