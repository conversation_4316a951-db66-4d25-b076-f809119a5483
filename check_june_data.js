// 브라우저 콘솔에서 실행할 코드
console.log('🔍 6월 일일 데이터 매핑 확인');

// 현재 로드된 데이터 확인
if (window.supabaseData) {
  console.log('📊 전체 데이터 건수:', window.supabaseData.length);
  
  // 6월 데이터만 필터링
  const juneData = window.supabaseData.filter(item => 
    item.date >= '2025-06-01' && item.date < '2025-07-01'
  );
  
  console.log('📅 6월 데이터 건수:', juneData.length);
  
  // 날짜별로 그룹화
  const dailyGrouped = {};
  juneData.forEach(item => {
    if (!dailyGrouped[item.date]) {
      dailyGrouped[item.date] = [];
    }
    dailyGrouped[item.date].push(item);
  });
  
  // 각 날짜별로 출력
  Object.keys(dailyGrouped).sort().forEach(date => {
    console.log(`\n📅 ${date}:`);
    dailyGrouped[date].forEach(item => {
      console.log(`  ${item.brand_name}: GMV=$${item.gmv.toLocaleString()}, Sales=${item.sales_count}, Videos=${item.video_count}`);
    });
  });
} else {
  console.log('❌ 데이터가 window.supabaseData에 없습니다');
}
