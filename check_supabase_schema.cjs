const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://jqfijmjvfgzgghvjbwxc.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpxZmlqbWp2Zmd6Z2dodmpid3hjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDI1NjUyMDIsImV4cCI6MjAxODE0MTIwMn0.JItd7grxUM0vo_h-UWc6Z7KuGusTbFK0qQPEYQjrUaU'
);

async function checkSchema() {
  try {
    console.log('🔍 Supabase competitor-dashboard-daily 테이블 스키마 확인');
    
    // 첫 번째 레코드 가져와서 컬럼 확인
    const { data, error } = await supabase
      .schema('brand')
      .from('competitor-dashboard-daily')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Error:', error);
      return;
    }
    
    if (data && data.length > 0) {
      console.log('\n📋 테이블 컬럼 목록:');
      const columns = Object.keys(data[0]);
      columns.forEach(col => {
        console.log(`  - ${col}: ${typeof data[0][col]} (예시: ${data[0][col]})`);
      });
      
      console.log('\n🎯 Views/Creators 관련 컬럼 존재 여부:');
      console.log(`  creators 컬럼: ${columns.includes('creators') ? '✅ 존재' : '❌ 없음'}`);
      console.log(`  views 컬럼: ${columns.includes('views') ? '✅ 존재' : '❌ 없음'}`);
      console.log(`  view_count 컬럼: ${columns.includes('view_count') ? '✅ 존재' : '❌ 없음'}`);
      console.log(`  creator_count 컬럼: ${columns.includes('creator_count') ? '✅ 존재' : '❌ 없음'}`);
      
    } else {
      console.log('❌ 데이터 없음');
    }
    
  } catch (err) {
    console.error('❌ 오류:', err);
  }
}

checkSchema(); 