// 브라우저 콘솔에서 실행 (복사해서 붙여넣으세요)
console.log('🔍 6월 데이터 매핑 확인 시작');

// 현재 페이지에 있는 Supabase 클라이언트 사용
const checkJuneMapping = async () => {
  try {
    // 브라우저에서 Supabase 모듈 가져오기
    const { createClient } = await import('https://cdn.skypack.dev/@supabase/supabase-js');
    
    const supabase = createClient(
      'https://jqfijmjvfgzgghvjbwxc.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpxZmlqbWp2Zmd6Z2dodmpid3hjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDI1NjUyMDIsImV4cCI6MjAxODE0MTIwMn0.JItd7grxUM0vo_h-UWc6Z7KuGusTbFK0qQPEYQjrUaU'
    );

    // 6월 데이터 조회
    const { data, error } = await supabase
      .schema('brand')
      .from('competitor-dashboard-daily')
      .select('*')
      .gte('date', '2025-06-01')
      .lt('date', '2025-07-01')
      .order('date', { ascending: true });

    if (error) {
      console.error('❌ Supabase Error:', error);
      return;
    }

    console.log('📊 6월 총 데이터 건수:', data.length);

    // 첫 5일 데이터 출력
    const dailyData = {};
    data.forEach(item => {
      if (!dailyData[item.date]) {
        dailyData[item.date] = [];
      }
      dailyData[item.date].push(item);
    });

    console.log('\n📅 6월 첫 5일 데이터:');
    Object.keys(dailyData).sort().slice(0, 5).forEach(date => {
      console.log(`\n📅 ${date}:`);
      dailyData[date].forEach(item => {
        console.log(`  ${item.brand_name}: GMV=$${item.gmv.toLocaleString()}, Sales=${item.sales_count}, Videos=${item.video_count}`);
      });
    });

    // 6월 전체 월별 집계
    console.log('\n📈 6월 월별 GMV 집계:');
    const monthlyGMV = {};
    data.forEach(item => {
      if (!monthlyGMV[item.brand_name]) {
        monthlyGMV[item.brand_name] = 0;
      }
      monthlyGMV[item.brand_name] += item.gmv;
    });

    Object.keys(monthlyGMV).forEach(brand => {
      console.log(`${brand}: $${Math.ceil(monthlyGMV[brand]).toLocaleString()}`);
    });

    // 차트 데이터 매핑 확인
    console.log('\n🎯 차트 데이터 매핑 확인:');
    console.log('- 일일 데이터가 월별로 집계되어 차트에 표시됩니다');
    console.log('- 현재 페이지에서 보는 6월 데이터는 위 집계 결과입니다');

  } catch (err) {
    console.error('❌ 예상치 못한 오류:', err);
  }
};

// 실행
checkJuneMapping(); 