-- competitor-dashboard 테이블 생성
CREATE TABLE IF NOT EXISTS "competitor-dashboard" (
  id SERIAL PRIMARY KEY,
  metric_type VARCHAR(50) NOT NULL,
  date_period VARCHAR(20) NOT NULL,
  month_key VARCHAR(10) NOT NULL,
  brand_name VARCHAR(100) NOT NULL,
  value BIGINT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 인덱스 생성 (쿼리 성능 향상)
CREATE INDEX IF NOT EXISTS idx_competitor_dashboard_metric ON "competitor-dashboard"(metric_type);
CREATE INDEX IF NOT EXISTS idx_competitor_dashboard_date ON "competitor-dashboard"(date_period);
CREATE INDEX IF NOT EXISTS idx_competitor_dashboard_brand ON "competitor-dashboard"(brand_name);
CREATE INDEX IF NOT EXISTS idx_competitor_dashboard_month ON "competitor-dashboard"(month_key);

-- 복합 인덱스 (대시보드 쿼리 최적화)
CREATE INDEX IF NOT EXISTS idx_competitor_dashboard_metric_brand ON "competitor-dashboard"(metric_type, brand_name);
CREATE INDEX IF NOT EXISTS idx_competitor_dashboard_metric_date ON "competitor-dashboard"(metric_type, date_period);

-- 테이블 설명 추가
COMMENT ON TABLE "competitor-dashboard" IS 'K-beauty 브랜드 경쟁사 분석 데이터 - EchoTik API 기반';
COMMENT ON COLUMN "competitor-dashboard".metric_type IS '메트릭 타입: GMV, Item solds, Creators, Video, Live, Views';
COMMENT ON COLUMN "competitor-dashboard".date_period IS '표시용 날짜: Dec 2024, Jan 2025, etc.';
COMMENT ON COLUMN "competitor-dashboard".month_key IS '정렬용 월 키: 2024-12, 2025-01, etc.';
COMMENT ON COLUMN "competitor-dashboard".brand_name IS '브랜드명: Skin1004 US, medicube US Store, etc.';
COMMENT ON COLUMN "competitor-dashboard".value IS '메트릭 값 (GMV는 달러, Item solds는 개수 등)'; 