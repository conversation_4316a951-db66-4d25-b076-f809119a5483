const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://jqfijmjvfgzgghvjbwxc.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpxZmlqbWp2Zmd6Z2dodmpid3hjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDI1NjUyMDIsImV4cCI6MjAxODE0MTIwMn0.JItd7grxUM0vo_h-UWc6Z7KuGusTbFK0qQPEYQjrUaU'
);

async function getCurrentSellers() {
  try {
    console.log('🔍 현재 Supabase에 있는 Seller ID 목록 확인');
    
    // 고유한 seller_id와 brand_name 조합 가져오기
    const { data, error } = await supabase
      .schema('brand')
      .from('competitor-dashboard-daily')
      .select('seller_id, brand_name')
      .order('brand_name');
    
    if (error) {
      console.error('❌ Error:', error);
      return;
    }
    
    // 중복 제거
    const uniqueSellers = [...new Map(data.map(item => [item.seller_id, item])).values()];
    
    console.log('\n📋 현재 등록된 Seller 목록:');
    console.log('브랜드명\t\t\tSeller ID');
    console.log('-'.repeat(80));
    
    uniqueSellers.forEach(seller => {
      console.log(`${seller.brand_name.padEnd(25)}\t${seller.seller_id}`);
    });
    
    console.log(`\n총 ${uniqueSellers.length}개 Seller ID 발견`);
    
    // 최신 데이터 날짜 확인
    const { data: latestData, error: latestError } = await supabase
      .schema('brand')
      .from('competitor-dashboard-daily')
      .select('date')
      .order('date', { ascending: false })
      .limit(1);
    
    if (latestError) {
      console.error('❌ Latest date error:', latestError);
    } else if (latestData && latestData.length > 0) {
      console.log(`\n📅 가장 최신 데이터 날짜: ${latestData[0].date}`);
    }
    
    return uniqueSellers;
    
  } catch (err) {
    console.error('❌ 오류:', err);
  }
}

getCurrentSellers(); 