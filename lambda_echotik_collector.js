/**
 * AWS Lambda Function: EchoTik Seller Trend Data Collector
 * 매일 새벽 1시에 실행되어 EchoTik API에서 Seller Trend 데이터를 수집하고 Supabase에 저장
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase 클라이언트 설정
const supabase = createClient(
  process.env.SUPABASE_URL || 'https://jqfijmjvfgzgghvjbwxc.supabase.co',
  process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpxZmlqbWp2Zmd6Z2dodmpid3hjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDI1NjUyMDIsImV4cCI6MjAxODE0MTIwMn0.JItd7grxUM0vo_h-UWc6Z7KuGusTbFK0qQPEYQjrUaU'
);

// EchoTik API 설정
const ECHOTIK_CONFIG = {
  baseUrl: 'https://open.echotik.live/api/v2',
  username: 'mark_kim',
  password: '76a669aeff3d11efad3b5254ac16e20b',
  maxDays: 180 // API 최대 제한
};

// 현재 등록된 Seller ID 목록
const SELLER_IDS = [
  { seller_id: '7495467833010457057', brand_name: 'Anua Store US' },
  { seller_id: '7495838346099132849', brand_name: 'Beauty of Joseon US' },
  { seller_id: '7495173442985953451', brand_name: 'COSRX US' },
  { seller_id: '7495830785034323995', brand_name: 'Dr.Melaxin Global' },
  { seller_id: '7495275617887947202', brand_name: 'Skin1004 US' },
  { seller_id: '7495514739648989419', brand_name: 'medicube US Store' }
];

/**
 * 날짜를 YYYY-MM-DD 형식으로 포맷
 */
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

/**
 * 날짜 범위 계산 (178일 전부터 2일 전까지)
 */
function calculateDateRange() {
  const today = new Date();
  const twoDaysAgo = new Date(today);
  twoDaysAgo.setDate(today.getDate() - 2);
  
  const startDate = new Date(today);
  startDate.setDate(today.getDate() - 178);
  
  return {
    start_date: formatDate(startDate),
    end_date: formatDate(twoDaysAgo)
  };
}

/**
 * Seller의 최신 데이터 날짜 확인
 */
async function getLatestDataDate(sellerId) {
  try {
    const { data, error } = await supabase
      .schema('brand')
      .from('echotik_seller_trend_daily')
      .select('date')
      .eq('seller_id', sellerId)
      .order('date', { ascending: false })
      .limit(1);
    
    if (error) {
      console.error(`❌ Error getting latest date for ${sellerId}:`, error);
      return null;
    }
    
    return data && data.length > 0 ? data[0].date : null;
  } catch (err) {
    console.error(`❌ Exception getting latest date for ${sellerId}:`, err);
    return null;
  }
}

/**
 * EchoTik API 호출
 */
async function fetchEchoTikData(sellerId, startDate, endDate) {
  const url = `${ECHOTIK_CONFIG.baseUrl}/seller/trend`;
  const params = new URLSearchParams({
    seller_id: sellerId,
    start_date: startDate,
    end_date: endDate,
    page_num: 1,
    page_size: 10
  });
  
  const auth = Buffer.from(`${ECHOTIK_CONFIG.username}:${ECHOTIK_CONFIG.password}`).toString('base64');
  
  try {
    console.log(`📡 EchoTik API 호출: ${sellerId} (${startDate} ~ ${endDate})`);
    
    const response = await fetch(`${url}?${params}`, {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    if (result.code !== 0) {
      throw new Error(`API Error: ${result.message || 'Unknown error'}`);
    }
    
    console.log(`✅ API 응답 성공: ${result.data?.length || 0}건의 데이터`);
    return result.data || [];
    
  } catch (err) {
    console.error(`❌ EchoTik API 호출 실패 (${sellerId}):`, err.message);
    throw err;
  }
}

/**
 * Supabase에 데이터 저장 (UPSERT)
 */
async function saveToSupabase(sellerId, brandName, apiData) {
  if (!apiData || apiData.length === 0) {
    console.log(`ℹ️  ${brandName}: 저장할 데이터 없음`);
    return { success: 0, skipped: 0, errors: 0 };
  }
  
  const stats = { success: 0, skipped: 0, errors: 0 };
  
  for (const item of apiData) {
    try {
      // 데이터 변환
      const recordData = {
        seller_id: sellerId,
        date: item.date,
        total_sale_cnt: item.total_sale_cnt || 0,
        total_sale_gmv_amt: item.total_sale_gmv_amt || 0,
        total_video_cnt: item.total_video_cnt || 0,
        total_live_cnt: item.total_live_cnt || 0,
        total_video_sale_cnt: item.total_video_sale_cnt || 0,
        total_video_sale_gmv_amt: item.total_video_sale_gmv_amt || 0,
        total_ifl_cnt: item.total_ifl_cnt || 0,
        updated_at: new Date().toISOString()
      };
      
      // UPSERT (중복시 업데이트)
      const { error } = await supabase
        .schema('brand')
        .from('echotik_seller_trend_daily')
        .upsert(recordData, {
          onConflict: 'seller_id,date'
        });
      
      if (error) {
        console.error(`❌ 저장 실패 (${sellerId}, ${item.date}):`, error);
        stats.errors++;
      } else {
        stats.success++;
      }
      
    } catch (err) {
      console.error(`❌ 데이터 처리 실패 (${sellerId}, ${item.date}):`, err);
      stats.errors++;
    }
  }
  
  console.log(`📊 ${brandName} 저장 결과: 성공 ${stats.success}건, 오류 ${stats.errors}건`);
  return stats;
}

/**
 * 개별 Seller 데이터 수집
 */
async function collectSellerData(seller) {
  const { seller_id, brand_name } = seller;
  
  try {
    console.log(`\n🔄 ${brand_name} (${seller_id}) 데이터 수집 시작`);
    
    // 1. 최신 데이터 날짜 확인
    const latestDate = await getLatestDataDate(seller_id);
    console.log(`📅 ${brand_name} 최신 데이터: ${latestDate || '없음'}`);
    
    // 2. 날짜 범위 계산
    const dateRange = calculateDateRange();
    
    // 3. 이미 최신이면 스킵 (선택적)
    if (latestDate && latestDate >= dateRange.end_date) {
      console.log(`⏭️  ${brand_name}: 이미 최신 데이터 (${latestDate})`);
      return { seller_id, brand_name, status: 'skipped', reason: 'already_latest' };
    }
    
    // 4. EchoTik API 호출
    const apiData = await fetchEchoTikData(seller_id, dateRange.start_date, dateRange.end_date);
    
    // 5. Supabase에 저장
    const saveStats = await saveToSupabase(seller_id, brand_name, apiData);
    
    return {
      seller_id,
      brand_name,
      status: 'completed',
      date_range: dateRange,
      api_records: apiData.length,
      save_stats: saveStats
    };
    
  } catch (err) {
    console.error(`❌ ${brand_name} 수집 실패:`, err.message);
    return {
      seller_id,
      brand_name,
      status: 'failed',
      error: err.message
    };
  }
}

/**
 * Lambda 핸들러 함수
 */
exports.handler = async (event, context) => {
  console.log('🚀 EchoTik Seller Trend 데이터 수집 시작');
  console.log('📅 실행 시간:', new Date().toISOString());
  
  const results = [];
  let totalSuccess = 0;
  let totalErrors = 0;
  
  try {
    // 각 Seller에 대해 순차적으로 데이터 수집
    for (const seller of SELLER_IDS) {
      const result = await collectSellerData(seller);
      results.push(result);
      
      if (result.status === 'completed' && result.save_stats) {
        totalSuccess += result.save_stats.success;
        totalErrors += result.save_stats.errors;
      }
      
      // API 호출 제한을 위한 딜레이 (선택적)
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 최종 결과
    const summary = {
      timestamp: new Date().toISOString(),
      total_sellers: SELLER_IDS.length,
      completed: results.filter(r => r.status === 'completed').length,
      failed: results.filter(r => r.status === 'failed').length,
      skipped: results.filter(r => r.status === 'skipped').length,
      total_records_saved: totalSuccess,
      total_errors: totalErrors,
      results: results
    };
    
    console.log('\n📋 최종 수집 결과:');
    console.log(`  처리된 Seller: ${summary.total_sellers}개`);
    console.log(`  성공: ${summary.completed}개`);
    console.log(`  실패: ${summary.failed}개`);
    console.log(`  스킵: ${summary.skipped}개`);
    console.log(`  저장된 레코드: ${summary.total_records_saved}건`);
    console.log(`  오류: ${summary.total_errors}건`);
    
    return {
      statusCode: 200,
      body: JSON.stringify(summary, null, 2)
    };
    
  } catch (err) {
    console.error('❌ Lambda 실행 실패:', err);
    
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: err.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};

// 로컬 테스트용 (Lambda 환경이 아닐 때)
if (require.main === module) {
  exports.handler({}, {}).then(result => {
    console.log('\n🏁 로컬 테스트 완료');
    console.log(JSON.stringify(result, null, 2));
  }).catch(err => {
    console.error('❌ 로컬 테스트 실패:', err);
  });
} 