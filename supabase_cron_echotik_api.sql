-- 실제 EchoTik API 호출을 위한 개선된 Cron Job 함수
-- HTTP extension 사용

-- 1. HTTP extension 활성화 (Supabase에서 필요시 활성화)
CREATE EXTENSION IF NOT EXISTS http;

-- 2. EchoTik API 설정을 위한 테이블 생성
CREATE TABLE IF NOT EXISTS echotik_api_config (
    id SERIAL PRIMARY KEY,
    api_key TEXT NOT NULL,
    base_url TEXT NOT NULL DEFAULT 'https://api.echotik.com',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. API 설정 초기 데이터 (Basic Auth 정보)
INSERT INTO echotik_api_config (api_key, base_url) 
VALUES ('mark_kim:76a669aeff3d11efad3b5254ac16e20b', 'https://api.echotik.com')
ON CONFLICT (id) DO NOTHING;

-- 4. 실제 EchoTik API 호출 함수 (Basic Auth 사용)
CREATE OR REPLACE FUNCTION call_echotik_seller_trend_api(
    seller_id TEXT,
    target_date TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    api_config RECORD;
    api_url TEXT;
    request_body JSONB;
    response RECORD;
    api_response JSONB;
    basic_auth_header TEXT;
BEGIN
    -- API 설정 가져오기
    SELECT api_key, base_url INTO api_config 
    FROM echotik_api_config 
    ORDER BY id DESC 
    LIMIT 1;
    
    IF api_config.api_key IS NULL THEN
        RAISE EXCEPTION 'EchoTik API credentials not configured';
    END IF;
    
    -- 기본 날짜 설정 (어제)
    IF target_date IS NULL THEN
        target_date := (CURRENT_DATE - INTERVAL '1 day')::TEXT;
    END IF;
    
    -- API URL 구성
    api_url := api_config.base_url || '/v1/seller/trend';
    
    -- Basic Auth 헤더 생성 (username:password를 base64 인코딩)
    basic_auth_header := 'Basic ' || encode(api_config.api_key::bytea, 'base64');
    
    -- 요청 본문 구성
    request_body := jsonb_build_object(
        'seller_id', seller_id,
        'date_from', target_date,
        'date_to', target_date,
        'granularity', 'daily'
    );
    
    RAISE NOTICE '🔗 [%] Calling EchoTik API: % for seller % on date %', 
        NOW()::TEXT, api_url, seller_id, target_date;
    
    -- HTTP POST 요청 (Basic Auth 사용)
    SELECT * INTO response
    FROM http((
        'POST',
        api_url,
        ARRAY[
            http_header('Content-Type', 'application/json'),
            http_header('Authorization', basic_auth_header),
            http_header('User-Agent', 'Supabase-Cron/1.0'),
            http_header('Accept', 'application/json')
        ],
        'application/json',
        request_body::TEXT
    ));
    
    RAISE NOTICE '📡 [%] API Response: HTTP % - %', 
        NOW()::TEXT, response.status, LEFT(response.content, 200);
    
    -- 응답 확인
    IF response.status < 200 OR response.status >= 300 THEN
        RAISE EXCEPTION 'EchoTik API error: HTTP % - %', response.status, response.content;
    END IF;
    
    -- JSON 파싱
    BEGIN
        api_response := response.content::JSONB;
    EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to parse API response as JSON: %', response.content;
    END;
    
    RETURN api_response;
END;
$$;

-- 5. 개선된 일일 데이터 수집 함수
CREATE OR REPLACE FUNCTION fetch_daily_competitor_data_v2()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    brand_info RECORD;
    api_response JSONB;
    data_item JSONB;
    current_date_str TEXT;
    success_count INTEGER := 0;
    error_count INTEGER := 0;
BEGIN
    -- 현재 날짜 (어제 데이터를 가져옴)
    current_date_str := (CURRENT_DATE - INTERVAL '1 day')::TEXT;
    
    -- 로그 시작
    RAISE NOTICE '🚀 [%] Daily competitor data fetch started for date: %', 
        NOW()::TEXT, current_date_str;
    
    -- 브랜드별 데이터 수집
    FOR brand_info IN 
        SELECT 
            'Skin1004 US' as brand_name, 
            '7495275617887947202' as seller_id
        UNION ALL SELECT 
            'medicube US Store', 
            '7495514739648989419'
        UNION ALL SELECT 
            'COSRX US', 
            '7495173442985953451'
        UNION ALL SELECT 
            'Dr.Melaxin Global', 
            '7495830785034323995'
        UNION ALL SELECT 
            'Anua Store US', 
            '7495467833010457057'
        UNION ALL SELECT 
            'Beauty of Joseon US', 
            '7495838346099132849'
    LOOP
        BEGIN
            RAISE NOTICE '📊 [%] Processing brand: % (seller_id: %)', 
                NOW()::TEXT, brand_info.brand_name, brand_info.seller_id;
            
            -- EchoTik API 호출
            api_response := call_echotik_seller_trend_api(
                brand_info.seller_id, 
                current_date_str
            );
            
            -- API 응답에서 데이터 추출
            IF api_response ? 'data' AND jsonb_array_length(api_response->'data') > 0 THEN
                -- 첫 번째 데이터 항목 사용
                data_item := api_response->'data'->0;
                
                -- 기존 데이터 확인 및 UPSERT
                INSERT INTO "competitor-dashboard-daily" (
                    date,
                    brand_name,
                    seller_id,
                    gmv,
                    sales_count,
                    video_count,
                    live_count,
                    product_count,
                    total_gmv,
                    total_sales,
                    created_at,
                    updated_at
                ) VALUES (
                    current_date_str::DATE,
                    brand_info.brand_name,
                    brand_info.seller_id,
                    COALESCE((data_item->>'total_sale_gmv_1d_amt')::DECIMAL, 0),
                    COALESCE((data_item->>'total_sale_1d_cnt')::INTEGER, 0),
                    COALESCE((data_item->>'total_video_cnt')::INTEGER, 0),
                    COALESCE((data_item->>'total_live_cnt')::INTEGER, 0),
                    COALESCE((data_item->>'total_product_cnt')::INTEGER, 0),
                    COALESCE((data_item->>'total_sale_gmv_amt')::DECIMAL, 0),
                    COALESCE((data_item->>'total_sale_cnt')::INTEGER, 0),
                    NOW(),
                    NOW()
                )
                ON CONFLICT (date, brand_name) 
                DO UPDATE SET
                    gmv = EXCLUDED.gmv,
                    sales_count = EXCLUDED.sales_count,
                    video_count = EXCLUDED.video_count,
                    live_count = EXCLUDED.live_count,
                    product_count = EXCLUDED.product_count,
                    total_gmv = EXCLUDED.total_gmv,
                    total_sales = EXCLUDED.total_sales,
                    updated_at = NOW();
                
                success_count := success_count + 1;
                RAISE NOTICE '✅ [%] Successfully processed %: GMV $%, Sales %', 
                    NOW()::TEXT, 
                    brand_info.brand_name,
                    COALESCE((data_item->>'total_sale_gmv_1d_amt')::DECIMAL, 0),
                    COALESCE((data_item->>'total_sale_1d_cnt')::INTEGER, 0);
            ELSE
                RAISE NOTICE '⚠️ [%] No data returned for %', NOW()::TEXT, brand_info.brand_name;
                error_count := error_count + 1;
            END IF;
            
        EXCEPTION WHEN OTHERS THEN
            error_count := error_count + 1;
            RAISE NOTICE '❌ [%] Error processing %: %', 
                NOW()::TEXT, brand_info.brand_name, SQLERRM;
        END;
        
        -- API 호출 간 잠시 대기 (Rate limiting 방지)
        PERFORM pg_sleep(1);
    END LOOP;
    
    -- 최종 결과 로그
    RAISE NOTICE '🎉 [%] Daily competitor data fetch completed - Success: %, Errors: %', 
        NOW()::TEXT, success_count, error_count;
        
    -- 결과를 로그 테이블에 저장 (선택사항)
    INSERT INTO cron_job_logs (
        job_name,
        execution_date,
        success_count,
        error_count,
        details
    ) VALUES (
        'daily-competitor-data-fetch',
        current_date_str::DATE,
        success_count,
        error_count,
        format('Processed %s brands for date %s', success_count + error_count, current_date_str)
    );
END;
$$;

-- 6. Cron Job 로그 테이블 생성
CREATE TABLE IF NOT EXISTS cron_job_logs (
    id SERIAL PRIMARY KEY,
    job_name TEXT NOT NULL,
    execution_date DATE NOT NULL,
    success_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    details TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. 기존 Cron Job 제거 후 새로운 것으로 교체
SELECT cron.unschedule('daily-competitor-data-fetch');

-- 8. 새로운 Cron Job 스케줄 설정
SELECT cron.schedule(
    'daily-competitor-data-fetch-v2',
    '0 16 * * *',  -- 매일 16:00 UTC (다음날 01:00 KST)
    'SELECT fetch_daily_competitor_data_v2();'
);

-- 9. 테스트 함수 업데이트
CREATE OR REPLACE FUNCTION test_daily_fetch_v2()
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
    PERFORM fetch_daily_competitor_data_v2();
    RETURN '✅ Test completed. Check logs and data for details.';
END;
$$;

-- 10. 최근 Cron Job 실행 결과 확인 함수
CREATE OR REPLACE FUNCTION check_recent_cron_results(days INTEGER DEFAULT 7)
RETURNS TABLE(
    execution_date DATE,
    success_count INTEGER,
    error_count INTEGER,
    details TEXT,
    created_at TIMESTAMPTZ
)
LANGUAGE SQL
AS $$
    SELECT 
        execution_date,
        success_count,
        error_count,
        details,
        created_at
    FROM cron_job_logs 
    WHERE job_name = 'daily-competitor-data-fetch'
      AND execution_date >= CURRENT_DATE - INTERVAL '%d days'
    ORDER BY execution_date DESC;
$$ LANGUAGE SQL; 