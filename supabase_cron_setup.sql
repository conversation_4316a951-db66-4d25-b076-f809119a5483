-- Supa<PERSON> Cron Jobs 설정
-- 매일 01:00 AM (KST)에 EchoTik API 데이터 업데이트

-- 1. pg_cron extension 활성화 (Supabase에서는 기본 활성화됨)
-- CREATE EXTENSION IF NOT EXISTS pg_cron;

-- 2. EchoTik API 호출을 위한 함수 생성
CREATE OR REPLACE FUNCTION fetch_daily_competitor_data()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    brand_info RECORD;
    api_response TEXT;
    daily_data JSONB;
    item JSONB;
    current_date_str TEXT;
BEGIN
    -- 현재 날짜 (어제 데이터를 가져옴)
    current_date_str := (CURRENT_DATE - INTERVAL '1 day')::TEXT;
    
    -- 로그 시작
    RAISE NOTICE '🚀 Daily competitor data fetch started for date: %', current_date_str;
    
    -- 브랜드별 데이터 수집
    FOR brand_info IN 
        SELECT 
            'Skin1004 US' as brand_name, 
            '7495275617887947202' as seller_id
        UNION ALL SELECT 
            'medicube US Store', 
            '7495514739648989419'
        UNION ALL SELECT 
            'COSRX US', 
            '7495173442985953451'
        UNION ALL SELECT 
            'Dr.Melaxin Global', 
            '7495830785034323995'
        UNION ALL SELECT 
            'Anua Store US', 
            '7495467833010457057'
        UNION ALL SELECT 
            'Beauty of Joseon US', 
            '7495838346099132849'
    LOOP
        BEGIN
            -- EchoTik API 호출 (HTTP extension 사용)
            -- 실제 구현에서는 http extension을 사용하여 API 호출
            -- 여기서는 더미 데이터로 대체 (실제로는 API 호출 코드 필요)
            
            RAISE NOTICE '📊 Processing brand: %', brand_info.brand_name;
            
            -- 기존 데이터가 있는지 확인
            IF EXISTS (
                SELECT 1 FROM "competitor-dashboard-daily" 
                WHERE date = current_date_str::DATE 
                AND brand_name = brand_info.brand_name
            ) THEN
                -- 업데이트
                UPDATE "competitor-dashboard-daily"
                SET 
                    updated_at = NOW()
                WHERE date = current_date_str::DATE 
                AND brand_name = brand_info.brand_name;
                
                RAISE NOTICE '✅ Updated existing data for %', brand_info.brand_name;
            ELSE
                -- 새 데이터 삽입 (실제로는 API에서 받은 데이터 사용)
                INSERT INTO "competitor-dashboard-daily" (
                    date,
                    brand_name,
                    seller_id,
                    gmv,
                    sales_count,
                    video_count,
                    live_count,
                    product_count,
                    total_gmv,
                    total_sales
                ) VALUES (
                    current_date_str::DATE,
                    brand_info.brand_name,
                    brand_info.seller_id,
                    0, -- API에서 받을 실제 값
                    0, -- API에서 받을 실제 값
                    0, -- API에서 받을 실제 값
                    0, -- API에서 받을 실제 값
                    0, -- API에서 받을 실제 값
                    0, -- API에서 받을 실제 값
                    0  -- API에서 받을 실제 값
                );
                
                RAISE NOTICE '✅ Inserted new data for %', brand_info.brand_name;
            END IF;
            
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE '❌ Error processing %: %', brand_info.brand_name, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE '🎉 Daily competitor data fetch completed';
END;
$$;

-- 3. HTTP extension을 사용한 실제 API 호출 함수 (고급 버전)
CREATE OR REPLACE FUNCTION fetch_echotik_api_data(seller_id TEXT, target_date TEXT)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    api_url TEXT;
    api_headers JSONB;
    api_response JSONB;
BEGIN
    -- EchoTik API URL 구성
    api_url := 'https://api.echotik.com/v1/seller/trend';
    
    -- API 헤더 설정
    api_headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer YOUR_ECHOTIK_API_KEY'
    );
    
    -- HTTP 요청 (http extension 필요)
    -- SELECT content::JSONB INTO api_response
    -- FROM http((
    --     'POST',
    --     api_url,
    --     api_headers,
    --     'application/json',
    --     jsonb_build_object(
    --         'seller_id', seller_id,
    --         'date', target_date
    --     )::TEXT
    -- ));
    
    -- 임시로 더미 데이터 반환 (실제로는 위의 HTTP 호출 결과 사용)
    api_response := jsonb_build_object(
        'data', jsonb_build_array(
            jsonb_build_object(
                'dt', target_date,
                'total_sale_gmv_1d_amt', '1000.00',
                'total_sale_1d_cnt', '10',
                'total_video_cnt', '100',
                'total_live_cnt', '5',
                'total_product_cnt', '50'
            )
        )
    );
    
    RETURN api_response;
END;
$$;

-- 4. Cron Job 스케줄 설정 (매일 01:00 AM KST = 16:00 UTC 전날)
-- KST는 UTC+9이므로, KST 01:00 = UTC 16:00 (전날)
SELECT cron.schedule(
    'daily-competitor-data-fetch',
    '0 16 * * *',  -- 매일 16:00 UTC (다음날 01:00 KST)
    'SELECT fetch_daily_competitor_data();'
);

-- 5. Cron Job 상태 확인 함수
CREATE OR REPLACE FUNCTION check_cron_jobs()
RETURNS TABLE(
    jobid BIGINT,
    schedule TEXT,
    command TEXT,
    nodename TEXT,
    nodeport INTEGER,
    database TEXT,
    username TEXT,
    active BOOLEAN,
    jobname TEXT
)
LANGUAGE SQL
AS $$
    SELECT * FROM cron.job;
$$;

-- 6. 수동 테스트 함수
CREATE OR REPLACE FUNCTION test_daily_fetch()
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
    PERFORM fetch_daily_competitor_data();
    RETURN '✅ Test completed. Check logs for details.';
END;
$$;

-- 7. Cron Job 로그 확인 함수
CREATE OR REPLACE FUNCTION check_cron_logs(job_name TEXT DEFAULT 'daily-competitor-data-fetch')
RETURNS TABLE(
    jobid BIGINT,
    runid BIGINT,
    job_pid INTEGER,
    database TEXT,
    username TEXT,
    command TEXT,
    status TEXT,
    return_message TEXT,
    start_time TIMESTAMPTZ,
    end_time TIMESTAMPTZ
)
LANGUAGE SQL
AS $$
    SELECT * FROM cron.job_run_details 
    WHERE command LIKE '%fetch_daily_competitor_data%'
    ORDER BY start_time DESC
    LIMIT 10;
$$;

-- 사용법 주석:
-- 1. 이 SQL을 Supabase SQL Editor에서 실행
-- 2. EchoTik API 키를 실제 값으로 교체
-- 3. HTTP extension 활성화: CREATE EXTENSION IF NOT EXISTS http;
-- 4. 테스트: SELECT test_daily_fetch();
-- 5. Cron 상태 확인: SELECT * FROM check_cron_jobs();
-- 6. 로그 확인: SELECT * FROM check_cron_logs(); 